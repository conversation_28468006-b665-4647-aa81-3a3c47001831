# Changelog

<!--
    IMPORTANT: all upcoming changes must be listed here in order to be able to anticipate and mitigate rate failures
    during upgrades.
-->

Items listed here describe completed work that is ready to be shipped with the next version release.

## v3.16
- CRTX-198587: Added access to additional datasets for xSPM Scanner
- CRTX-157188: Add infrastructure for DMS/Pipeline/XQL HPA adhoc tweaks
- CRTX-176830: Finding pipeline spec and HPA improvements
- CRTX-184035: Add VSG regarding OOM scaling for ca collection pods
- CRTX-188800: Fix HPA for fsi and eai workers
- BCE-48847: Added a new environment variable for secrets chunks size and a new field to the configmap
- CRTX-186877: Add analytics installed content version metric to whitelist
- BCE-51733: Added repository_urgency_runtime_metrics_collector job image
- CRTX-188068: Add uvem-netscan-controller
- CRTX-192511: startupProbe for Verdict Manager, CLoudsec api, Rules, InlineScanner
- CRTX-168949: Enable External metrics caching
- BCE-51066: change repo workflow in cas argo workflows
- CRTX-189912 - ciem whitelist gauge for AccountMonitoringService (and add role for new ciem service)
- CRTX-185737: Reconfigure ca collection resources
- CRTX-193298 - New ST is platform metrics allowlisting
- CRTX-191334: Reconfigure HPA for fsi worker
- CRTX-191691 - Adjusting resources for cloud asset collection services
- CRTX-195363 - fix netscan controller image issues not being updated in tenants-config
- CRTX-191691 - Adjusting resources for cloud asset collection services
- CRTX-183635: Add new preprocessed_data_bucket cron-job + bucket + service account
- BCE-52044: Combine repo steps in cicd repo enrichment wf
- CRTX-192297: Add New metric to asset pipeline
- CRTX-194737: DevOps-Add new itdr bucket latency metric
- CRTX-192277: Add VSG regarding OOM scaling for analytics-profiles-orchestrator pods
- CRTX-188372: Request Metric whitelistings custom metrics for xdr-st-api service in BE3
- CRTX-186256: Whitelist prometheus metrics for StandardEvents


## v3.15
- CRTX-191200: Add GCS Bucket ReadAndWrite role to dp_finding_emits bucket for uvem_vxp_api
- CRTX-172207: Added subscriber binding for uvem-vxp-api on dp-findings-revisions-sub subscription
- CRTX-168759: Added BC_DISTRIBUTOR_BASE_URL to ready-job-ecvaluator
- CRTX-166508: Added dp-asset-association pipeline and support pub/sub
- CRTX-168210: Remove the cloudsec cronjobs risk-prioritization and blast-radius
- DIGX-7411: Added dspm-worker deployment
- CRTX-169423: Adding auto-remediation tool "healops"
- BCE-48082: Adding helm charts for CAS DB migrations
- CRTX-173578: Add a no-restart ConfigMap volume mount to DP apps
- CRTX-174577: Add dp-uai-assets-migration pipeline and support pub/sub
- CRTX-175637: Additional pubsub config for email-artifacts-relay
- CRTX-176674: Reverted VSG content sync feature
- CRTX-176584: Added VSG configuration for analytics-alerts-emitter
- DIG-7435: Add dspm-das deployment
- CRTX-175641: Additional metrics whitelisted for email-artifacts-relay
- CRTX-140260: Removing applicationhub_enabled ff
- CRTX-179200: Add agentix hub pod for new agentix product
- CRTX-174748: Added VulnerabilityNetworkScanningConf default values
- CRTX-175229: Add MCP pod
- CRTX-145773 - upgrade ST prometheus to v3.0
- CRTX-178491: whitelist CIEM timer job metrics
- CRTX-177961: Request metric whitelistings custom metrics for cloudsec apiservice
- DIGX-8370: Add new subscription to dspm-voyager topic
- CRTX-177908: Whitelisted 5 metrics and added 7 recording rules for tenant
- CRTX-178453: Metrics for ciem LPA
- CRTX-181411: Whitelist metrics for CIEM - LAP
- CRTX-182964: Whitelist email_relay_scheduler_task_executions_total
- CRTX-174353: Added VSG secdo init roadblock support
- CRTX-173675: Support the new XSOAR code workers
- CRTX-182865: Metrics for LAP
- CRTX-178564: Whitelist Adv Agent Compensating Controls Metrics
- CRTX-178794: Whitelist protofindings producer Metrics
- CRTX-167687: Api enricher whitelist and rules
- CRTX-184201: Whitelist new metrics for Asset Association
- CRTX-169821: Whitelist DSPM Mac Metrics
- CRTX-186610 - Enable Contextual Search on all new platform tenants
- CRTX-188439: Whitelisted metric for cas issues upserts
- CRTX-181291: Whitelist new metric Association replication
- CRTX-188374: Whitelist-new-metrics-for-xql-inflight
- CRTX-189823: Attack path scanner cron job to run once a day

## v3.14

- CRTX-150904: Add dataset authorizations needed for public access views to work with platform datasets.
- CRTX-154504: Added DACS_INTERNAL_URL env var to crespo-worker and delete permissions to dspm_crespo_payload bucket
- DIGX-6155: fix certificate errors and warning in init containers
- DIGX-5988: Add health logs topic ID and permissions for dspm crespo-worker and midfielder
- EXPANDR-12377: Enable ASN details for all tenant types
- CRTX-154560: Added pubsub topic dss-sync-notifier topic
- CRTX-154560: Added publisher role for dss-sync-notifier topic
- CRTX-158769: Added itdr-data-pipeline-dss-sync subscription to dss-sync-notifier topic
- CRTX-159825: Update scanner to version 2.4.4 , update Runner version to 0.1.14
- DIGX-6360: Add sp-workload-orchestration url as env variable to datasource pods
- CRTX-159240: Added image-analyzer-cron-job
- EXPANDR-12213: Grant task-processor workload cloud trace agent role
- CRTX-150340: Add Storybuilder operator awareness of configmap-dml-feature-flags
- DIGX-6710: add env variable for asset error pubsub
- CRTX-140185: Added VSG configmaps, statefulset support, metrics, VSG configurations for Rocksdb and Analytics Detection Engine.
- CRTX-166228: HPA config changes for Fetcher service to address pub/sub slow processing issue for xpanse tenants.
- BCE-47231: Fixed handling of empty `customer-modules` input in the CAS branch scan workflow
- CRTX-166228: HPA config changes for Fetcher service to address pub/sub slow processing issue for xpanse tenants.
- EXPANDR-12919: Grant fedramp api workload cloud trace agent role
- CRTX-174767: Adjust the startup probe for the RMS to avoid deployment issues
- CRTX-175661: Run analytics-de-cron-job and enable analytics pickling only for XSIAM tenants.

## v3.13

- CRTX-156969: Add xsoar-job-pod service account for running XSOAR jobs in a separate pod.
- CRTX-149331: Enable xsoar request duration by config
- CRTX-140291: Adding cloud api service configurations
- CRTX-137577: Add rule management bq access
- DIGX-3950: Add onboarding Snowflake private and public keys
- CRTX-137846: Add continuous profiling for xsoar-init, content pod, xsoar-api and enable profiling in dev env
- CRTX-118505: Adding missing api host in content pod conf
- DIGX-2868: Add DSPM Genie, resource-digger, resource-cleaner and worker
- CRTX-133998: Add vsg support for archive aggregator cron job
- DIGX-4577: Add env variable to the main profile, add Big Query permissions for the main pod
- CRTX-131517: Allow Cronus operator delete pods
- CRTX-149764: Update image lookup for apisec-spec-service
- CRTX-150173: Filter asm product in asm error pub/sub subscriptions
- CRTX-147978: Add blast radius and risk prioritization service
- CRTX-151052: Change port for DSPM Genie to 443
- CRTX-151052: Added JOSE_INTERNAL_URL env var to Genie
- CRTX-157473: reduce cwp/dspm deployments min replicas
- CRTX-159825: scanner version 1.0.12 , runner version 0.0.27

## v3.12

- CRTX-133180: Set GKE Control Plane to 1.30
- DIGX-2757: Add DSPM FDA
- DIGX-2778: Add DSPM FDA Listeners
- DIGX-2794: Add DSPM CB
- CRTX-122085: Add new "xsoar-api" deployment with HPA
- CRTX-127467: Update API Monitoring flag for XSIAM Tenants
- CRTX-121931: Adding VPC Service Perimeter to Prod ID Region and to Metro Tenants
- CRTX-137586: Adding condition to use diff mysql version images based on env
- CRTX-126411: Add VSG support for cron jobs
- CRTX-137790: Enable GKE backup for certain applications in prod-pr.

## v3.11

- CRTX-111992: Add Prisma Access gateway to global-master-autorized-networks
- CRTX-112567: change additional_dynamic_nodepool node type to e2-standard-32
- CRTX-109570: Remove mysql password from xql-engine deployment
- CRTX-113414: Add chat-api also in XDR tenants
- CRTX-109583: Bulk Messages Processing- pb-runners-v2
- CRTX-112906: New Cronus-metrics workload
- CRTX-113266: support-internal new bucket
- CRTX-117414: new service account for running external tools (viso-remote-executor)
- CRTX-113815: xql-engine delete permissions on ext-logs
- CRTX-115539: startupProbe for engine-hub and xsoar
- CRTX-117678: Mount DML FF CM to Cronus
- CRTX-110154: Saas collection autoscale
- CRTX-109523: Add gmp to google group
- CRTX-117741: Add CM key XpanseThreatEventsConf_bq_sync_enabled
- CRTX-113168: Add ConfigMap to cold storage
- CRTX-106907: Add hourly mysql snapshots + reorder the snapshot code also keeping it backward compatible
- CRTX-119262: Disabled kubelet and containerd health monitor on GKE nodes to prevent stuck pods
- CRTX-101726: Pipeline throttling integration
- CRTX-117899: Rocksdb backups bucket
- CRTX-104724: Removed debug subscriptions and reduced message retention duration for QA/Internal
- CRTX-121601: Increased RocksDB PVC size for metro tenants
- CRTX-123067: Modify GV xpanse bucket permissions to new SA
- CRTX-120334: Add AWS outpost module and module call
- CRTX-120334: Add AWS outpost cwp agentless and iam module and module call

## v3.10

- CRTX-118967: Upgrade GKE Control Plane to 1.28
- CRTX-105034: Replace wf metric from request_success to request_failed
- CRTX-98787: Add messages-processor pub/sub publisher permissions to celery-worker
- CRTX-109709: Add recording rules (pz_schema_manager)
- CRTX-111893: Removed ops-nanny completely to avoid conflicts with VSG
- CRTX-106537: Add prometheus monitoring support for metro resident tenants
- CRTX-106433: Gcr remove from new tenants
- CRTX-116389: Add log-forwarding image tag override
- CRTX-117754: Added viso-playbook-executor SA in xdr-mt namespace for metro host project
- CRTX-104724: Removed debug subscriptions and modified message retention duration for QA/Internal

## v3.9

- CRTX-95859: Replacing multiple uses of is_perf_tenant and prod_spec to a single prod_spec variable
- CRTX-95195: adding async-export-files bucket
- CRTX-97043: Add cloudprofiler.agent role to frontend
- CRTX-95084: Add missing components to the effect of the prod_spec flag
- CRTX-96404 - add egress forward vsg resource
- CRTX-97188 - Modifidaction to vsg resources
  - add status.properties.downscaledByUser to CRD
  - add validating webhook - deployment
  - enable vsg by default
  - remove pubsub metrics from dms/xql-engine hpa
- CRTX-98169
  - change vsg egress-forwarding config
- CRTX-100212
  - create new tenants with gke 1.27
- CRTX-99994
  - change pb runner strategy_type from Recreate to RollingUpdate
- CRTX-97427
  - add key to config map, change bucket life cycle to 30 days
- CRTX-100858
  - Whitleist metric xdr_init_app_took
- CRTX-102497
  - add rocksdb cluster mode
- CRTX-104714
  - new service account for custom viso playbook executor
- CRTX-105405
  - Added BQ metrics to be exported via stackdriver-exporter on XSOAR, XSIAM and XPANSE tenants
- CRTX-106904
  - Enabled daily snapshots for all redis. Default enabled only for Main Redis, Analytics Redis and IPL Asset Redis.
- CRTX-105390
  - Enable installing of Google Managed Prometheus on ST
- CRTX-109709
  - Add recording rules to GnzGlobal
- CRTX-109166
  - Add new innodb_buffer_poolsize configuration for xpanse tenants
- CRTX-112105
  - Overwrite StackDriver metrics project_id label to the GMP

## v3.8

- CRTX-87886
  - Remove unused node ports from Frontend and Prometheus k8s services
- CRTX-89594: Calculate Redis `maxmemory` argument value dynamically based on memory limit.
- CRTX-89148 - establish connection between chat-api and Copilot multi tenant service:
  - open new firewall port (11115 listen in proxy-vm) for copilot MT service.
  - chat-api deployment and service.
  - service prefix added to no_proxy key in config map.
- CRTX-90146 - Copilot BQ project name and location keys added to config map.
- CRTX-89818 - Cortex and Prisma Unified Agent:
  - bucket <project_id>-vulnerability-and-compliance-scans
  - service account prisma-console@<project_id>.iam.gserviceaccount.com
  - allow api pod to create key for service account prisma-console@<project_id>.iam.gserviceaccount.com
  - topic vulnerability-and-compliance-scans-<lcaas_id>
  - two subscription vulnerability-and-compliance-scans and vulnerability-and-compliance-scans-debug
  - four new keys to main config map
- CRTX-93886: 3rd-party component version upgrades
- CRTX-76030:
  - Added Ops-nanny 0.0.5 to TF and is disabled by default
  - Ops-nanny 0.0.5 supports OOM Handler and Scale to zero
- CRTX-95879
  - add BQ dataset permissions to the analytics service account
- CRTX-96426
  - Update GKE control plane and node pools to V1.25
- CRTX-96205:
  - increas xsoar engine pod mem limit/request for prod_specs
- CRTX-96911:
  - add XSOAR migration banners config for XSOAR 8 tenants
- CRTX-96596
  - Added support for mysql daily snapshot schedule with 30 days rentention and disabled by default. Can be enabled or disabled via overrides.
  - Added override for mysql backup cron job. It will be enabled on Prod and Dev Perf Tenants and disabled on other Dev by default.
  - Runbook <https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+Troubleshoot+MySQL#RunbookTroubleshootMySQL-EnableorDisableMySQLSnapshotSchedules>
- CRTX-97706
  - Add xsoar metrics xsoar_completed_inv_playbook_total and xsoar_inv_playbook_ack_nack_messages_total
- CRTX-96169
  - Created new topic/sub for event-forwarding flow (event-forwarding-external-lcaas)
  - Created new GCS notification to route external prefix to new topic (event-forwarding-external-lcaas)
  - Added new GCS notification to route internal prefix to old topic (event-forwarding-lcaas)
- CRTX-96596
  - Enabled mysql snapshot schedule and disabled mysql cron jobs by default.
- CRTX-98944
  - Modify Fedramp (Mod and High) default ST GCP Project folders
  - Removed conditional override for xsiam application hub and xsoar shared engine projects pointing XDR Fedramp High Engines 689569380641 to always check the folder_id_map TF Var where it will be pointing Cortex XDR IL4 Shared Engines 905638983349 folder for prod-gv
- CRTX-99996
  - Application Hub - add observability bucket & pubsub
- CRTX-100705
  - Populate config bq stats project id to xsoar config
- CRTX-88178
  - Added xpanse_aum_count to handle MYSQL resource provisioning for XPANSE tenants
- CRTX-99996 - Application Hub
  - add Octopus operator
  - move from Ingress to Gateway API
- CRTX-85579 - Ship Saving Costs for XPANSE
  - Removes pipeline from xpanse tenants and it's dependencies
  - Removes xcloud standalone deployment from production XPANSE tenants
CRTX-104973 - remove taint from dynamic node pool
- CRTX-105091 - Add FF for xpanse tenants
  - MitreMappingsConf_enable_asm_alert_mitre_mappings was added to the main configmap for xpanse tenants
- CRTX-106255  - Added Bucket IAM Binding for XSOAR POD Service account
- CRTX-105874 - Removing secdo-init logs from the exclusion filter
- CRTX-112505 - Enabling GSR for Fedramp moderate tenants
- CRTX-125653 - Added CNA Cronjob
- CRTX-130582 - Enabling sidecar images for ca-collection fsi & eai worker services
  - CRTX-132567 - adding unique labels_overide for ca-collection fsi & eai worker services
- CRTX-132352 - Add subscriber permission to GCP PubSub for CWP Subsctiptions that using DQL
- CRTX-137845: Add action plan API service & 2 cron jobs to cloudsec.tf
- CRTX-40704 - Add topic and subscriber for cas webhooks
- CRTX-143615 - Add cloudsec batch-scanner cron job
- CRTX-144715 - Add cloudsec dashboard-api service
- CRTX-150121 - Add bigquery permissions to cwp-pc-health-tracker service
- CRTX-154938 - Update xql-engine ,pipeline & dms max hpa to 85%
- CRTX-153245 - sp-scan-runner version updated to v0.1.13
- CRTX-153245 - sp-scan-runner version updated to v0.1.17
- CRTX-161726 - sp-scan-runner version updated to v0.1.18
- CRTX-164977 - Whitelist CIEM account manager metrics
- BCE-47372 - Whitelist CAS general HTTP & job metrics
- BCE-46626 - Whitelist CAS dashboards-api metrics + recording rule for failures
- CRTX-167019 - Whitelist CIEM health manager metrics
- BCE-47372 - Whitelist CAS general HTTP & job metrics
- CRTX-164744 - Add recording rules for CIEM account manager
- CRTX-170490 - Whitelist gauge for CIEM EPCVlaidationService
- CRTX-172081 - Add env variables to CIEM account-manager
- CRTX-176812 - Add ACCOUNT_MANAGER_RATE_MS to CIEM account manager deployment
- CRTX-175884 - Whitelist CIEM metric for EPC validation and account manager
- CRTX-175884 - Whitelist ciem_gauge_epc_validation_time_passed_since_last_snapshot CIEM metric
- CRTX-17617 - Add pubsub viewer role for CIEM account manager
- CRTX-47724 - Add CORTEX_PLATFORM_URL to application-api service env var
- CRTX-173535 - scanlog permission bucket access dp-scan-log for scanners
- CRTX-173193 - Update CWP rules cron job env var configuration
- CRTX-182962 - Add pubsub publisher role to bq_stats topic for uvem_vxp_api
- CRTX-192511: startupProbe for Verdict Manager, CLoudsec api, Rules, InlineScanner- CRTX-188532 - sp-scan-runner version updated to v0.3.1
- CRTX-182666 - Add lcaas id to metrus env
- CRTX-169794 - add prometheous to association-replication-cron-job
- CRTX-195528 - Add Asset Groups change topic
- CRTX-198145 - Add permissions to Asset Groups change topic
