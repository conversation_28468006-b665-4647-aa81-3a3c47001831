Closes <-- <PERSON><PERSON> ID -->
 
/label "prometheus"
/label "metrics"
/assign me
/assign_reviewer @igajera @estar @tnalam @rnatour @sdawood
   
 
#### Details about Metric Whitelistings in this MR:
 
- <--metric_name_a-->: <--Slack thread link for Cardinality Evaluation Bot Run which covers this metric-->
- <--metric_name_b-->: <--Slack thread link for Cardinality Evaluation Bot Run which covers this metric-->
 
  
#### Checklist (please check each before moving MR away from draft for review):
  
- [ ] I've reviewed my own code
- [ ] I've reviewed all Pipelines of this MR are passing
- [ ] I've attached slack cardinality evaluation bot links for each metric I am whitelisting
- [ ] I've verified the whitelisted metrics are detected by the slack bot links I have shared above
- [ ] I've have added recording rules for all histogram metrics I am adding
- [ ] I've blacklisted all raw metrics which I have used in recording rules (except for stackdriver_.+ metrics)
- [ ] I've reviewed checklist for Metric names, Label names and Label values: https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Metrics+best+practice
- [ ] I've reviewed checklist for Metric Whitelistings, Recording Rules and Blacklist: https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Cortex+Metric+Whitelisting+Checklist
