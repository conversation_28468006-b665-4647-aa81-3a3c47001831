variable "consul_datacenter" {}
variable "consul_host" {}
variable "consul_token" {}
variable "cronus_node_count" {
  type    = number
  default = 1
}
variable "host_project" {}
variable "host_project_subnetwork_name" {}
variable "metro_id" {}
variable "multi_project_postfix" {}
variable "project_id" {}
variable "project_prefix" {}
variable "region" {}
variable "overrides" { default = {} }
variable "viso_env" {}
variable "xdr_env" {}
variable "creation_date" {}
variable "network_policy" {
  type    = bool
  default = true
}
variable "zone" {}
variable "enable_gke_metering" {
  type    = bool
  default = false
}
variable "enable_network_egress_metering" {
  type    = bool
  default = true
}
variable "enable_resource_consumption_metering" {
  type    = bool
  default = true
}
variable "enable_custom_kube_dns" {
  type    = bool
  default = true
}

variable "viso_image_tag" {
  type        = string
  description = "viso image tag for version"
}

locals {
  is_fedramp                   = var.viso_env == "prod-fr" || var.viso_env == "prod-gv"
  tf_root_path                 = abspath("${path.module}/../../")
  tenant_files_path            = "${local.tf_root_path}/tenant/02_resources_and_permissions/files"
  gke_version                  = "1-33"
  default_instance_type        = "n1-standard-4"
  node_locations               = []
  project_prefix_hyphened      = "${var.project_prefix}-"
  proxy_service_account_email  = module.create_iam.service_account_email["proxy-sa"]
  router_service_account_email = module.create_iam.service_account_email["router-sa"]
  gcp_groups_domain            = local.is_fedramp ? "fedramp-panw.com" : "paloaltonetworks.com"
  autoscaling_profile          = "OPTIMIZE_UTILIZATION"
  enable_custom_kube_dns       = lookup(var.overrides, "enable_custom_kube_dns", var.enable_custom_kube_dns)
  group_env                    = var.viso_env == "dev" ? var.viso_env : replace(var.viso_env, "prod-", "")
  router_sa_google_group       = var.viso_env == "prod-fr" ? "<EMAIL>" : var.viso_env == "prod-gv" ? "<EMAIL>" : "gcplocal-xdr-router-${local.group_env}@paloaltonetworks.com"
  viso_version                 = replace(regex("v(\\d+.\\d+)", var.viso_image_tag)[0], ".", "")
}