module "create_tools_apps" {
  source               = "../../modules/app"
  for_each = {
    for app, app_value in local.tools_apps :
    app => app_value
    if lookup(lookup(app_value, "deployment", {}), "enabled", true)
  }
  app_name             = each.value.app_name
  cron_job             = lookup(each.value, "cron_job", {})
  deployment           = lookup(each.value, "deployment", {})
  only_cron            = lookup(each.value, "only_cron", false)
  hpa                  = lookup(each.value, "hpa", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns["xdr-st"])
  pool_tenant_creation = var.pool_tenant_creation
  service              = lookup(each.value, "service", {})
  service_account_name = each.value.service_account_name
  project_id           = var.project_id
  region               = var.region
  secrets              = lookup(each.value, "secrets", {})
}

module "create_daemonsets" {
  source               = "../../modules/daemonset"
  for_each             = {
  for k, v in local.daemonsets:
        k => v
        if lookup(v, "enabled", true)
  }
  app_name             = each.value.app_name
  daemonset           = lookup(each.value, "daemonset", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns["xdr-st"])
  service_account_name = lookup(each.value, "service_account_name", null)
}

resource "kubernetes_secret" "xdr-daemon-secret" {
  count = local.enable_xdr_agent_daemon ? 1 : 0
  metadata {
    name      = "cortex-docker-secret"
    namespace = module.create_namespace.ns["xdr-agent-daemon"]
  }
  data = {
    ".dockerconfigjson" = base64decode(data.google_secret_manager_secret_version.xdr_daemon_dockersecret.secret_data)
  }
  type = "kubernetes.io/dockerconfigjson"
}

locals {
  tools_apps = {
    custom-metrics-stackdriver-adapter = {
      app_name             = "custom-metrics-stackdriver-adapter"
      namespace            = module.create_namespace.ns["custom-metrics"]
      service_account_name = "custom-metrics-stackdriver-adapter"
      deployment = {
        template_namespace = null
        host_network = true
        toleration = [{
          operator = "Exists"
        }]
        labels_override = {
          run     = "custom-metrics-stackdriver-adapter"
          k8s-app = "custom-metrics-stackdriver-adapter"
        }
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          name            = "pod-custom-metrics-stackdriver-adapter"
          image           = "${var.docker_image_gar_location}/golden-images/custom-metrics-stackdriver-adapter:${local.custom_metrics_stackdriver_adapter_version}"
          command         = concat(["/adapter", "--use-new-resource-model=true"], var.enable_external_metrics_cache ? ["--external-metric-cache-ttl=1m"] : [])
          requests = {
            cpu    = "50m"
            memory = "50Mi"
          }
          limits = {
            cpu      = "250m"
            memory   = "200Mi"
          }
        }]
      }
      service = {
        spec = {
          name      = "custom-metrics-stackdriver-adapter"
          namespace = module.create_namespace.ns["custom-metrics"]
          labels = {
            run                             = "custom-metrics-stackdriver-adapter"
            k8s-app                         = "custom-metrics-stackdriver-adapter"
            "kubernetes.io/cluster-service" = "true"
            "kubernetes.io/name"            = "Adapter"
          }
          selector = {
            run     = "custom-metrics-stackdriver-adapter"
            k8s-app = "custom-metrics-stackdriver-adapter"
          }
          port = [{
            port        = 443
            target_port = 443
            protocol    = "TCP"
          }]
        }
      }
    }
    kube_state_metrics = {
      app_name             = "kube-state-metrics-${var.lcaas}"
      namespace            = "kube-system"
      service_account_name = "kube-state-metrics"
      deployment = {
        labels_override                 = { k8s-app = "kube-state-metrics" }
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          name            = "kube-state-metrics"
          image           = "${var.docker_image_gar_location}/golden-images/kube-state-metrics:${local.kube_state_metrics_version}"
          ports           = [8080, 8081]
          limits = {
            cpu      = "300m"
            memory   = "320Mi"
          }
          requests = {
            cpu    = "150m"
            memory = "50Mi"
          }
          readiness_probe = [{
            http_get = [{
              path = "/healthz"
              port = "8080"
            }]
            initial_delay_seconds = "5"
            timeout_seconds       = "5"
          }]
          },
          {
            name            = "addon-resizer"
            image           = "${var.docker_image_gar_location}/golden-images/addon-resizer:1.8.3"
            limits = {
              cpu      = "150m"
              memory   = "50Mi"
            }
            requests = {
              cpu    = "150m"
              memory = "50Mi"
            }
            command = [
              "/pod_nanny",
              "--container=kube-state-metrics",
              "--cpu=100m",
              "--extra-cpu=1m",
              "--memory=100Mi",
              "--extra-memory=2Mi",
              "--threshold=5",
            "--deployment=kube-state-metrics-${var.lcaas}"]

            fields = [{
              name       = "MY_POD_NAME"
              field_path = "metadata.name"
              }, {
              name       = "MY_POD_NAMESPACE"
              field_path = "metadata.namespace"
            }]
        }]
      }
      service = {
        spec = {
          name = "kube-state-metrics-${var.lcaas}"
          annotations = {
            "prometheus.io/scrape" = "true"
          }
          labels = {
            k8s-app = "kube-state-metrics"
          }
          selector = {
            k8s-app = "kube-state-metrics"
          }
          port = [{
            name        = "http-metrics"
            port        = 8080
            target_port = 8080
            protocol    = "TCP"
            }, {
            name        = "telemetry"
            port        = 8081
            target_port = 8081
            protocol    = "TCP"
          }]
        }
      }
    }
    prometheus = {
      app_name             = "monitoring-${var.lcaas}-prometheus"
      namespace            = module.create_namespace.ns["monitoring"]
      service_account_name = "prometheus"
      deployment = {
        enabled                         = ! var.pool_tenant_creation
        metadata_annotations = {
          "configmap.reloader.stakater.com/reload" = "prometheus-server-conf"
        }
        strategy_type                   = "Recreate"
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          image = "${var.docker_image_gar_location}/golden-images/prom-tools/prometheus:${local.prometheus_image_tag}"
          name  = "prometheus"
          ports = [9090]
          volume_mount = [{
            mount_path = "/etc/prometheus/"
            name       = "prometheus-config-volume"
          }]
          requests = {
            cpu    = var.tenant_type != "internal" ? "0.3" : "0.1"
            memory = "700Mi"
          }
          limits = {
            cpu      = "1"
            memory   = "1Gi"
          }
        }]
        volume_config_map = [{
          name         = "prometheus-config-volume"
          cm_name      = "prometheus-server-conf"
          default_mode = "0777"
        }]
      }
      service = {
        spec = {
          name = "monitoring-${var.lcaas}-prometheus"
          annotations = {
            "networking.gke.io/load-balancer-type" = "Internal"
          }
          port = [{
            port        = 8080
            protocol    = "TCP"
            target_port = "9090"
          }]
          load_balancer_ip = var.xdr_prometheus_loadbalancer_ip
          type             = "LoadBalancer"
        }
      }
    }
    stackdriver_exporter_pod = {
      app_name             = "stackdriver-exporter-${var.lcaas}"
      namespace            = module.create_namespace.ns["monitoring"]
      service_account_name = "prometheus"
      deployment = {
        strategy_type = "Recreate"
        annotations = {
          "prometheus.io/scrape" = "true"
        }
        labels_override = {
          app = "stackdriver-exporter"
        }
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          image = "${var.docker_image_gar_location}/golden-images/stackdriver-exporter:${local.stackdriver_exporter_version}"
          name  = "stackdriver-exporter"
          ports = [9255]
          args  = sort([
            "--google.project-id=${var.project_id}",
            "--monitoring.metrics-type-prefixes=${local.stackdriver_metrics_list}",
            "--monitoring.metrics-interval=1m",
            "--monitoring.metrics-offset=10m"
          ])
          requests = {
            cpu    = "50m"
            memory = "50Mi"
          }
          limits = {
            cpu      = "100m"
            memory   = "100Mi"
          }
        }]
      }
    }
    reloader = {
      app_name             = "reloader"
      service_account_name = "reloader"
      deployment = {
        automount_service_account_token = true
        containers = [{
          name              = "reloader"
          image             = "${var.docker_image_gar_location}/golden-images/stakater/reloader:v1.2.0"
          args  = [
            "--reload-strategy=annotations",
          ]
          image_pull_policy = "IfNotPresent"
          requests = {
            cpu      = "10m"
            memory   = "128Mi"
          }
          limits = {
            cpu        = "0.1"
            memory     = "512Mi"
          }
        }]
      }
    }
    kube_dns = {
      app_name             = "kube-dns-${var.lcaas}"
      namespace            = "kube-system"
      service_account_name = "kube-dns-${var.lcaas}"
      deployment = {
        enabled                         = local.enable_custom_kube_dns
        metadata_annotations            = {
          "components.gke.io/component-name"           = "kubedns"
          "prometheus.io/port"                         = "10054"
          "prometheus.io/scrape"                       = "true"
          "scheduler.alpha.kubernetes.io/critical-pod" = ""
          "seccomp.security.alpha.kubernetes.io/pod"   = "runtime/default"
        }
        labels_override                 = { k8s-app = "kube-dns" }
        node_selector                   = { "kubernetes.io/os" = "linux" }
        strategy_type                   = "RollingUpdate"
        strategy_max_surge              = "10%"
        strategy_max_unavailable        = "0"
        template_namespace              = null
        priority_class_name             = "system-cluster-critical"
        restart_policy                  = "Always"
        automount_service_account_token = true
        toleration                      = [{operator = "Exists", key = "CriticalAddonsOnly"},
                                           {operator = "Exists", key = "components.gke.io/gke-managed-components"},
                                           {operator = "Equal", effect = "NoSchedule", key = "kubernetes.io/arch", value = "arm64"}]
        containers = [{
          name              = "kubedns"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_image}"
          image_pull_policy = "IfNotPresent"
          security_context  = true
          privileged        = false
          run_as_group      = 1001
          run_as_user       = 1001
          allow_privilege_escalation      = false
          read_only_root_filesystem       = true
          named_ports = [{
            container_port = "10053"
            name           = "dns-local"
            protocol       = "UDP"
          },
          {
            container_port = "10053"
            name           = "dns-tcp-local"
            protocol       = "TCP"
          },
          {
            container_port = "10055"
            name           = "metrics"
            protocol       = "TCP"
          }]
          args = [
            "--domain=cluster.local.",
            "--dns-port=10053",
            "--config-dir=/kube-dns-config",
            "--v=2",
          ]
          volume_mount = [{
            mount_path = "/kube-dns-config"
            name       = "kube-dns-config"
          }]
          env = [{
            name  = "PROMETHEUS_PORT"
            value = "10055"
          }]
          limits = {
            memory   = "210Mi"
          }
          requests = {
            cpu    = "50m"
            memory = "70Mi"
          }
          readiness_probe = [{
            http_get = [{
              path = "/readiness"
              port = "8081"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 3
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 3
          }]
          liveness_probe = [{
            http_get = [{
              path = "/healthcheck/kubedns"
              port = "10054"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 60
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 5
          }]
        },
        {
          name              = "dnsmasq"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_dnsmasq_image}"
          image_pull_policy = "IfNotPresent"
          named_ports = [{
            container_port = "53"
            name           = "dns"
            protocol       = "UDP"
          },
          {
            container_port = "53"
            name           = "dns-tcp"
            protocol       = "TCP"
          }]
          args = [
            "-v=2",
            "-logtostderr",
            "-configDir=/etc/k8s/dns/dnsmasq-nanny",
            "-restartDnsmasq=true",
            "--",
            "-k",
            "--cache-size=1000",
            "--no-negcache",
            "--dns-forward-max=1500",
            "--log-facility=-",
            "--server=/cluster.local/127.0.0.1#10053",
            "--server=/in-addr.arpa/127.0.0.1#10053",
            "--server=/ip6.arpa/127.0.0.1#10053",
            "--max-ttl=30",
            "--max-cache-ttl=30",
          ]
          volume_mount = [{
            mount_path = "/etc/k8s/dns/dnsmasq-nanny"
            name       = "kube-dns-config"
          }]
          requests = {
            cpu    = "50m"
            memory = "20Mi"
          }
          security_context = true
          privileged       = false
          capabilities     = true
          drop = [ "all" ]
          add = [
            "NET_BIND_SERVICE",
            "SETGID"
          ]
          liveness_probe = [{
            http_get = [{
              path = "/healthcheck/dnsmasq"
              port = "10054"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 60
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 5
          }]
        },
        {
          name              = "sidecar"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_sidecar_image}"
          image_pull_policy = "IfNotPresent"
          security_context  = true
          privileged        = false
          run_as_group      = 1001
          run_as_user       = 1001
          allow_privilege_escalation = false
          read_only_root_filesystem  = true
          named_ports = [{
            container_port = "10054"
            name           = "metrics"
            protocol       = "TCP"
          }]
          args = [
            "--v=2",
            "--logtostderr",
            "--probe=kubedns,127.0.0.1:10053,kubernetes.default.svc.cluster.local,5,SRV",
            "--probe=dnsmasq,127.0.0.1:53,kubernetes.default.svc.cluster.local,5,SRV",
          ]
          requests = {
            cpu    = "10m"
            memory = "20Mi"
          }
          liveness_probe = [{
            http_get = [{
              path = "/metrics"
              port = "10054"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 60
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 5
          }]
        }]
        volume_config_map = [{
          name         = "kube-dns-config"
          cm_name      = "kube-dns"
          default_mode = "0420"
          optional     = true
        }]
        security_context = [{
            fs_group            = "65534"
            supplemental_groups = [
              "65534"
            ]
        }]
        dns_policy = "Default"
        affinity = [{
          pod_anti_affinity = [{
            preferred_during_scheduling_ignored_during_execution = [{
              weight                                    = 100
              topology_key                              = "kubernetes.io/hostname"
              label_selector_match_expressions_key      = "k8s-app"
              label_selector_match_expressions_operator = "In"
              label_selector_match_expressions_values   = ["kube-dns"]
            }]
          }]
        }]
      }
    }
    kube_dns_autoscaler = {
      app_name             = "kube-dns-autoscaler-${var.lcaas}"
      namespace            = "kube-system"
      service_account_name = "kube-dns-autoscaler-${var.lcaas}"
      deployment = {
        enabled                         = local.enable_custom_kube_dns
        metadata_annotations            = {
          "seccomp.security.alpha.kubernetes.io/pod"           = "docker/default"
        }
        labels_override                 = { k8s-app = "kube-dns-autoscaler-${var.lcaas}" }
        node_selector                   = { "kubernetes.io/os" = "linux" }
        template_namespace              = null
        priority_class_name             = "system-cluster-critical"
        toleration                      = [{operator = "Exists", key = "CriticalAddonsOnly"},
                                           {operator = "Exists", key = "components.gke.io/gke-managed-components"},]
        automount_service_account_token = true
        containers = [{
          name              = "autoscaler"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_autoscaler_image}"
          image_pull_policy = "IfNotPresent"
          command = [
            "/cluster-proportional-autoscaler",
            "--namespace=kube-system",
            "--configmap=kube-dns-autoscaler",
            "--target=Deployment/kube-dns-${var.lcaas}",
            "--default-params={\"linear\":{\"coresPerReplica\":256,\"nodesPerReplica\":16,\"preventSinglePointFailure\":true,\"includeUnschedulableNodes\":true}}",
            "--logtostderr=true",
            "--v=2",
          ]
          requests = {
            cpu    = "20m"
            memory = "10Mi"
          }
        }]
        security_context = [{
            fs_group             = "65534"
            supplemental_groups  = [
              "65534"
            ]
        }]
      }
    }
  }
  daemonsets = {
    fuse = {
      app_name             = "fuse-device-plugin-daemonset"
      namespace            = "kube-system"
      service_account_name = ""
      daemonset = {
        host_network = true
        labels_override = {
          name     = "fuse-device-plugin-ds"
        }
        restart_policy                  = "Always"
        containers = [{
          name            = "fuse-device-plugin-ctr"
          image           = local.fuse_image
          security_context = true
          privileged = false
          drop = ["all"]
          volume_mount = [{
            mount_path = "/var/lib/kubelet/device-plugins"
            name       = "device-plugin"
          }]
        }]
        volume = [{
          name = "device-plugin"
          path = "/var/lib/kubelet/device-plugins"
        }]
        image_pull_secrets = [{
          name = "registry-secret"
        }]
      }
    },
    xdr-agent-daemon = {
      enabled              = local.enable_xdr_agent_daemon
      app_name             = "xdr-agent-daemon"
      namespace            = module.create_namespace.ns["xdr-agent-daemon"]
      service_account_name = "xdr-agent-user"
      termination_grace_period_seconds = 90
      daemonset = {
        node_selector = {"kubernetes.io/os" = "linux"}
        host_network = true
        host_pid = true
        host_ipc = true
        annotations = {"container.apparmor.security.beta.kubernetes.io/cortex-agent" = "unconfined"}
        toleration = [{ operator = "Exists" , effect = "NoSchedule"},
                      {operator = "Exists" , effect = "PreferNoSchedule"},
                      {operator = "Exists" , effect = "NoExecute"}]
        meta_labels_override = {
          "app.kubernetes.io/name" = "cortex-agent",
          "app.kubernetes.io/part-of" = "cortex",
          "app.kubernetes.io/component" = "agent" ,
          "app.kubernetes.io/version" = local.xdr_agent_daemon_version
          }
        labels_override = {
          "app.kubernetes.io/name" = "cortex-agent"
        }
        restart_policy                  = "Always"
        priority_class_name             = "xdr-agent-priority"
        containers = [{
          name            = "cortex-agent"
          image           = "${local.xdr_agent_daemon_registry}:${local.xdr_agent_daemon_version}"
          security_context = true
          privileged = false
          add = ["SYS_ADMIN",
                 "SYS_CHROOT",
                "SYS_MODULE",
                "SYS_PTRACE",
                "SYS_RESOURCE",
                "SYS_RAWIO",
                "DAC_OVERRIDE",
                "DAC_READ_SEARCH",
                "NET_ADMIN",
                "NET_RAW",
                "IPC_LOCK",
                "FOWNER",
                "KILL",
                "SETGID",
                "SETUID"]
          requests = {
            cpu    = lookup(var.overrides, "xdr_agent_daemon_cpu_request", "100m")
            memory = lookup(var.overrides, "xdr_agent_daemon_memory_request", "128Mi")
          }
          limits = {
            cpu    = lookup(var.overrides, "xdr_agent_daemon_cpu_limit", "1000m")
            memory = lookup(var.overrides, "xdr_agent_daemon_memory_limit", "4Gi")
          }
          env = [
            { name = "XDR_CLUSTER_NAME_URL", value = "metadata2" },
            { name = "XDR_HOST_ROOT", value = "/host-fs" },
            { name = "XDR_POD_INFO", value = "/var/run/pod-info" },
            { name = "XDR_PROXY_LIST", value = "************:13128" }
          ]
          volume_mount = [{
            mount_path = "/host-fs"
            name       = "host-fs"
            read_only  = true
          },
          {
            mount_path = "/var/log"
            name       = "var-log"
          },
          {
            mount_path = "/lib/modules"
            name       = "host-km-directory"
          },
          {
            mount_path = "/var/run/pod-info"
            name       = "pod-info"
            read_only  = true
          },
          {
            mount_path = "/etc/traps"
            name       = "agent-ids"
          },
          {
            mount_path = "/opt/traps/config/deployment"
            name       = "deployment-secrets"
            read_only  = true
          }]
        }]
        volume = [{
          name = "host-fs"
          path = "/"
          type = "Directory"
        },
        {
          name = "var-log"
          path = "/var/log"
          type = "Directory"
        },
        {
          name = "host-km-directory"
          path = "/lib/modules"
          type = "Directory"
        },
        {
          name = "agent-ids"
          path = "/etc/traps"
          type = "DirectoryOrCreate"
        }]
        downwardApi_volume = [
          {
            name = "pod-info"
            items = [{path = "uid", field_path = "metadata.uid"},
                     {path = "name", field_path = "metadata.name"},
                     {path = "labels", field_path = "metadata.labels"},
                     {path = "annotations", field_path = "metadata.annotations"}]
          }
        ]
        secret_volume = [
          {
            name = "deployment-secrets"
            secret_name = "xdr-agent-deployment"
          }
        ]
        image_pull_secrets = [{
          name = "cortex-docker-secret"
        }]
      }
    }
    disable-health-monitor = {
      enabled              = local.disable_health_monitor
      app_name             = "disable-health-monitor"
      namespace            = "kube-system"
      daemonset = {
        node_selector = {"kubernetes.io/os" = "linux"}
        host_pid = true
        volume = [{
          name = "host"
          path = "/"
          type = ""
        }]
        init_containers = [{
          name                       = "startup-script"
          image                      = local.disable_health_monitor_image
          security_context           = true
          privileged                 = true
          allow_privilege_escalation = true
          env = [{
            name = "STARTUP_SCRIPT",
            value = <<-SCRIPT
              set -o errexit
              set -o pipefail
              set -o nounset
              echo "Stopping kubelet health-monitor"
              chroot /host nsenter -a -t1 -- systemctl stop kubelet-monitor.service

              echo "Disabling kubelet health-monitor"
              chroot /host nsenter -a -t1 -- systemctl disable kubelet-monitor.service

              echo "Stopping containerd health-monitor"
              chroot /host nsenter -a -t1 -- systemctl stop kube-container-runtime-monitor.service

              echo "Disabling containerd health-monitor"
              chroot /host nsenter -a -t1 -- systemctl disable kube-container-runtime-monitor.service
            SCRIPT
          }]
          command = [
            "/bin/bash",
            "-c",
            <<-SCRIPT
            do_startup_script() {
              local err=0;
              bash -c "$STARTUP_SCRIPT" && err=0 || err=$?
              if [[ $err != 0 ]]; then
                echo "!!! startup-script failed! exit code '$err'" 1>&2
                return 1
              fi
              echo "!!! startup-script succeeded!" 1>&2
              return 0
            }

            do_startup_script
            SCRIPT
          ]
          volume_mount = [
            {
              mount_path = "/host"
              name       = "host"
            }
          ]
        }]
        containers = [{
          name                       = "pause"
          image                      = local.disable_health_monitor_pause_image
        }]
      }
    }
  }
  prom_stackdriver_metrics_epp = [
    "pubsub.googleapis.com/subscription/ack_message_count",
    "pubsub.googleapis.com/subscription/num_undelivered_messages",
    "pubsub.googleapis.com/subscription/oldest_unacked_message_age",
    "pubsub.googleapis.com/topic/send_message_operation_count",
    "logging.googleapis.com/byte_count",
    "logging.googleapis.com/log_entry_count"
  ]
  prom_stackdriver_metrics_full = [
    "bigquery.googleapis.com/slots/allocated_for_project_and_job_type",
    "bigquery.googleapis.com/query/count",
    "dataflow.googleapis.com/job/elements_produced_count",
    "kubernetes.io/node/ephemeral_storage/total_bytes",
    "kubernetes.io/node/ephemeral_storage/used_bytes"
  ]
  stackdriver_metrics_list = false ? join(",", local.prom_stackdriver_metrics_epp) : join(",", concat(local.prom_stackdriver_metrics_epp, local.prom_stackdriver_metrics_full))
  stackdriver_metrics_7h = join(",", [
    "bigquery.googleapis.com/storage/uploaded_bytes",
    "bigquery.googleapis.com/storage/uploaded_row_count",
    "bigquery.googleapis.com/storage/stored_bytes"
  ])
  log_forwarding_namespace = var.viso_env == "dev" ? "performance-log-forwarding" : "${var.viso_env}-log-forwarding"
  fuse_image = "${var.docker_image_gar_location}/cortex-xdr/fuse-plugin/fuse-device-plugin:v7"
  stackdriver_exporter_version = lookup(var.overrides, "stackdriver_exporter_version", "v0.17.0")
  xdr_agent_daemon_registry = var.viso_env == "prod-fr" ? "us-central1-docker.pkg.dev/xdr-fr-1541059937/agent-docker/cortex-agent" :var.viso_env == "prod-gv" ? "us-central1-docker.pkg.dev/xdr-gv-1908378801456/agent-docker/cortex-agent": var.viso_env == "dev" ? "us-central1-docker.pkg.dev/xdr-us-24072002/agent-docker/cortex-agent" : "us-central1-docker.pkg.dev/xdr-us-24072002/agent-docker/cortex-agent"
  xdr_agent_daemon_version = lookup(var.overrides, "xdr_agent_daemon_version", var.viso_env == "prod-gv" ? "8.6.1.130921" : var.viso_env == "prod-fr" ? "8.6.1.129214" : "8.7.100.136016")
  kube_state_metrics_version = "v2.13.0"
  custom_metrics_stackdriver_adapter_version = "v0.16.1-gke.0"
}

resource "kubernetes_api_service" "custom-metrics-api-service" {
  depends_on = [module.create_tools_apps]
  metadata {
    name = "v1beta1.external.metrics.k8s.io"

    annotations = {
      app = "custom-metrics-stackdriver-adapter"
    }
  }

  spec {
    service {
      namespace = module.create_namespace.ns["custom-metrics"]
      name      = "custom-metrics-stackdriver-adapter"
    }

    group                    = "external.metrics.k8s.io"
    version                  = "v1beta1"
    insecure_skip_tls_verify = true
    group_priority_minimum   = 100
    version_priority         = 100
  }
}

resource "kubernetes_priority_class" "xdr_agent_priority_class" {

  metadata {
    name = "xdr-agent-priority"
    annotations = {
      "meta.helm.sh/release-name" = "kube-system-xdr-agent-daemon"
      "meta.helm.sh/release-namespace" = "kube-system"
    }
  }

  value = 90
}