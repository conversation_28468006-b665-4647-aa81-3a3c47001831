
module "bootstrap" {
  source                  = "../../../tf/modules/oci_outpost"
  lcaas                   = var.lcaas
  compartment_name        = var.project_id
  tenancy_ocid            = var.viso_env == "dev" ? local.dev.tenancy_ocid : local.prod.tenancy_ocid
  parent_compartment_ocid = var.viso_env == "dev" ? local.dev.parent_compartment_ocid : local.prod.parent_compartment_ocid
  project_id              = var.project_id
  replication_type        = contains(["prod-fr","prod-gv"],var.viso_env) ? "user_managed" : "auto"
  providers = {
    oci = oci.us-phoenix-1
  }
}

