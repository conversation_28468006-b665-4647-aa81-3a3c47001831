# TODO: default was copy/pasted from tenant, should be factored out
variable "viso_new_platform" { default = false }
variable "billing_account" { default = "0191EA-C0E00D-70713E" }
variable "lcaas" {}
variable "outpost_project_id" {}
variable "viso_env" {
  type = string

  validation {
    condition = contains([
      "dev",
      "prod-au",
      "prod-ca",
      "prod-ch",
      "prod-de",
      "prod-es",
      "prod-eu",
      "prod-fa",
      "prod-id",
      "prod-il",
      "prod-it",
      "prod-kr",
      "prod-in",
      "prod-jp",
      "prod-pl",
      "prod-pr",
      "prod-qt",
      "prod-sa",
      "prod-sg",
      "prod-tw",
      "prod-uk",
      "prod-us",
      "prod-za",
      "prod-gv",
      "prod-fr"
    ], var.viso_env)

    error_message = "Not a recognized viso_env - a GCP folder for creating the outpost project may be missing!"
  }
}
variable "product_type" {}
variable "owner" {}
variable "owner_group" { default = "unknown" }
variable "tenant_type" {}
variable "project_id" {}

#oci specific vars:
