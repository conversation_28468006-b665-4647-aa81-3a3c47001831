variable "project_id" {
  default = ""
}
variable "xdr_env" {}

variable "viso_new_platform" {}

variable "lcaas" {}
variable "project_prefix" {}
variable "host_project" {}
variable "viso_env" {
  type = string

  validation {
    condition = contains([
      "dev",
      "prod-au",
      "prod-ca",
      "prod-ch",
      "prod-de",
      "prod-es",
      "prod-eu",
      "prod-fa",
      "prod-id",
      "prod-il",
      "prod-it",
      "prod-kr",
      "prod-in",
      "prod-jp",
      "prod-pl",
      "prod-pr",
      "prod-qt",
      "prod-sa",
      "prod-sg",
      "prod-tw",
      "prod-uk",
      "prod-us",
      "prod-za",
      "prod-gv",
      "prod-fr"
    ], var.viso_env)

    error_message = "Not a recognized viso_env - a GCP folder for creating the outpost project may be missing!"
  }
}

variable "region" {
  default = ""
}