resource "tls_private_key" "oci_api_signing_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "google_secret_manager_secret" "oci_api_signing_key_private" {
  secret_id = "${var.lcaas}-oci-api-signing-key-private-key"
  dynamic "replication" {
    for_each = var.replication_type == "auto" ? [1] : []
    content {
      auto {}
    }
  }
  dynamic "replication" {
    for_each = var.replication_type == "user_managed" ? [1] : []
    content {
      user_managed {
        dynamic "replicas" {
          for_each = var.secret_replicas
          content {
            location = replicas.value
          }
        }
      }
    }
  }
}

resource "google_secret_manager_secret_version" "oci_api_signing_key_private" {
  secret = google_secret_manager_secret.oci_api_signing_key_private.id

  secret_data      = tls_private_key.oci_api_signing_key.private_key_pem
}

resource "google_secret_manager_secret" "oci_api_signing_key_public" {
  secret_id = "${var.lcaas}-oci-api-signing-key-public-key"
  dynamic "replication" {
    for_each = var.replication_type == "auto" ? [1] : []
    content {
      auto {}
    }
  }
  dynamic "replication" {
    for_each = var.replication_type == "user_managed" ? [1] : []
    content {
      user_managed {
        dynamic "replicas" {
          for_each = var.secret_replicas
          content {
            location = replicas.value
          }
        }
      }
    }
  }
}

resource "google_secret_manager_secret_version" "oci_api_signing_key_public" {
  secret = google_secret_manager_secret.oci_api_signing_key_public.id

  secret_data         = tls_private_key.oci_api_signing_key.public_key_pem
}

# the data resource is necessary to "white-wash" the ephemerality of the secret
data "google_secret_manager_secret_version_access" "oci_api_signing_key_public" {
  secret = google_secret_manager_secret_version.oci_api_signing_key_public.secret
}

resource "google_secret_manager_secret_iam_member" "cts" {
  secret_id = google_secret_manager_secret.oci_api_signing_key_private.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:cts-pod@${var.project_id}.iam.gserviceaccount.com"
}
