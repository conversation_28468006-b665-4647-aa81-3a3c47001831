variable "lcaas" {}
variable "compartment_name" {}
variable "parent_compartment_ocid" {}
variable "tenancy_ocid" {}
variable "project_id" {
  description = "GCP ST project ID"
  type = string
}

variable "secret_replicas" {
  description = "List of locations for user_managed replication"
  type        = list(string)
  default     = ["us-central1"]
}

variable "replication_type" {
  description = "Replication type for secrets: auto or user_managed, in fedramp env secrets replication must be user_managed"
  type        = string
  default     = "auto"
  validation {
    condition     = contains(["auto", "user_managed"], var.replication_type)
    error_message = "replication_type must be either 'auto' or 'user_managed'."
  }
}