_component_name: "'scans'"

namespaceOverride: "globals.cas_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

resources:
  limits:
    cpu: '"2"'
    memory: '"2Gi"'
    ephemeral-storage: '"500Mi"'
  requests:
    cpu: 'region.is_dev ? "50m" : "200m"'
    memory: '"512Mi"'
    ephemeral-storage: '"50Mi"'