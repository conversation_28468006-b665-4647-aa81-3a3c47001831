data "google_container_engine_versions" "gke_versions" {
  location       = var.clusters_data.location
  project        = var.project_id
  version_prefix = "${var.clusters_data.min_master_version}." # ended with "." following provider docs
}

resource "google_container_cluster" "cluster" {
  #checkov:skip=CKV_GCP_13: "Ensure a client certificate is used by clients to authenticate to Kubernetes Engine Clusters"
  #checkov:skip=CKV_GCP_24: "Ensure PodSecurityPolicy controller is enabled on the Kubernetes Engine Clusters"
  #checkov:skip=CKV_GCP_12: "Ensure Network Policy is enabled on Kubernetes Engine Clusters"
  provider                 = google-beta
  name                     = var.clusters_data.name
  location                 = var.clusters_data.location
  remove_default_node_pool = true
  initial_node_count       = var.clusters_data.initial_node_count
  project                  = var.project_id
  min_master_version       = var.clusters_data.min_master_version
  enable_shielded_nodes    = var.shielded_nodes
  datapath_provider        = var.enable_dataplane_v2 ? "ADVANCED_DATAPATH" : null
  enable_l4_ilb_subsetting = var.enable_l4_ilb_subsetting

  dynamic "enable_k8s_beta_apis" {
    for_each = var.enable_k8s_beta_apis ? ["enable_k8s_beta_apis"] : []
    content {
      enabled_apis = var.beta_apis
    }
  }

release_channel {
  channel = (
    var.enable_gateway_api ||
    contains(["dev", "prod-pr"], var.viso_env) ||
    startswith(var.clusters_data.name, "app-hub")
  ) ? "UNSPECIFIED" : "EXTENDED"
}
  dynamic "database_encryption" {
    for_each = var.enable_secure_gke ? ["enable_secure_gke"] : []
    content {
      state = "ENCRYPTED"
      key_name = var.default_kms_key_id
    }
  }
  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
  }
  private_cluster_config {
    enable_private_endpoint = false
    enable_private_nodes    = true
    master_ipv4_cidr_block  = "**********/28"
  }

  node_locations = var.clusters_data.node_locations
  network        = var.clusters_data.network
  subnetwork     = var.clusters_data.subnetwork

  dynamic "gateway_api_config" {
    for_each = var.enable_gateway_api ? ["enable_gateway_api"] : []
    content {
      channel = "CHANNEL_STANDARD"
    }
  }

  dynamic "resource_usage_export_config" {
    for_each = var.enable_gke_metering ? ["enable_gke_metering"] : []
    content {
      enable_network_egress_metering       = var.enable_network_egress_metering
      enable_resource_consumption_metering = var.enable_resource_consumption_metering
      bigquery_destination {
        dataset_id = var.bigquery_destination_dataset_id
      }
    }
  }

  ip_allocation_policy {
    services_ipv4_cidr_block = "*********/20"
    cluster_ipv4_cidr_block  = "*********/14"
  }

  dynamic "network_policy" {
    # This entire section is not present in the API when dataplane v2 is enabled
    for_each = (var.network_policy_config && !var.enable_dataplane_v2) ? ["apply"] : []
    content {
      provider = "CALICO"
      enabled  = true
    }
  }

  addons_config {
    dynamic "gke_backup_agent_config" {
      for_each = var.enable_gke_backup ? ["enable_gke_backup"] : []
      content {
        enabled = true
      }
    }
    dynamic "network_policy_config" {
      for_each = var.network_policy_config ? ["apply"] : []
      content {
        # this must be disabled = true when dataplane v2 is enabled; use dataplane v2 for NetworkPolicy not Calico
        # otherwise it should generally be disabled = false
        disabled = var.enable_dataplane_v2
      }
    }

    http_load_balancing {
      disabled = false
    }

    horizontal_pod_autoscaling {
      disabled = false
    }
  }

  master_auth {
    #username = ""
    #password = ""
    client_certificate_config {
      issue_client_certificate = false
    }
  }
  master_authorized_networks_config {
    dynamic "cidr_blocks" {
      for_each = [for network in var.clusters_data.cidr_blocks : {
        cidr_block   = network.cidr_block
        display_name = network.display_name
      }]
      content {
        cidr_block   = cidr_blocks.value.cidr_block
        display_name = cidr_blocks.value.display_name
      }
    }
  }

  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }

  resource_labels = {
    app = "xdr"
  }

  maintenance_policy {
    recurring_window {
      start_time = "2020-04-11T15:00:00Z"
      end_time   = "2020-04-11T21:00:00Z"
      recurrence = "FREQ=WEEKLY;BYDAY=SA,SU"
    }

  dynamic "maintenance_exclusion" {
    for_each = (
      contains(["dev", "prod-pr"], var.viso_env) ||
      var.enable_gateway_api ||
      startswith(var.clusters_data.name, "app-hub")
    ) ? [] : [1]

    content {
      exclusion_name = "no-minor-or-node-upgrades-${uuid()}"
      start_time     = local.master_version_to_dates[var.clusters_data.min_master_version].release_date
      end_time       = local.master_version_to_dates[var.clusters_data.min_master_version].end_of_extended_support
      exclusion_options {
        scope = "NO_MINOR_OR_NODE_UPGRADES"
        }
      }
    }
  }

  cluster_autoscaling {
    # This enabled parameter is for node autoprovisioning
    # We are forced to set it as it is a required in current terraform provider version
    enabled = try(var.clusters_data.node_autoprovisioning, false)
    # Irrespective of node autoprovisioning, Cluster autoscaling will remain enabled with below choosen profile
    # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#nested_cluster_autoscaling
    # https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1/projects.locations.clusters#Cluster.ClusterAutoscaling
    autoscaling_profile = try(var.clusters_data.autoscaling_profile, "BALANCED")
  }

  monitoring_config {
    managed_prometheus {
      enabled = false
    }
    enable_components = ["SYSTEM_COMPONENTS"]


    dynamic "advanced_datapath_observability_config" {
      for_each = var.enable_dataplane_v2 ? ["enabled"] : []
      content {
        enable_metrics = var.enable_dataplane_v2_metrics
        enable_relay   = var.enable_dataplane_v2_relay
      }
    }
  }


  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      initial_node_count,
      node_config,
      ip_allocation_policy,
      min_master_version,
      location,
      addons_config[0].http_load_balancing,
      addons_config[0].horizontal_pod_autoscaling,
      addons_config[0].gke_backup_agent_config,
      datapath_provider,
      enable_shielded_nodes,
      maintenance_policy,
      node_locations,
      release_channel,
      enable_l4_ilb_subsetting,
    ]
  }
}

resource "google_container_node_pool" "alyx_node_pool" {
  for_each = {
    for nodepool in var.alyx_node_pool :
    nodepool.name => nodepool
    if nodepool.enabled
  }

  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null
    disk_size_gb      = each.value.node_disk_size
    disk_type         = each.value.disk_type
    labels            = each.value.labels
    machine_type      = each.value.machine_type
    service_account   = each.value.service_account
    oauth_scopes      = ["https://www.googleapis.com/auth/cloud-platform"]

    metadata = {
      disable-legacy-endpoints = "true"
    }

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})

      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})

    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      node_config[0].boot_disk_kms_key, # BYOK (CMEK) feature flag is immutable
      node_count,                       # Only Cronus operator should control node count by changing replica count as node pool has autoscaling enabled
      version,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
    ]
  }
}

resource "google_container_node_pool" "argo_enhanced_node_pool" {
  for_each = {
    for nodepool in var.argo_enhanced_node_pool :
    nodepool.key_name => nodepool
    if nodepool.enabled
  }

  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = each.value.node_count
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    gcfs_config {
      enabled = lookup(each.value, "image_streaming", false)
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null
    disk_size_gb      = each.value.node_disk_size
    disk_type         = each.value.disk_type
    labels            = each.value.labels
    machine_type      = each.value.machine_type
    service_account   = each.value.service_account
    oauth_scopes      = ["https://www.googleapis.com/auth/cloud-platform"]

    metadata = {
      disable-legacy-endpoints = "true"
    }

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})

      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})

    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      node_config[0].boot_disk_kms_key, # BYOK (CMEK) feature flag is immutable
      node_count,                       # Only Cronus operator should control node count by changing replica count as node pool has autoscaling enabled
      version,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
    ]
  }
}

resource "google_container_node_pool" "argo_node_pool" {
  for_each = {
    for nodepool in var.argo_node_pool :
    nodepool.key_name => nodepool
    if nodepool.enabled
  }

  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = each.value.node_count
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    gcfs_config {
      enabled = lookup(each.value, "image_streaming", false)
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null
    disk_size_gb      = each.value.node_disk_size
    disk_type         = each.value.disk_type
    labels            = each.value.labels
    machine_type      = each.value.machine_type
    service_account   = each.value.service_account
    oauth_scopes      = ["https://www.googleapis.com/auth/cloud-platform"]

    metadata = {
      disable-legacy-endpoints = "true"
    }

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})

      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})

    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      node_config[0].boot_disk_kms_key, # BYOK (CMEK) feature flag is immutable
      node_count,                       # Only Cronus operator should control node count by changing replica count as node pool has autoscaling enabled
      version,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
    ]
  }
}

resource "google_container_node_pool" "cronus_node_pool" {
  for_each = {
    for nodepool in var.cronus_node_pool :
    nodepool.key_name => nodepool
    if nodepool.enabled
  }

  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = each.value.node_count
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null
    disk_size_gb      = each.value.node_disk_size
    disk_type         = each.value.disk_type
    labels            = each.value.labels
    machine_type      = each.value.machine_type
    service_account   = each.value.service_account
    oauth_scopes      = ["https://www.googleapis.com/auth/cloud-platform"]

    metadata = {
      disable-legacy-endpoints = "true"
    }

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})

      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})

    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  lifecycle {
    create_before_destroy = false
    ignore_changes = [
      node_config[0].boot_disk_kms_key, # BYOK (CMEK) feature flag is immutable
      node_count,                       # Only Cronus operator should control node count by changing replica count as node pool has autoscaling enabled
      version,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
    ]
  }
}

resource "google_container_node_pool" "static_node_pool" {
  #checkov:skip=CKV_GCP_10: "Ensure 'Automatic node upgrade' is enabled for Kubernetes Clusters"
  #checkov:skip=CKV_GCP_22: "Ensure Container-Optimized OS (cos) is used for Kubernetes Engine Clusters Node image"
  for_each = {
    for nodepool in var.static_node_pool :
    nodepool.key_name => nodepool
    if nodepool.enabled
  }
  depends_on = [google_container_node_pool.scylla_node_pool]
  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = each.value.node_count
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      id,
      node_config[0].boot_disk_kms_key,
      node_config[0].oauth_scopes,
      node_config[0].metadata,
      node_config[0].linux_node_config,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
      version,
      node_locations
    ]
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})
    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    gcfs_config {
      enabled = lookup(each.value, "image_streaming", false)
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null

    disk_size_gb = lookup(each.value, "node_disk_size", 50)

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
    machine_type    = each.value.machine_type
    service_account = each.value.service_account
    oauth_scopes    = ["https://www.googleapis.com/auth/cloud-platform"]
    metadata = {
      disable-legacy-endpoints = "true"
    }

    labels = each.value.labels

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})
      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }
  }

  timeouts {
    update = "80m"
    delete = "80m"
  }
}

resource "google_container_node_pool" "taint_nodepool" {
  #checkov:skip=CKV_GCP_10: "Ensure 'Automatic node upgrade' is enabled for Kubernetes Clusters"
  #checkov:skip=CKV_GCP_22: "Ensure Container-Optimized OS (cos) is used for Kubernetes Engine Clusters Node image"
  for_each = {
    for nodepool in var.dynamic_node_pools :
    nodepool.key_name => nodepool
    if nodepool.enabled
  }
  depends_on = [google_container_node_pool.scylla_node_pool]
  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = lookup(each.value, "node_count", null)
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      id,
      node_config[0].boot_disk_kms_key,
      node_config[0].metadata,
      node_config[0].oauth_scopes,
      node_config[0].linux_node_config,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
      node_count,
      version
    ]
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})
    content {
      total_max_node_count = max(lookup(lookup(local.dynamic_max_node_count, each.value.name, {}), "max_node_count", 0), lookup(autoscaling.value, "max_node_count", null))
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  dynamic "upgrade_settings" {
    for_each = lookup(each.value, "upgrade_settings", {})
    content {
      max_surge       = lookup(upgrade_settings.value, "max_surge", null)
      max_unavailable = lookup(upgrade_settings.value, "max_unavailable", null)
    }
  }

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    gcfs_config {
      enabled = lookup(each.value, "image_streaming", false)
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null

    disk_size_gb = lookup(each.value, "node_disk_size", "20")
    disk_type    = lookup(each.value, "disk_type", "pd-standard")

    workload_metadata_config {
      mode = "GKE_METADATA"
    }
    machine_type    = each.value.machine_type
    service_account = each.value.service_account
    oauth_scopes    = ["https://www.googleapis.com/auth/cloud-platform"]
    metadata = {
      disable-legacy-endpoints = "true"
    }

    labels = each.value.labels

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})
      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }
  }

  timeouts {
    update = "80m"
    delete = "80m"
  }
}

resource "google_container_node_pool" "scylla_node_pool" {
  #checkov:skip=CKV_GCP_10: "Ensure 'Automatic node upgrade' is enabled for Kubernetes Clusters"
  #checkov:skip=CKV_GCP_22: "Ensure Container-Optimized OS (cos) is used for Kubernetes Engine Clusters Node image"
  for_each = {
    for nodepool in var.scylla_node_pool :
    nodepool.name => nodepool
    if nodepool.enabled
  }
  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = each.value.node_count
  version    = local.nodepool_version

  lifecycle {
    ignore_changes = [
      autoscaling,
      id,
      node_config[0].boot_disk_kms_key,
      node_config[0].metadata,
      node_config[0].oauth_scopes,
      node_config[0].workload_metadata_config,
      node_config[0].linux_node_config,
      node_config[0].disk_type,
      node_config[0].disk_size_gb,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
      node_count,
      version,
      node_locations
    ]
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})
    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null

    disk_size_gb = lookup(each.value, "node_disk_size", "20")
    disk_type    = lookup(each.value, "disk_type", "pd-standard")

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
    machine_type    = each.value.machine_type
    service_account = each.value.service_account
    oauth_scopes    = ["https://www.googleapis.com/auth/cloud-platform"]
    metadata = {
      disable-legacy-endpoints = "true"
    }

    labels = each.value.labels

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})
      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }
  }
}

resource "google_container_node_pool" "scylla_xcloud_node_pool" {
  #checkov:skip=CKV_GCP_10: "Ensure 'Automatic node upgrade' is enabled for Kubernetes Clusters"
  #checkov:skip=CKV_GCP_22: "Ensure Container-Optimized OS (cos) is used for Kubernetes Engine Clusters Node image"
  for_each = {
    for nodepool in var.scylla_xcloud_node_pool :
    nodepool.name => nodepool
    if nodepool.enabled
  }
  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = each.value.node_count
  version    = local.nodepool_version

  lifecycle {
    ignore_changes = [
      autoscaling,
      id,
      node_config[0].boot_disk_kms_key,
      node_config[0].metadata,
      node_config[0].oauth_scopes,
      node_config[0].workload_metadata_config,
      node_config[0].linux_node_config,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
      node_count,
      version,
      node_locations
    ]
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})
    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null

    disk_size_gb = lookup(each.value, "node_disk_size", "20")
    disk_type    = lookup(each.value, "disk_type", "pd-standard")

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
    machine_type    = each.value.machine_type
    service_account = each.value.service_account
    oauth_scopes    = ["https://www.googleapis.com/auth/cloud-platform"]
    metadata = {
      disable-legacy-endpoints = "true"
    }

    labels = each.value.labels

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})
      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }
  }
}

resource "google_container_node_pool" "xsoar_node_pool" {
  #checkov:skip=CKV_GCP_10: "Ensure 'Automatic node upgrade' is enabled for Kubernetes Clusters"
  #checkov:skip=CKV_GCP_22: "Ensure Container-Optimized OS (cos) is used for Kubernetes Engine Clusters Node image"
  for_each = {
    for nodepool in var.xsoar_node_pool :
    nodepool.name => nodepool
    if nodepool.enabled
  }
  project    = var.project_id
  provider   = google-beta
  name       = each.value.name
  location   = google_container_cluster.cluster.location
  cluster    = google_container_cluster.cluster.name
  node_count = lookup(each.value, "node_count", null)
  version    = local.nodepool_version
  max_pods_per_node = lookup(each.value, "max_pods_per_node", null)

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      id,
      node_config[0].boot_disk_kms_key,
      node_config[0].metadata,
      node_config[0].oauth_scopes,
      node_config[0].linux_node_config,
      node_config[0].kubelet_config,
      node_config[0].resource_labels,
      version,
    ]
  }

  dynamic "autoscaling" {
    for_each = lookup(each.value, "autoscaling", {})
    content {
      total_max_node_count = lookup(autoscaling.value, "max_node_count", null)
      total_min_node_count = lookup(autoscaling.value, "min_node_count", null)
    }
  }

  management {
    auto_repair  = true
    auto_upgrade = var.viso_env == "dev" ? false : true
  }

  node_config {
    shielded_instance_config {
      enable_secure_boot = var.enable_secure_gke
    }
    gcfs_config {
      enabled = lookup(each.value, "image_streaming", false)
    }
    boot_disk_kms_key = var.enable_secure_gke || var.enable_byok ? var.default_kms_key_id : null

    disk_size_gb = lookup(each.value, "node_disk_size", "20")
    disk_type    = lookup(each.value, "disk_type", "pd-standard")

    workload_metadata_config {
      mode = lookup(each.value, "node_metadata", "GKE_METADATA")
    }
    machine_type    = each.value.machine_type
    service_account = each.value.service_account
    oauth_scopes    = ["https://www.googleapis.com/auth/cloud-platform"]
    metadata = {
      disable-legacy-endpoints = "true"
    }

    labels = each.value.labels

    dynamic "taint" {
      for_each = lookup(each.value, "taint", {})
      content {
        effect = lookup(taint.value, "effect", null)
        key    = lookup(taint.value, "key", null)
        value  = lookup(taint.value, "value", null)
      }
    }
  }
}


output "gke_host" {
  value = google_container_cluster.cluster.endpoint
}

output "cluster_name" {
  value = var.cluster
}

output "gke_cluster_ca_certificate" {
  value = google_container_cluster.cluster.master_auth[0].cluster_ca_certificate
}

locals {
  master_version = google_container_cluster.cluster.master_version
  master_version_to_dates = { "1.28": {release_date = "2023-09-04T00:00:00Z", end_of_extended_support = "2025-12-03T00:00:00Z"},
    "1.29": {release_date = "2024-01-05T00:00:00Z", end_of_extended_support = "2026-01-24T00:00:00Z"},
    "1.30": {release_date = "2024-04-29T00:00:00Z", end_of_extended_support = "2026-07-29T00:00:00Z" },
    "1.31": {release_date = "2024-08-20T00:00:00Z", end_of_extended_support = "2026-10-21T00:00:00Z" },
    "1.32": {release_date = "2025-03-04T00:00:00Z", end_of_extended_support = "2027-02-10T00:00:00Z" },
    "1.33": {release_date = "2025-07-22T00:00:00Z", end_of_extended_support = "2027-06-02T00:00:00Z" },
  }
  valid_node_versions = data.google_container_engine_versions.gke_versions.valid_node_versions
  is_master_version_a_valid_node_version = contains(local.valid_node_versions, local.master_version)
  nodepool_version                       = var.viso_env == "dev" ? local.master_version : local.is_master_version_a_valid_node_version ? local.master_version : local.valid_node_versions[0]
  dynamic_max_node_count = {
    for node, node_data in google_container_cluster.cluster.node_pool :
    node_data.name => { max_node_count = node_data.autoscaling[0].max_node_count }
    if can(regex("dynamic-1", node_data.name))
  }
}
