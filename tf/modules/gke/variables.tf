variable "cronus_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      node_metadata   = ""
      disk_type       = ""
      node_disk_size  = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "alyx_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      node_metadata   = ""
      disk_type       = ""
      node_disk_size  = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "argo_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      node_metadata   = ""
      disk_type       = ""
      node_disk_size  = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "argo_enhanced_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      node_metadata   = ""
      disk_type       = ""
      node_disk_size  = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "default_kms_key_id" {
  default = ""
}

variable "enable_byok" {
  default = false
  type    = bool
}

variable "enable_secure_gke" {
  default = false
  type    = bool
}

variable "shielded_nodes" {
  default = false
  type    = bool
}

variable "project_id" {}
variable "viso_env" {}
variable "cluster" {}
variable "clusters_data" {
  default = {
    template = {
      name                  = ""
      node_count            = ""
      node_autoprovisioning = false
      autoscaling_profile   = "BALANCED"
      location              = ""
      machine_type          = ""
      service_account       = ""
      min_master_version    = ""
      node_locations        = ""
      network               = ""
      subnetwork            = ""
      cidr_blocks = {
        cidr_block   = ""
        display_name = ""
      }
    }
  }
}

variable "dynamic_node_pools" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "scylla_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "scylla_xcloud_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "small_epp" {
  type    = bool
  default = false
}

variable "xsoar_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "static_node_pool" {
  default = {
    template = {
      enabled         = false
      name            = ""
      node_count      = ""
      location        = ""
      cluster         = ""
      machine_type    = ""
      service_account = ""
      labels = {
        xdr-pool = ""
      }
      taint = {
        effect = ""
        key    = ""
        value  = ""
      }
    }
  }
}

variable "enable_gke_metering" {
  type    = bool
  default = false
}
variable "enable_network_egress_metering" {
  type    = bool
  default = true
}
variable "enable_resource_consumption_metering" {
  type    = bool
  default = true
}
variable "bigquery_destination_dataset_id" {
  default = ""
}

variable "is_fedramp" {
  default = false
}

variable "enable_dataplane_v2" {
  description = "ENABLE GKE dataplane V2"
  type        = bool
  default     = false
}

variable "enable_dataplane_v2_metrics" {
  description = "enable GKE dataplane v2 metrics"
  type        = bool
  default     = false
}

variable "enable_dataplane_v2_relay" {
  description = "enable GKE dataplane v2 relay"
  type        = bool
  default     = false
}

variable "network_policy_config" {
  description = "ENABLE network policy"
  type        = bool
  default     = false
}

variable "enable_l4_ilb_subsetting" {
  description = "ENABLE GKE L4 ILB subsetting"
  type        = bool
  default     = true
}

variable "enable_k8s_beta_apis" {
  description = "ENABLE GKE BETA API's"
  type        = bool
  default     = false
}

variable "beta_apis" {
  description = "BETA API's"
  type        = set(string)
  default     = []
}

variable "enable_gateway_api" {
  description = "ENABLE GKE Gateway Api"
  type        = bool
  default     = false
}

variable "enable_gke_backup" {
  description = "ENABLE GKE Backup Agent and CRDs"
  type        = bool
  default     = false
}

variable "enable_cloud_posture" {
  type = bool
  default = false
}

variable "enable_cloud_appsec" {
  type = bool
  default = false
}