module "cloudsec_init_workload_identity" {
  source = "../../modules/workload_identity"
  for_each = {
    for app in local.init :
    app.app_name => app
    if lookup(app, "enabled", true) && !lookup(app, "skip_workload_identity", false)
  }
  providers = {
    google     = google
    google.mt  = google.mt
    kubernetes = kubernetes
  }
  service_account_name = each.value.service_account_name
  mt_dedicated_group   = lookup(each.value, "mt_dedicated_group", false)
  dedicated_group_name = lookup(each.value, "dedicated_group_name", false)
  project_id           = var.project_id
  wi_project_id        = local.workload_identity_project_id
  data                 = lookup(each.value, "workload_identity", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns[local.cloudsec_namespace])
  viso_env             = var.viso_env
  create_kubernetes_sa = false
}

module "cloudsec_workload_identity" {
  source = "../../modules/workload_identity"
  for_each = {
    for app in local.cloudsec_apps :
    app.app_name => app
    if lookup(app, "enabled", true) && !lookup(app, "skip_workload_identity", false)
  }
  providers = {
    google     = google
    google.mt  = google.mt
    kubernetes = kubernetes
  }
  service_account_name = each.value.service_account_name
  mt_dedicated_group   = lookup(each.value, "mt_dedicated_group", false)
  dedicated_group_name = lookup(each.value, "dedicated_group_name", false)
  project_id           = var.project_id
  wi_project_id        = local.workload_identity_project_id
  data                 = lookup(each.value, "workload_identity", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns[local.cloudsec_namespace])
  viso_env             = var.viso_env
  create_kubernetes_sa = false
}

locals {
  init = {
    cloudsec_init = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-init"
      service_account_name = "cloudsec-init"
      workload_identity = {
        bq_admin = true
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"]
        ]
      }
    }
  }
}
# Apps
locals {
  cloudsec_apps = {
    action_plan_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-action-plan-api"
      service_account_name = "action-plan-api"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem_permissions_raw"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    action_plan_gen_cron = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-action-plan-gen"
      service_account_name = "action-plan-gen"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem_permissions_raw"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    action_plan_recon_cron = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-action-plan-recon"
      service_account_name = "action-plan-recon"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem_permissions_raw"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    action_plan_cleanup_cron = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-action-plan-cleanup"
      service_account_name = "action-plan-cleanup"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem_permissions_raw"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    dashboard_api_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-dashboard-api"
      service_account_name = "dashboard-api"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_access_views"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "dspm"), "roles/bigquery.dataViewer"],
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
      }
    }
    rule_management = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-rule-management"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-xdr-wi-rule-management-service"
      service_account_name = "rule-management"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"]
        ]
        bq_job_user = true,
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
      }
    }
    search_and_investigate = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-search-and-investigate"
      service_account_name = "search-and-investigate"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    secops_dash_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-secops-dash-api"
      service_account_name = "secops-dash-api"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    secops_dash_mttr_cron = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-secops-dash-mttr"
      service_account_name = "secops-dash-mttr"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    secops_dash_burndown_cron = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-secops-dash-burndown"
      service_account_name = "secops-dash-burndown"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"]
        ]
        bq_job_user = true,
      }
    }
    cloudsec_inline_scanner = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-inline-scanner"
      service_account_name = "inline-scanner"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "dp_uai_asset_change_feed_topic", null),
          lookup(var.topics_output, "verdict_manager_topic", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "dp_scan_logs", null), "roles/storage.objectCreator"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "verdict_manager_topic", null), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "bq_stats_topic", "null"), "roles/pubsub.publisher"]
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cloudsec_asset_change_sub", null),
        ]
        pubsub_viewer = true
      }
    }
    cloudsec_verdict_manager = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-verdict-manager"
      service_account_name = "verdict-manager"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"]
        ]
        topics = [
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "ap_issue_upsert", null)
        ]
        buckets       = [
          [lookup(var.buckets_output, "verdict_manager", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "dp_finding_emits", null), "roles/storage.objectCreator"]
          ]
        pubsub_viewer = true
        pubsub_subscriber = true
        topic_additional_roles = [
          [lookup(var.topics_output, "cloudsec_batch_verdicts"), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cloudsec_batch_verdicts_dlq"), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cloudsec_batch_verdicts", null),
          lookup(var.subscriptions_output, "cloudsec_batch_verdicts_dlq", null),
          lookup(var.subscriptions_output, "cloudsec_xspm_verdicts_notification", null),
          lookup(var.subscriptions_output, "cloudsec_xspm_verdicts_notification_dlq", null),
        ]
      }
    }
    cloudsec_attack_path_scanner_cron_job = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-attack-path-scanner"
      service_account_name = "attack-path-scanner"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"]
        ]
        buckets = [[lookup(var.buckets_output, "verdict_manager", null), "roles/storage.objectUser"]]
        topics = [
          lookup(var.topics_output, "verdict_manager_topic", null),
        ],
        subscriptions = [
          lookup(var.subscriptions_output, "dp_finding_emits_sub", null),
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", "null"), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cloudsec-xspm-scanner-rules", null), "roles/pubsub.publisher"],
        ]
      }
    }
    cloudsec_batch_scanner_cron_job = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-batch-scanner"
      service_account_name = "batch-scanner"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
        ]
        buckets = [
          [lookup(var.buckets_output, "verdict_manager", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "dp_scan_logs", null), "roles/storage.objectCreator"]
        ]
        topics = [
          lookup(var.topics_output, "verdict_manager_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", "null"), "roles/pubsub.publisher"]
        ]
      }
    }
    cloudsec_cloud_api_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-cloud-api-service"
      service_account_name = "cloud-api-service"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic"), "roles/pubsub.publisher"]
        ]
      }
    }
    cloudsec_xspm_scanner = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-xspm-scanner"
      service_account_name = "xspm-scanner"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cloudsec"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem_permissions_raw"), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "verdict_manager_topic", null),
          lookup(var.topics_output, "cloudsec-xspm-scanner-rules", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cloudsec-xspm-scanner-rules-sub", null),
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", null), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "verdict_manager_topic", null), "roles/pubsub.publisher"],
        ],
        buckets = [
          [lookup(var.buckets_output, "verdict_manager", null), "roles/storage.objectAdmin"],
        ]
      }
    }
    cloudsec_xspm_rules_sync_cron_job = {
      enabled              = local.enable_cortex_platform
      app_name             = "cloudsec-${var.lcaas}-xspm-rules-sync-job"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-xdr-wi-xspm-rule-sync-job"
      service_account_name = "xspm-rules-sync-job"
      workload_identity = {
        bq_job_user = true
        buckets = [
          [lookup(var.buckets_output, "verdict_manager",null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "dp_scan_logs", null), "roles/storage.objectCreator"]
        ]
      }
    }
  }
}