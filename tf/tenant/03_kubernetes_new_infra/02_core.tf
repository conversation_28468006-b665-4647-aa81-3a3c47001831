module "core_workload_identity" {
  source = "../../modules/workload_identity"
  for_each = {
    for app in local.core_apps :
    app.app_name => app
    if lookup(app, "enabled", true) && ! lookup(app, "skip_workload_identity", false)
  }
  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes
  }
  service_account_name = each.value.service_account_name
  mt_additional_group  = lookup(each.value, "mt_additional_group", "false")
  mt_dedicated_group   = lookup(each.value, "mt_dedicated_group", false)
  dedicated_group_name = lookup(each.value, "dedicated_group_name", "")
  project_id           = var.project_id
  wi_project_id        = local.workload_identity_project_id
  data                 = lookup(each.value, "workload_identity", {})
  namespace            = module.create_namespace.ns[local.st_namespace]
  viso_env             = var.viso_env
  lcaas                = var.lcaas
  create_kubernetes_sa = false
}

module "create_core_apps" {
  source = "../../modules/app"
  for_each = {
    for app, app_value in local.core_apps :
    app => app_value
    if lookup(app_value, "enabled", true)
  }
  app_name             = each.value.app_name
  cron_job             = lookup(each.value, "cron_job", {})
  only_cron            = lookup(each.value, "only_cron", false)
  deployment           = lookup(each.value, "deployment", {})
  hpa                  = lookup(each.value, "hpa", {})
  vsg                  = lookup(each.value, "vsg", {})
  namespace            = module.create_namespace.ns[local.st_namespace]
  pdb                  = lookup(each.value, "pdb", false)
  pool_tenant_creation = var.pool_tenant_creation
  service              = lookup(each.value, "service", {})
  service_account_name = each.value.service_account_name
  project_id           = var.project_id
  region               = var.region
  secrets              = lookup(each.value, "secrets", {})
  depends_on           = [
    module.core_workload_identity,
    module.dml_workload_identity,
    module.analytics_workload_identity,
  ]
}

locals {
  api_locals = {
      app_name                = "xdr-st-${var.lcaas}-api"
      service_account_name    = "api-pod"
      workload_identity = {
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
        #CRTX-92226
        monitoring_viewer = var.is_xdr || var.is_xsiam? true : false
        subscriptions = [
          lookup(var.subscriptions_output, "external_notifications_sub", null),
          lookup(var.subscriptions_output, "cortex_gateway_messages_processor_sub", null),
          lookup(var.subscriptions_output, "xsoar_artifacts_extraction_incident_notifications_sub", null),
          lookup(var.subscriptions_output, "permissions_auditing_sub", null),
          lookup(var.subscriptions_output, "vulnerability_and_compliance_scans_sub", null),
          lookup(var.subscriptions_output, "ap_issue_ingest_feedback_sub_vxp", null),
          lookup(var.subscriptions_output, "vulnerability_and_compliance_scans_sub_debug", null),
          lookup(var.subscriptions_output, "tech_support_file_retrieval_sub", null),
          lookup(var.subscriptions_output, "dp_finding_ingestion_errors_asm_sub", null),
          lookup(var.subscriptions_output, "ap_issue_ingestion_errors_asm_sub", null),
          lookup(var.subscriptions_output, "object_mirroring_service_sub", null),
          lookup(var.subscriptions_output, "dp_uai_asset_change_feed_uvem_vxp_sub", null),
          lookup(var.subscriptions_output, "dp_uai_asset_ingestion_errors_asm_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "chrome_app", null),
          lookup(var.topics_output, "external_logs_topic", null),
          lookup(var.topics_output, "lcaas_topic", null),
          lookup(var.topics_output, "slack_notification_topic", null),
          lookup(var.topics_output, "analytics_detection_hits", null),
          lookup(var.topics_output, "log_processor_topic", null),
          lookup(var.topics_output, "forensics_processor_topic", null),
          lookup(var.topics_output, "inventory_topic", null),
          lookup(var.topics_output, "task_processor_topic", null),
          lookup(var.topics_output, "playbook_execution_topic", null),
          lookup(var.topics_output, "alerts_fetcher", null),
          lookup(var.topics_output, "alerts_to_xsoar_topic", null),
          lookup(var.topics_output, "artifact_extraction_topic", null),
          lookup(var.topics_output, "vulnerability_and_compliance_scans", null),
          lookup(var.topics_output, "ipl_asset_changes_topic", null),
          lookup(var.topics_output, "edr_topic", null),
          lookup(var.topics_output, "dlq-ext-topic", null),
          lookup(var.topics_output, "external_integration_data_forwarder", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cloud_accounts", null),
          lookup(var.topics_output, "ap_issue_ingest_feedback", null),
          lookup(var.topics_output, "ap_issue_update", null),
          lookup(var.topics_output, "modification_topic", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
          lookup(var.topics_output, "uvem_protofinding_emits_topic", null),
          lookup(var.topics_output, "ap_issue_ingest_feedback", null),
          lookup(var.topics_output, "itdr_update_topic", null),
          lookup(var.topics_output, "cloud_onboarding_tasks", null),
          lookup(var.topics_output, "identity_risk_score_updates", null),
          lookup(var.topics_output, "email_data_submitted_hashes", null),
          lookup(var.topics_output, "cloud_accounts_full_discovery_topic", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dss_sync_notifier_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "dp_uai_asset_association_observations_topic", null),
          lookup(var.topics_output, "dp_uai_asset_groups_change_feed_topic", null),
          lookup(var.topics_output, "ap_issue_create", null),
          lookup(var.topics_output, "uvem_netscan_results_topic", null),
          lookup(var.topics_output, "object_mirroring_service_topic", null),
          lookup(var.topics_output, "health_alerts", null),
          lookup(var.topics_output, "dp_uai_asset_refresh", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_topic", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_dlq_topic", null),
        ]
        topics_mt = local.enable_secondary_playbook_mirroring ? [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"], ["high-availability-tenants-sync", local.primary_project_id]] :  [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        artifact_registry_permissions = true
        artifact_registry_download = true
        bq_admin = true
        bq = [[lookup(var.bq_output, "autocomplete"), "roles/bigquery.admin"]]
        buckets = [
            [lookup(var.buckets_output, "alert_original_bucket", null), "ReadAndWrite"],
            [lookup(var.buckets_output, "analytics_bq_export", null), "ReadAndWrite"],
            [lookup(var.buckets_output, "papi_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "rts_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "agent-uploads", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "broker_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "alerts-queue", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "distributions_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "feature-data_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "reports_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "lookups_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "redis-backup", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "ext_files_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "shared_bucket", null), "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "broker_secret_bucket", null), "roles/storage.objectCreator"],
            [lookup(var.buckets_output, "cold_storage_aggregated_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "cold_storage_raw_bucket", null),"roles/storage.admin"],
            [lookup(var.buckets_output, "cold-storage-quantums-raw", null),"roles/storage.admin"],
            [lookup(var.buckets_output, "alert_original_bucket", null), "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "xsiam_content_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "tim_indicators_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "user_exports_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "xsoar-migration", null), "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "xsoar_files", null), "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "vulnerability_and_compliance_scans", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "async_export_files_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "sfdc-upload-files", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "forensics_bucket", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "platform_data", null), "projects/${var.project_id}/roles/AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"],
            [lookup(var.buckets_output, "egress_raw_bucket", null), "AllowBucketsDeleteObject"],
            [lookup(var.buckets_output, "cloud_onboarding_templates", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "asset_inventory_bucket", null), "ReadAndWrite"],
            [lookup(var.buckets_output, "dp_asset_export", null), "roles/storage.objectCreator"],
            [lookup(var.buckets_output, "compliance_reports", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "archive_load", null), "roles/storage.admin"],
            [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
            [lookup(var.buckets_output, "xpanse_manual_scan_results_bucket", null), "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "xpanse_bucket", null), var.is_fedramp ? "ReadAndWrite" : "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "xsoar_alerts_artifacts", null), "AllowBucketsReadAccess"],
            [lookup(var.buckets_output, "egress_raw_bucket", null), "ReadAndWrite"],
        ]
        kms_crypto_keys = concat([
          [local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.broker-vm_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.hydra_kms_keyring, "roles/cloudkms.cryptoKeyEncrypter"],
          [local.broker-vm_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"],
          [local.tsf_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"],
          [local.tsf_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyEncrypterDecrypter"],
          [local.cts_kms_keyring, "roles/cloudkms.cryptoKeyEncrypter"]],
          local.enable_cortex_platform ? [[local.uvem_kms_keyring, "roles/cloudkms.cryptoKeyEncrypterDecrypter"]] : []
        )
        sa_key_members = concat(
          [["projects/${var.project_id}/serviceAccounts/prisma-console@${var.project_id}.iam.gserviceaccount.com", "roles/iam.serviceAccountKeyAdmin"]],
          var.egress_enabled ? [["projects/${var.project_id}/serviceAccounts/event-forwarding-viewer@${var.project_id}.iam.gserviceaccount.com", "roles/iam.serviceAccountKeyAdmin"]] : [])
        additional_project_roles = var.is_fedramp ? ["roles/cloudtrace.agent"] : ["roles/cloudprofiler.agent", "roles/cloudtrace.agent"]
        additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
      }
  }
  log_processor_workload_identity = {
    subscriptions = [
      lookup(var.subscriptions_output, "external_agent_management_messages_sub", null),
      lookup(var.subscriptions_output, "log_processor_sub", null),
      lookup(var.subscriptions_output, "log_processor_errors_sub", null),
      lookup(var.subscriptions_output, "forensics_processor_sub", null),
      lookup(var.subscriptions_output, "modification_sub", null),
      lookup(var.subscriptions_output, "high_availability_tenants_sync_sub", null),
      lookup(var.subscriptions_output, "agent_management_reported_issues_sub", null),
      lookup(var.subscriptions_output, "classification_mgmt_data_pattern_update_sub", null),
      lookup(var.subscriptions_output, "classification_mgmt_profile_update_sub", null),
      lookup(var.subscriptions_output, "classification_mgmt_global_settings_update_sub", null),
    ]
    topics =[
      lookup(var.topics_output, "log_processor_errors_topic", null),
      lookup(var.topics_output, "slack_notification_topic", null),
      lookup(var.topics_output, "lcaas_topic", null),
      lookup(var.topics_output, "alerts_fetcher", null),
      lookup(var.topics_output, "dml_script_results_topic", null),
      lookup(var.topics_output, "vulnerability_and_compliance_scans", null),
      lookup(var.topics_output, "modification_topic", null),
      lookup(var.topics_output, "edr_topic", null),
      lookup(var.topics_output, "log_processor_topic", null),
      lookup(var.topics_output, "ap_issue_upsert", null),
      lookup(var.topics_output, "dlq-ext-topic", null),
      lookup(var.topics_output, "cwp_scan_results", null),
      lookup(var.topics_output, "agent_management_reported_issues", null),
      lookup(var.topics_output, "high_availability_tenants_sync", null),
      lookup(var.topics_output, "agent_management_outgoing_external_integrations_topic", null),
    ]
    topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
    buckets = [
      [lookup(var.buckets_output, "agent-uploads", null), "roles/storage.admin"],
      [lookup(var.buckets_output, "alerts-queue", null), "ReadAndWrite"],
      [lookup(var.buckets_output, "vulnerability_and_compliance_scans", null), "roles/storage.objectCreator"],
      [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "agent-reports", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "vulnerability_and_compliance_scans", null), "roles/storage.admin"],
      [lookup(var.buckets_output, "cwp_scan_results", null), "roles/storage.admin"],
    ]
    bq_job_user = true
    read_session_user = true
    bq = [
      [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
      [lookup(var.bq_output, "forensics"), "roles/bigquery.dataEditor"]
    ]
    additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
    additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
  }
  fetcher_workload_identity = {
    bq = [
      [lookup(var.bq_output, "ds"), "roles/bigquery.admin"],
      [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"]
    ]
    bq_job_user = true
    read_session_user = true
    monitoring_viewer = true
    subscriptions = [
      lookup(var.subscriptions_output, "inr-ext-logs-sub", null),
      lookup(var.subscriptions_output, "alerts_fetcher_sub", null),
      lookup(var.subscriptions_output, "health_alerts_sub", null),
    ]
    topics = [
      lookup(var.topics_output, "slack_notification_topic", null),
      lookup(var.topics_output, "task_processor_topic", null),
      lookup(var.topics_output, "playbook_execution_topic", null),
      lookup(var.topics_output, "edr_topic", null),
      lookup(var.topics_output, "ap_issue_update", null),
      lookup(var.topics_output, "itdr_update_topic", null),
    ]
    topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
    buckets = [
      [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "agent-uploads", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "broker_bucket", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "alerts-queue", null), "roles/storage.admin"],
      [lookup(var.buckets_output, "ext_logs_bucket", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "ipl-edr-data", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "alert_original_bucket", null), "ReadAndWrite"],
      [lookup(var.buckets_output, "analytics_data", null), "ReadAndWrite"],
      [lookup(var.buckets_output, "xpanse_bucket", null), "AllowBucketsReadAccess"],
    ]
    kms_crypto_keys    = [[local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"]]
    additional_project_roles = var.is_fedramp ? ["roles/cloudtrace.agent"] : ["roles/cloudprofiler.agent", "roles/cloudtrace.agent"]
  }
  task_processor_workload_identity = {
    additional_sa_roles = [
      "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
    ]
    bq_job_user = true
    read_session_user = true
    bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "forensics"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"]
         ]
    subscriptions = [
      lookup(var.subscriptions_output, "task_processor_sub", null),
      lookup(var.subscriptions_output, "xsoar_artifacts_extraction_incident_notifications_sub", null),
    ]
    buckets  = [
      [lookup(var.buckets_output, "agent-uploads"), "roles/storage.admin"],
      [lookup(var.buckets_output, "feature-data_bucket"), "ReadAndWrite"],
      [lookup(var.buckets_output, "forensics_bucket"), "roles/storage.admin"],
        ]
    topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
    topics = [
      lookup(var.topics_output, "playbook_execution_topic", null),
      lookup(var.topics_output, "task_processor_topic", null),
      lookup(var.topics_output, "slack_notification_topic", null),
      lookup(var.topics_output, "artifact_extraction_topic", null),
      lookup(var.topics_output, "alerts_fetcher", null),
      lookup(var.topics_output, "itdr_update_topic", null),
      lookup(var.topics_output, "identity_risk_score_updates", null)
    ]
    additional_project_roles = var.is_fedramp ? ["roles/cloudtrace.agent"] : ["roles/cloudprofiler.agent", "roles/cloudtrace.agent"]
  }
  core_apps = {
    app_init = {
      app_name             = "xdr-st-${var.lcaas}-secdo-init"
      service_account_name = "secdo-init"
      mt_dedicated_group   = true
      workload_identity = {
        bq_admin = true
        buckets  = [
          [lookup(var.buckets_output, "alerts-queue"), "projects/${var.project_id}/roles/AllowBucketsReadAndWriteAccess"],
          [lookup(var.buckets_output, "alert_original_bucket"), "roles/storage.admin"],
          [lookup(var.buckets_output, "feature-data_bucket"), "roles/storage.admin"],
          [lookup(var.buckets_output, "cold_storage_aggregated_bucket"), "roles/storage.admin"],
          [lookup(var.buckets_output, "analytics_bq_export"), "roles/storage.admin"],
          [lookup(var.buckets_output, "platform_data"), "projects/${var.project_id}/roles/AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "migration_data"), "projects/${var.project_id}/roles/AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "cloud_onboarding_templates"), "projects/${var.project_id}/roles/AllowBucketsReadAndWriteAccess"],
          [lookup(var.buckets_output, "cloud_onboarding_templates"), "AllowBucketsDeleteObject"],
          [lookup(var.buckets_output, "asset_migrations_bucket"), "projects/${var.project_id}/roles/AllowBucketsReadAndWriteAccess"],
        ]
        bq = [[lookup(var.bq_output, "enums"), "roles/bigquery.dataEditor"],
              [lookup(var.bq_output, "autocomplete"), "roles/bigquery.admin"]]
        kms_crypto_keys    = [
          [local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.broker-vm_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"],
          [local.tsf_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyEncrypter"]
        ]
        subscriptions = [lookup(var.subscriptions_output, "external_notifications_sub", null)]
        topics        = [lookup(var.topics_output, "external_notifications_topic", null),
                         lookup(var.topics_output, "dp_uai_asset_groups_change_feed_topic", null)]
        topics_mt     = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
	additional_project_roles = concat(
	  var.is_fedramp ? [] : ["roles/cloudprofiler.agent"] ,
	  local.enable_cortex_platform ? ["projects/${var.project_id}/roles/${lookup(var.overrides, "create_and_attach_subs_role_id", "CreateAndAttachSubscriptions")}"] : []
	  )
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-secdo_init"
      }
    }
    agent_api = {
      enabled              = var.is_xdr || var.is_xsiam
      app_name             = "xdr-st-${var.lcaas}-agent-api"
      service_account_name = "agent-api"
      mt_dedicated_group   = true
      workload_identity = {
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
        bq = [[lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]]
        kms_crypto_keys = [
          [local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.tsf_kms_keyring, "roles/cloudkms.publicKeyViewer"],
        ]
        topics = [
          lookup(var.topics_output, "log_processor_topic", null),
          lookup(var.topics_output, "forensics_processor_topic", null),
          lookup(var.topics_output, "lcaas_topic", null),
          lookup(var.topics_output, "task_processor_topic", null),
          lookup(var.topics_output, "alerts_fetcher", null),
          lookup(var.topics_output, "dml_script_results_topic", null),
          lookup(var.topics_output, "vulnerability_and_compliance_scans", null),
          lookup(var.topics_output, "modification_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
          lookup(var.topics_output, "agent_management_reported_issues", null),
          lookup(var.topics_output, "agent_management_outgoing_external_integrations_topic", null),
        ]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        buckets  = [
          [lookup(var.buckets_output, "broker_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "agent-itdr-integrations", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "agent-itdr-ca-integrations", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "agent-uploads", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "tim_indicators_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "vulnerability_and_compliance_scans", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "cwp_scan_results", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "preprocessed_data_bucket", null), "ReadAndWrite"]
        ]
        secret_manager_secrets = concat(
          var.enable_itdr ? [[lookup(var.secret_manager_secrets_output, "itdr_weak_passwords_salt", ""), "roles/secretmanager.secretAccessor"]] : [],
        )
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-agent_api"
      }
    }
    email_artifacts_relay = {
      enabled              = local.enable_email_sku
      app_name             = "xdr-st-${var.lcaas}-email-artifacts-relay"
      service_account_name = "email-artifacts-relay"
      mt_dedicated_group   = true
      workload_identity = {
        bq = [[lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]]
        topics = [
          lookup(var.topics_output, "alerts_fetcher", null),
          lookup(var.topics_output, "email_data_attachments", null),
          lookup(var.topics_output, "email_security_alerts_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "email_data_sub", null),
          lookup(var.subscriptions_output, "email_data_attachments_sub", null),
          lookup(var.subscriptions_output, "email_data_submitted_hashes_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "ext_files_bucket", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "unknown_mail_attachments", null), "roles/storage.admin"],
        ]
        kms_crypto_keys = [
          [local.collection-security_kms_keyring, "roles/cloudkms.cryptoOperator"]
        ]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
      }
    }
    api = merge(local.api_locals, {
      mt_dedicated_group     = true
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-api"
      }
    })
    chat-api = {
      enabled              = !var.is_fedramp && ! var.is_xsoar && var.tb_licenses > 0
      app_name             = "xdr-st-${var.lcaas}-chat-api"
      service_account_name = "chat-api"
      mt_dedicated_group   = true
      workload_identity = {
        bq  = [[lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"]]
        bq_job_user = true
      },
    }
    agentix-hub = {
      enabled                = lookup(var.overrides, "enable_agentix_hub", false) || (local.enable_cortex_platform && local.is_agentix)
      app_name               = "xdr-st-${var.lcaas}-agentix-hub"
      service_account_name   = "agentix-hub"
      mt_dedicated_group     = true
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "ds_agentix", "ds_${var.lcaas}_agentix"), "roles/bigquery.dataEditor"],
        ],
        additional_project_roles = [
          "projects/${var.project_id}/roles/VertexAIEmbedderRole",
          "roles/browser",
          "roles/cloudprofiler.agent"
        ],
      }
    }
    mcp = {
      enabled                = local.enable_cortex_mcp
      app_name               = "xdr-st-${var.lcaas}-mcp"
      service_account_name   = "mcp-server"
      skip_workload_identity = true
      mt_dedicated_group     = true
    }
    chrome_app = {
      enabled              = !var.is_xsoar
      app_name             = "xdr-st-${var.lcaas}-chrome-app"
      service_account_name = "chrome-app"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "temp_tables"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "platform_compliance_reports"), "roles/bigquery.admin"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "reports_bucket", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "compliance_reports", null), "roles/storage.admin"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "chrome_app", null),
        ]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-chrome_app"
      }
    }
    engine_hub = {
      enabled = var.enable_xsoar_shared_components
      app_name             = "xdr-st-${var.lcaas}-engine-hub"
      service_account_name = "engine-hub"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "xsoar_files", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_default_runner", null), "roles/storage.objectAdmin"],

        ]
        additional_project_roles = [
          "roles/cloudprofiler.agent",
        ]
      }
    }
    fetcher = {
      enabled              = !var.is_metro_tenant && ! var.is_xsoar
      app_name             = "xdr-st-${var.lcaas}-fetcher"
      service_account_name = "fetcher"
      mt_dedicated_group   = true
      workload_identity = local.fetcher_workload_identity
    }
    frontend = {
      app_name             = "xdr-st-${var.lcaas}-frontend"
      service_account_name = "frontend"
      mt_additional_group  = local.marketplace_bucket_name
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "agent-uploads", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "broker_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "papi_bucket", null), "AllowBucketsReadAccess"],
        ]
        kms_crypto_keys = [[local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"]]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        bq = [[lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]]
        bq_job_user = true
        read_session_user = true
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent", "roles/cloudtrace.agent"]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-frontend"
      }
    }
    dp-uai-assets-legacy = {
      enabled = var.is_xsiam
      app_name             = "xdr-st-${var.lcaas}-dp-uai-assets-legacy"
      service_account_name = "dp-uai-assets-legacy"
      workload_identity = {
        buckets = [[lookup(var.buckets_output, "asset_inventory_bucket", null), "AllowBucketsReadAccess"]]
        subscriptions = [lookup(var.subscriptions_output, "ipl_asset_changes_sub", null)]
        topics = [lookup(var.topics_output, "ipl_asset_changes_topic", null)]
        bq_admin = true
      }
    }
    log_processor = {
      enabled              = !var.is_metro_tenant && (var.is_xdr || var.is_xsiam)
      app_name             = "xdr-st-${var.lcaas}-log-processor"
      service_account_name = "log-processor"
      mt_dedicated_group   = true
      workload_identity = local.log_processor_workload_identity
    }
    mega_processor = {
      enabled              = var.is_metro_tenant
      # this deployment is: fetcher + log-processor + task-processor + alerts-emitter
      app_name             = "xdr-st-${var.lcaas}-mega-processor"
      service_account_name = "mega-processor"
      mt_dedicated_group   = false
      workload_identity = {
        bq_job_user = true
        read_session_user = true
        monitoring_viewer = true
        bq = distinct(concat(
          local.log_processor_workload_identity.bq,
          local.task_processor_workload_identity.bq,
          local.fetcher_workload_identity.bq,
          local.analytics_alerts_emitter_workload_identity.bq,
        )),
        subscriptions = distinct([for item in concat(
          local.log_processor_workload_identity.subscriptions,
          local.task_processor_workload_identity.subscriptions,
          local.fetcher_workload_identity.subscriptions,
          local.analytics_alerts_emitter_workload_identity.subscriptions,
        ): item if item != null]),
        topics = distinct([for item in concat(
          local.log_processor_workload_identity.topics,
          local.task_processor_workload_identity.topics,
          local.fetcher_workload_identity.topics,
          local.analytics_alerts_emitter_workload_identity.topics,
        ): item if item != null]),
        topics_mt = distinct(concat(
          local.log_processor_workload_identity.topics_mt,
          local.task_processor_workload_identity.topics_mt,
          local.fetcher_workload_identity.topics_mt,
          local.analytics_alerts_emitter_workload_identity.topics_mt,
        )),
        buckets = distinct([for item in concat(
          local.log_processor_workload_identity.buckets,
          local.task_processor_workload_identity.buckets,
          local.fetcher_workload_identity.buckets,
          local.analytics_alerts_emitter_workload_identity.buckets,
        ): item if item != null]),
        kms_crypto_keys = distinct(concat(
          local.fetcher_workload_identity.kms_crypto_keys,
          local.analytics_alerts_emitter_workload_identity.kms_crypto_keys,
        ))
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-mega_processor"
      }
    }
    metrics_collector = {
      app_name             = "xdr-st-${var.lcaas}-metrics"
      service_account_name = "metrics"
      mt_dedicated_group   = true
      workload_identity = {
        bq_job_user              = true
        bq_list_jobs             = true
        compute_snapshots_viewer = true
        monitoring_viewer        = true
        bq = concat([[lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"]],
          [[lookup(var.bq_output, "ds"), var.is_xsiam || var.is_xpanse ? "roles/bigquery.dataEditor" : "roles/bigquery.dataViewer"]],
          [[lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"]],
          var.is_xsiam ? [[lookup(var.bq_output, "external_data"), "roles/bigquery.dataViewer"]] : []
        )
        buckets = [
          [lookup(var.buckets_output, "broker_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "agent-uploads", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "alerts-queue", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "mysql-backup", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "egress_raw_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "cold_storage_raw_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "cold-storage-quantums-raw", null),"AllowBucketsReadAccess"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "bq_stats_sub", null),
        ]
        kms_crypto_keys    = [[local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"]]
        additional_project_roles = concat(["roles/monitoring.metricWriter"], var.is_fedramp ? [] : ["roles/cloudprofiler.agent"])
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-metrics"
      }
    }
    notifier = {
      app_name             = "xdr-st-${var.lcaas}-notifier"
      service_account_name = "notifier"
      workload_identity = {
        bq = [[lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]]
        topics = [lookup(var.topics_output, "slack_notification_topic", null)]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        # handle the dup here after CRTX-185413 (handle duplications in (subscriptions) permissions correctly) is fixed
        subscriptions = concat(
          [
            lookup(var.subscriptions_output, "log_forwarding_sub", null),
            lookup(var.subscriptions_output,"mgmt_audit_notifier_sub", null),
            lookup(var.subscriptions_output,"notifications_notifier_sub", null),
            lookup(var.subscriptions_output, "slack_notification_sub", null),
            lookup(var.subscriptions_output, "mail_events_sub", null),
            lookup(var.subscriptions_output, "ap-communication-sub", null),
          ],
          var.is_fedramp ? [] : [
            lookup(var.subscriptions_output, "mail_events_sub", null),
          ]
        )
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-notifier"
      }
    }
    overseer = {
      app_name             = "xdr-st-${var.lcaas}-overseer"
      service_account_name = "overseer"
      mt_dedicated_group   = true
      workload_identity = {
        bq = [[lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]]
        buckets = [
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "agent-uploads", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "broker_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "xsiam_content_bucket", null), "AllowBucketsReadAccess"],
        ]
        kms_crypto_keys = [[local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"]]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-overseer"
      }
    }
    pb_runner_v2 = {
      enabled     = var.enable_xsoar_shared_components
      app_name             = "xdr-st-${var.lcaas}-pb-runner-v2"
      service_account_name = "pb-runner-v2"
      workload_identity = {
        bq_job_user = true
        firestore_user = local.enable_playbook_mirroring
        shared_storage = local.enable_playbook_mirroring
        primary_project_id = local.primary_project_id
        buckets = [
          [lookup(var.buckets_output, "xsoar_files", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_objects_archiving", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar-trimmed-alerts-bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_default_runner", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "mgmt_audit_notifier", null),
          lookup(var.topics_output, "xsoar_indicator_enrichment_results", null),
          lookup(var.topics_output, "xsoar_indicator_score_updates", null),
          lookup(var.topics_output, "xsoar_indicator_timeline_comments", null),
          lookup(var.topics_output, "xsoar_relationships", null),
          lookup(var.topics_output, "xsoar_tags_field_values", null),
          lookup(var.topics_output, "ha_mirroring", null),
          lookup(var.topics_output, "xsoar_tags_field_values", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_topic", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_dlq_topic", null),
          lookup(var.topics_output, "object_mirroring_service_topic", null),
        ]
        bq = [
          [lookup(var.bq_output, "ds_xsoar", "ds_${var.lcaas}_xsoar"), "roles/bigquery.dataEditor"],
        ],
        additional_project_roles = var.is_fedramp ? ["projects/${var.project_id}/roles/VertexAIEmbedderRole"] : ["roles/cloudprofiler.agent", "projects/${var.project_id}/roles/VertexAIEmbedderRole"],
      }
    }
    saas_collection = {
      enabled              = (!var.is_metro_tenant && ! var.is_xsoar && var.tb_licenses > 0) || local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-saas-collection"
      service_account_name = "saas-collection"
      mt_dedicated_group   = true
      workload_identity = {
        bq_job_user = true
        bq = [[lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]]
        topics = [
          lookup(var.topics_output, "health_alerts", null),
          lookup(var.topics_output, "edr_topic", null),
          lookup(var.topics_output, "dlq-ext-topic", null),
        ]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        subscriptions = [
          lookup(var.subscriptions_output, "collection_responses_sub", null),
          lookup(var.subscriptions_output, "collection_priority_sub", null),
          lookup(var.subscriptions_output, "cloud_accounts_log_collection_sub", null),
        ]
        buckets = [[lookup(var.buckets_output, "archive_load", null), "ReadAndWrite"]]
        kms_crypto_keys = [
          [local.broker-vm_kms_keyring, "roles/cloudkms.publicKeyViewer"],
          [local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyEncrypter"]
        ]
        additional_project_roles = concat(var.is_fedramp ? [] : ["roles/cloudprofiler.agent"],["projects/${var.project_id}/roles/${lookup(var.overrides, "create_and_attach_subs_role_id", "CreateAndAttachSubscriptions")}"])
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-saas_collection"
      }
    }
    scortex = {
      enabled              = var.enable_pipeline && !var.is_metro_tenant
      app_name             = "xdr-st-${var.lcaas}-scortex"
      service_account_name = "scortex"
      mt_dedicated_group   = true
    }
    task_processor = {
      enabled              = ! var.is_xsoar && !var.is_metro_tenant
      app_name             = "xdr-st-${var.lcaas}-task-processor"
      service_account_name = "task-processor"
      mt_dedicated_group = true
      workload_identity = local.task_processor_workload_identity
    }
    xcloud_ingester = {
      enabled              = var.enable_xcloud || local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-xcloud-ingester"
      service_account_name = "xcloud-ingester"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "inventory_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "inventory_bucket", null), "roles/storage.legacyBucketReader"],
          [lookup(var.buckets_output, "inventory_bucket", null), "roles/storage.objectViewer"],
        ]
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    xsoar = {
      enabled              = var.enable_xsoar_shared_components
      app_name = "xdr-st-${var.lcaas}-xsoar"
      service_account_name = "xsoar-pod"
      mt_dedicated_group   = true
      mt_additional_group  = local.marketplace_bucket_name
      workload_identity = {
        pubsub_viewer = true
        bq_job_user = true
        additional_project_roles = [
          "roles/cloudprofiler.agent",
          "roles/cloudtrace.agent",
          "projects/${var.project_id}/roles/AllowArtifactRegistryDownloadAndUpload",
          "projects/${var.project_id}/roles/VertexAIEmbedderRole"
        ]
        topics = [
          lookup(var.topics_output, "mgmt_audit_notifier", null),
          lookup(var.topics_output, "notifications_notifier", null),
          lookup(var.topics_output, "alerts_fetcher", null),
          lookup(var.topics_output, "xsoar_artifacts_extraction_incident_notifications", null),
          lookup(var.topics_output, "xsoar_tim_indicator_failures_topic", null),
          lookup(var.topics_output, "xsoar_incidents_to_create_topic_dlq", null),
          lookup(var.topics_output, "xsoar_incidents_to_create_topic", null),
          lookup(var.topics_output, "xsoar_indicator_enrichment_results", null),
          lookup(var.topics_output, "xsoar_indicator_score_updates", null),
          lookup(var.topics_output, "xsoar_indicator_timeline_comments", null),
          lookup(var.topics_output, "xsoar_relationships", null),
          lookup(var.topics_output, "xsoar_tags_field_values", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_topic", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_dlq_topic", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
          lookup(var.topics_output, "object_mirroring_service_topic", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "tim_indicators_bucket", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "papi_bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_files", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_objects_archiving", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_alerts_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar-trimmed-alerts-bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_incidents_to_create", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_default_runner", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "playbook_execution_sub", null),
          lookup(var.subscriptions_output, "alerts_to_xsoar_sub", null),
          lookup(var.subscriptions_output, "artifact_extraction_sub", null),
          lookup(var.subscriptions_output, "xsoar_tim_indicator_failures_sub", null),
          lookup(var.subscriptions_output, "xsoar_incidents_to_create_sub_dlq", null),
          lookup(var.subscriptions_output, "xsoar_incidents_to_create_sub", null),
          lookup(var.subscriptions_output, "xsoar_indicator_enrichment_results_sub", null),
          lookup(var.subscriptions_output, "xsoar_indicator_score_updates_sub", null),
          lookup(var.subscriptions_output, "xsoar_indicator_timeline_comments_sub", null),
          lookup(var.subscriptions_output, "xsoar_relationships_sub", null),
          lookup(var.subscriptions_output, "xsoar_tags_field_values_sub", null),
          lookup(var.subscriptions_output, "xsoar_cloud_accounts_sub", null),
          lookup(var.subscriptions_output, "ha_mirroring_sub", null),
        ],
        bq = [
          [lookup(var.bq_output, "ds_xsoar", "ds_${var.lcaas}_xsoar"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]
        ],
        firestore_user = local.enable_playbook_mirroring
        shared_storage = local.enable_playbook_mirroring
        primary_project_id = local.primary_project_id
      }
    }
    xsoar_migration = {
      enabled              = var.is_xsoar_6_migration
      app_name = "xdr-st-${var.lcaas}-xsoar-migration"
      service_account_name = "xsoar-mig-pod"
      mt_dedicated_group = true
      workload_identity = {
        pubsub_viewer = true
        bq_job_user = true
        additional_project_roles = [
          "roles/cloudprofiler.agent",
          "projects/${var.project_id}/roles/AllowArtifactRegistryDownloadAndUpload"
        ]
        topics = [
          lookup(var.topics_output, "mgmt_audit_notifier", null),
          lookup(var.topics_output, "notifications_notifier", null),
          lookup(var.topics_output, "alerts_fetcher", null),
          lookup(var.topics_output, "xsoar_artifacts_extraction_incident_notifications", null),
          lookup(var.topics_output, "xsoar_tim_indicator_failures_topic", null)
        ]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        buckets = [
          [lookup(var.buckets_output, "tim_indicators_bucket", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "papi_bucket", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "xsoar-migration", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "xsoar_files", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "xsoar_alerts_artifacts", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "xsoar-trimmed-alerts-bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_incidents_to_create", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_objects_archiving", null), "roles/storage.admin"]
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "playbook_execution_sub", null),
          lookup(var.subscriptions_output, "alerts_to_xsoar_sub", null),
          lookup(var.subscriptions_output, "artifact_extraction_sub", null),
          lookup(var.subscriptions_output, "xsoar_tim_indicator_failures_sub", null),
        ],
        bq = [
          [lookup(var.bq_output, "ds_xsoar", "ds_${var.lcaas}_xsoar"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
        ],
      }
    }
    xsoar_content = {
      enabled              = var.enable_xsoar_shared_components
      app_name                  = "xdr-st-${var.lcaas}-xsoar-content"
      service_account_name      = "xsoar-content"
      mt_additional_group       = local.marketplace_bucket_name
      workload_identity = {
        source_code_writer = var.viso_env == "prod-gv" ? [] : [[local.parent_project_id, "roles/source.writer"]]
        topics = [lookup(var.topics_output, "mgmt_audit_notifier", null)]
        buckets = [
          [lookup(var.buckets_output, "xsoar_files", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar-migration", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "xsoar-migration", null), "roles/storage.objectCreator"],
          [lookup(var.buckets_output, "xsoar-marketplace-overridable", null), "roles/storage.objectAdmin"]
        ],
        topics = [
          lookup(var.topics_output, "mgmt_audit_notifier", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_topic", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_dlq_topic", null),
       ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent", "projects/${var.project_id}/roles/VertexAIEmbedderRole"]
      }
    }
    xsoar_init = {
      enabled              = var.enable_xsoar_shared_components
      app_name = "xdr-st-${var.lcaas}-xsoar-init"
      service_account_name = "xsoar-pod-init"
      workload_identity = {
        bq_job_user = true
        buckets = [
          [lookup(var.buckets_output, "xsoar_files", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "tim_indicators_bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_alerts_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "xsoar_objects_archiving", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "xsoar-migration", null), "roles/storage.admin"]
        ]
        topics = [
          lookup(var.topics_output, "mgmt_audit_notifier", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_topic", null),
          lookup(var.topics_output, "xsoar_playbook_tasks_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_playbook_runs_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_relationships_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_execution_metrics_bq_dlq_topic", null),
          lookup(var.topics_output, "xsoar_indicators_bq_replica_dlq_topic", null),
        ],
        bq = [
          [lookup(var.bq_output, "ds_xsoar", "ds_${var.lcaas}_xsoar"), "roles/bigquery.dataEditor"],
        ],
        source_code_writer = var.viso_env == "prod-gv" ? [] : [[local.parent_project_id, "roles/source.writer"]],
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    xsoar_workers_router = {
      enabled              = local.enable_xsoar_workers
      app_name             = "xdr-st-${var.lcaas}-xsoar-workers-router"
      service_account_name = "xsoar-workers-router"
    }
    xsoar_workers_gateway = {
      enabled              = local.enable_xsoar_workers
      app_name             = "xdr-st-${var.lcaas}-xsoar-workers-gateway"
      service_account_name = "xsoar-workers-gateway"
    }
    xpanse_rcs_results_processor = {
      enabled              = var.enable_asm && !var.is_fedramp
      app_name             = "xdr-st-${var.lcaas}-rcs-results-processor"
      service_account_name = "rcs-results-processor"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "xpanse_manual_scan_results_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "xpanse_manual_scan_results_topic", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "xpanse_manual_scan_results_bucket", null), "AllowBucketsReadAccess"],
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    email_security_alerts = {
      enabled              = local.enable_email_sku
      app_name             = "email-security-alerts"
      service_account_name = "email-security-alerts"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "email_security_alerts_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "email_security_policy_actions_topic", null),
        ]
      }
    }
    email_security_actions = {
      enabled              = local.enable_email_sku
      app_name             = "email-security-actions"
      service_account_name = "email-security-actions"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "email_security_policy_actions_sub", null),
          lookup(var.subscriptions_output, "email_security_actions_commands_sub", null),
          lookup(var.subscriptions_output, "email_security_retry_commands_sub", null),
          lookup(var.subscriptions_output, "email_security_commands_responses_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "email_security_actions_commands_topic", null),
          lookup(var.topics_output, "email_security_retry_commands_topic", null),
          lookup(var.topics_output, "email_security_quick_commands_topic", null),
        ]
      }
    }
    email_security_runner = {
      enabled              = local.enable_email_sku
      app_name             = "email-security-runner"
      service_account_name = "email-security-runner"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "email_security_quick_commands_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "email_security_commands_responses_topic", null),
        ]
      }
    }
    agent_mgmt_processor = {
      app_name             = "agent-mgmt-processor"
      service_account_name = "agent-mgmt-processor"
      workload_identity    = {
        subscriptions = [
          lookup(var.subscriptions_output, "agent_management_incoming_integrations_module_tag_changes_sub", null),
        ]
      }
    }
  }
  marketplace_bucket_name = var.pool_tenant_creation || (var.is_xdr && var.product_code == "legacy")  ? "false" : var.product_type == "cloud" || var.product_type == "xdr" ? "xsiam-content" : "${var.product_type}-content"
  broker-vm_kms_keyring = "xdr-kms-project-${var.multi_project_postfix}-01/${var.encrypt_fas_keyring_location}/broker-vm/${var.lcaas}"
  config_map            = ["${var.lcaas}-configmap", "${var.lcaas}-configmap-feature-flags"]
  enable_xsoar_workers  = lookup(var.overrides, "enable_xsoar_workers", false)
  parent_project_id     = var.customer_dev_tenant ? var.parent_project_id : var.project_id
  hydra_kms_keyring     = "xdr-kms-project-${var.multi_project_postfix}-01/${var.encrypt_fas_keyring_location}/hydra-tenant-ring/${var.lcaas}"
  tenants_kms_keyring   = "xdr-kms-project-${var.multi_project_postfix}-01/${var.encrypt_fas_keyring_location}/tenants/${var.lcaas}"
  collection-security_kms_keyring   = "xdr-kms-project-${var.multi_project_postfix}-01/${var.encrypt_fas_keyring_location}/collection-security/${var.lcaas}"
  email-bi-secure-key   = "${var.project_id}/${var.region}/email-crypto-ring/email-bi-secure-cache"
  cts_kms_keyring       = "xdr-kms-project-${var.multi_project_postfix}-01/${var.encrypt_fas_keyring_location}/cts-security/${var.lcaas}"
  tenant_kms_keyring    = "xdr-kms-project-${var.multi_project_postfix}-01/${var.encrypt_fas_keyring_location}/${var.encrypt_fas_keyring_name}/${var.lcaas}"
  tsf_kms_keyring       = "${var.project_id}/${var.region}/tsf-encrypt-decrypt/enc-dec"
  uvem_kms_keyring      = "${var.project_id}/${var.region}/uvem_application_layer_ring/uvem_${var.lcaas}"
  sp_jwt_kms_keyring    = "${var.project_id}/${var.region}/sp-jwt-keyring/sp-jwt"
  xsoar_ng_shared_key   = var.customer_dev_tenant ? join(",", data.terraform_remote_state.xsoar-ng-parent-state[0].outputs.xsoar_ng_prod_key) : element(concat(random_password.xsoar_ng_prod_key.*.result, tolist([""])), 0)
  aws_member = var.is_xsoar_6_migration && !var.is_xsoar_onprem_migration ? "principalSet://iam.googleapis.com/${google_iam_workload_identity_pool.aws.0.name}/attribute.aws_role/arn:aws:sts::${var.xsoar_6_account_id}:assumed-role/iam-${var.xsoar_6_sn}-${var.xsoar_6_env}" : ""
  aws_attribute_condition = "attribute.aws_role==\"arn:aws:sts::${var.xsoar_6_account_id}:assumed-role/iam-${var.xsoar_6_sn}-${var.xsoar_6_env}\""
  is_agentix            = var.product_code == "agentix"
  enable_cortex_mcp  = lookup(var.overrides, "enable_cortex_mcp", false)
}

# todo(yfried): replace with tf modules (deployment+configmap)
resource "helm_release" "log-forwarding" {
  # FIXME: A temporary hack until migration completed
  # chart     = "${path.module}/files/helm/log-forwarding"
  chart     = "../03_kubernetes/files/helm/log-forwarding"
  name      = "log-forwarding-${var.lcaas}"
  namespace = local.log_forwarding_namespace
  provider  = helm.log-forwarding
  max_history = 1

  set {
    type  = "string"
    name  = "lcaasId"
    value = var.lcaas
  }
  set {
    name  = "log_forwarding_namespace"
    value = local.log_forwarding_namespace
  }

  set {
    name  = "backend_version"
    value = lookup(var.overrides, "log_forwarding_image_tag", var.backend_version)
  }

  set {
    name  = "backend_image_name"
    value = var.backend_image_name
  }
  set {
    name = "docker_image_gar_location"
    value = var.docker_image_gar_location
  }
  set {
    name  = "redis_addr"
    value = var.is_perf_tenant ? "performance-redis-svc" : "${var.viso_env}-redis-svc"
  }
  set {
    name  = "log_forwarding_project_name"
    value = "xdr-log-forwarding-${var.viso_env}-01"
  }
  set {
    name  = "project_prefix"
    value = var.project_prefix
  }
  set {
    type  = "string"
    name  = "nameOverride"
    value = "log-forwarding-${var.lcaas}"
  }
}

resource "kubernetes_manifest" "scaledobject_ns_lf_scaledobject" {
  provider = kubernetes.log-forwarding
  manifest = {
    "apiVersion" = "keda.sh/v1alpha1"
    "kind" = "ScaledObject"
    "metadata" = {
      "labels" = {
        "deploymentName" = "log-forwarding-${var.lcaas}"
      }
      "name" = "lf-scaledobject-${var.lcaas}"
      "namespace" = local.log_forwarding_namespace
    }
    "spec" = {
      "cooldownPeriod" = 3600
      "maxReplicaCount" = 5
      "minReplicaCount" = 0
      "pollingInterval" = 30
      "scaleTargetRef" = {
        "name" = "log-forwarding-${var.lcaas}"
      }
      "triggers" = [
        {
          "authenticationRef" = {
            "name" = "${var.viso_env}-keda-trigger-auth-gcp-credentials"
          }
          "metadata" = {
            "subscriptionName" = "lf-data-${var.lcaas}-sub"
            "mode"             = "SubscriptionSize"
            "value"            = "1"
          }
          "type" = "gcp-pubsub"
        },
      ]
    }
  }
}


# Workload identity federation for xsoar 6 migration

resource "google_iam_workload_identity_pool" "aws" {
  count                     = var.is_xsoar_6_migration && !var.is_xsoar_onprem_migration ? 1 : 0
  workload_identity_pool_id = "xsoar-mig"
  project                   = var.project_id
  lifecycle {
    prevent_destroy = true
  }
}

resource "google_iam_workload_identity_pool_provider" "aws-provider" {
  count                              = var.is_xsoar_6_migration && !var.is_xsoar_onprem_migration ? 1 : 0
  depends_on                         = [google_iam_workload_identity_pool.aws]
  project                            = var.project_id
  workload_identity_pool_id          = google_iam_workload_identity_pool.aws.0.workload_identity_pool_id
  workload_identity_pool_provider_id = "aws-mig"
  display_name                       = "aws-mig"
  description                        = "AWS identity pool provider for hosted aws migration"
  attribute_condition                = local.aws_attribute_condition
  disabled                           = false
  aws {
    account_id = var.xsoar_6_account_id
  }
  lifecycle {
    prevent_destroy = true
  }

}

data "google_service_account" "xsoar-migration-sa" {
  count      = var.is_xsoar_6_migration ? 1 : 0
  depends_on = [module.core_workload_identity]
  account_id = "xsoar-mig-pod@${var.project_id}.iam.gserviceaccount.com"
}

# Service account impersonation

resource "google_service_account_iam_member" "wif_members" {
  count  = var.is_xsoar_6_migration && !var.is_xsoar_onprem_migration ? 1 : 0
  service_account_id = data.google_service_account.xsoar-migration-sa.0.name
  role = "roles/iam.workloadIdentityUser"
  member = local.aws_member
}

# onprem member

data "google_service_account" "xsoar-onprem-sa" {
  count      = var.is_xsoar_6_migration && var.is_xsoar_onprem_migration ? 1 : 0
  depends_on = [module.core_workload_identity]
  account_id = "xsoar-onprem@${var.project_id}.iam.gserviceaccount.com"
}

resource "google_service_account_iam_member" "xsoar_onprem_grant" {
  count  = var.is_xsoar_6_migration && var.is_xsoar_onprem_migration ? 1 : 0
  service_account_id = data.google_service_account.xsoar-onprem-sa.0.name
  role = "roles/iam.serviceAccountKeyAdmin"
  member = "serviceAccount:xsoar-mig-pod@${var.project_id}.iam.gserviceaccount.com"
}

module "harvester_workload_identity" {
  source = "../../modules/workload_identity"
  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes.harvester
  }
  service_account_name = "collector-${var.lcaas}"
  project_id           = var.project_id
  wi_project_id        = "xdr-harvester-${var.viso_env}-01"
  data                 = {
    buckets = [
      [lookup(var.buckets_output, "ext_logs_bucket", null), "projects/${var.project_id}/roles/AllowBucketsReadAndWriteAccess"],
      [lookup(var.buckets_output, "ext_files_bucket", null), "projects/${var.project_id}/roles/AllowBucketsReadAndWriteAccess"]
    ]
    topics = [
      lookup(var.topics_output, "collection_responses_topic", null),
      lookup(var.topics_output, "collection_priority_topic", null),
      lookup(var.topics_output, "external_logs_topic", null),
      lookup(var.topics_output, "inventory_topic", null),
      lookup(var.topics_output, "dlq-collection-topic", null),
      lookup(var.topics_output, "msft_lifecycle_notification_topic", null),
      lookup(var.topics_output, "msft_task_notification_topic", null),
      lookup(var.topics_output, "google_task_notification_topic", null),
    ]
    additional_project_roles = ["projects/${var.project_id}/roles/ServicesUsage"]
    kms_crypto_keys = [
      [local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"]
    ]
  }
  namespace            = lookup(var.overrides, "collection_namespace", local.enable_cortex_platform ? "collection-platform" : "collection")
  viso_env             = var.viso_env
}
