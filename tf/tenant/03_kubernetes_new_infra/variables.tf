variable "viso_new_platform" { default = false }
variable "agentconfig_encryption_key" {}
variable "backend_image_name" {}
variable "bq_output" {}
variable "default_byok_key_id" { default = "" }
variable "sp_jwt_key_id" {}
variable "buckets_output" {}
variable "secret_manager_secrets_output" {}
variable "certs_project" {}
variable "cold_retention" {}
variable "cronus_deploy_idle_resources" {
  type = bool
}
variable "deploy_metrus" {
  type = bool
  default = true
}

variable "docker_image_gar_location" {}
variable "egress_enabled" {}
variable "enable_asm" {
  type    = bool
  default = false
}
variable "enable_byok" {
  type = bool
}
variable "enable_xcloud" { default = false }
variable "encrypt_fas_keyring_location" {}
variable "encrypt_fas_keyring_name" {}
variable "enable_pipeline" {}
variable "external_fqdn" {}
variable "firestore_access_service_key" {}
variable "forensics" { default = false }
variable "host_project" {}
variable "is_perf_tenant" {
  type = bool
}
variable "is_xpanse" {}
variable "is_xsiam" {}
variable "is_xsoar" {}
variable "is_xdr" {}
variable "prod_spec" {
  type = bool
}
variable "lcaas" {}
variable "logout_url" {}
variable "megatron_xdr" {}
variable "multi_project_postfix" {}
variable "overrides" { default = {} }
variable "parent_project_id" {}
variable "pool_tenant_activation" {}
variable "pool_tenant_creation" {}
variable "pool_tenant" {}
variable "product_code" { default = "legacy" }
variable "product_type" { default = "" }
variable "product_tier" { default = "" }
variable "project_id" {}
variable "project_number" {}
variable "project_prefix" {}
variable "rbacconf_base_uri" {}
variable "redis_auth" { default = true }
variable "redis_split" { default = false }
variable "region" {}
variable "report_encryption_key" {}
variable "terraform_iam" {}
variable "service_account_key_output" {}
variable "slackconf_hydra_redirect" {}
variable "subscriptions_output" {}
variable "tenant_type" {}
variable "topics_output" {}
variable "viso_env" {}
variable "wildfire_apikey" {}
variable "hashed_wildfire_key" { default = "dummy_string" }
variable "hashed_monitoring_key" { default = "dummy_string" }
variable "hashed_xdr_http_token" { default = "dummy_string" }
variable "hashed_autofocus_key" {}
variable "xcloud_standalone_deployment" { default = false }
variable "xdr_auth_token" {}
variable "xdr_http_collection_token" {}
variable "backend_version" {}
variable "xdr_env" {}
variable "xdr_id" {}
variable "tb_licenses" {}
variable "is_xsoar_6_migration" {default = false}
variable "is_xsoar_onprem_migration" { default = false}
variable "xsoar_6_sn" { default = ""}
variable "xsoar_6_migration_token" {
  default =  "admin"
  sensitive = true
}
variable "xsoar_6_account_id" { default =  ""}
variable "xsoar_6_env" { default = ""}
variable "zone" {}
variable "enable_twistlock" {
  default = false
}
variable "pdb" {
  type    = list(string)
  default = []
}
variable "rbacconf_base_uri_uat" {
  default = "https://rbac-qa-uat.qa.appsvc.paloaltonetworks.com/auth/rbac"
}
variable "xpanse_asset_feedback_credentials_api_key" {
  type    = string
  default = ""
}
variable "xpanse_tenant_credentials_client_id" {
  type    = string
  default = ""
}
variable "xpanse_tenant_credentials_client_secret" {
  type    = string
  default = ""
}
variable "enable_xsoar_shared_components" {}
variable "is_fedramp" {
  type = bool
  default = false
}
variable "customer_dev_tenant" {}
variable "enable_custom_kube_dns" {
  type = bool
  default = true
}
variable "enable_xdr_agent_daemon" {
  type = bool
  default = false
}

variable "gke_location" {
  type = string
}

variable "regional_kubernetes" {
  type    = bool
  default = false
}

variable "multi_zoned_nodepools" {
      type = bool
      default = false
}

variable "is_metro_tenant" {}
variable "metro_host_project_id" { default = "" }
variable "metro_host_id" { default = "" }
variable "metro_tenant_index" {
  type = number
  default = 0
}
variable "metro_host_zone" { default = "" }

variable "enable_email_artifacts_relay" {
  default = false
  type = bool
}

variable "app_images" {
  type = map(string)
}

variable "collection_enc_key" {}
variable "twistlock_defender_cluster_id" {}
variable "twistlock_defender_ws_address" {}

variable "enable_cloud_posture" {
  type = bool
  default = false
}

variable "enable_cloud_appsec" {
  type = bool
  default = false
}

variable "enable_assured_workloads" { default = false }

variable "enable_itdr" {
  type = bool
  default = false
}

locals {
  env_type                           = var.viso_env == "dev" ? "dev" : "prod"
  lf_topic                           = "lf-data-${var.lcaas}"

  docker_image_gar_location          = var.viso_env == "dev" ? "us-docker.pkg.dev/xdr-registry-dev-01" : "us-docker.pkg.dev/xdr-registry-prod-us-01"
  xcloud_redis_standalone_deployment = var.enable_xcloud && (var.xcloud_standalone_deployment || var.prod_spec || var.viso_env != "dev")

  enable_custom_kube_dns             = lookup(var.overrides, "enable_custom_kube_dns", var.is_metro_tenant ? false : var.enable_custom_kube_dns)
  custom_kube_dns_image              = lookup(var.overrides, "custom_kube_dns_image", "kube-dns/k8s-dns-kube-dns:1.23.0-gke.9")
  custom_kube_dns_dnsmasq_image      = lookup(var.overrides, "custom_kube_dns_dnsmasq_image", "kube-dns/k8s-dns-dnsmasq-nanny:1.23.0-gke.9")
  custom_kube_dns_sidecar_image      = lookup(var.overrides, "custom_kube_dns_sidecar_image", "kube-dns/k8s-dns-sidecar:1.23.0-gke.9")
  custom_kube_dns_autoscaler_image   = lookup(var.overrides, "custom_kube_dns_autoscaler_image", "kube-dns/cluster-proportional-autoscaler:v1.8.11-gke.7")
  enable_xdr_agent_daemon            = ! var.is_metro_tenant
  enable_vsg                         = lookup(var.overrides, "install_vsg_operator", !var.is_metro_tenant && var.enable_pipeline)

  shared_engines_public_ips = [for addr in data.google_compute_addresses.engine0_external.addresses : addr.address]
  workload_identity_project_id = var.is_metro_tenant ? var.metro_host_project_id : var.project_id
  enable_gmp   = lookup(var.overrides, "enable_gmp", false)
  enable_email_artifacts_relay = lookup(var.overrides, "enable_email_artifacts_relay", false)
  enable_primary_playbook_mirroring = lookup(var.overrides, "enable_primary_playbook_mirroring", false)
  enable_secondary_playbook_mirroring = lookup(var.overrides, "enable_secondary_playbook_mirroring", false)
  enable_playbook_mirroring = local.enable_primary_playbook_mirroring || local.enable_secondary_playbook_mirroring
  primary_project_id   = lookup(var.overrides, "primary_project_id", var.project_id)
  secondary_project_id = lookup(var.overrides, "secondary_project_id", var.project_id)
  enable_cortex_platform = lookup(var.overrides, "enable_cortex_platform", false)
  enable_otel_collector = local.enable_cortex_platform ? true : lookup(var.overrides, "enable_otel_collector", false)
  create_bq_tables_role = lookup(var.overrides, "role_id_create_bigquery_tables", "CreateBqTables")
  enable_email_sku     = !var.is_metro_tenant && local.enable_email_artifacts_relay && (var.is_xdr || var.is_xsiam)
}
