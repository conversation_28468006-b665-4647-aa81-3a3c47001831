module "platform_workload_identity" {
  source               = "../../modules/workload_identity"
  for_each = {
    for app in local.platform_apps:
    app.app_name => app
    if lookup(app, "enabled", true) && ! lookup(app, "skip_workload_identity", false)
  }
  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes
  }
  service_account_name = each.value.service_account_name
  mt_dedicated_group   = lookup(each.value, "mt_dedicated_group", false)
  dedicated_group_name = lookup(each.value, "dedicated_group_name", "")
  project_id           = var.project_id
  wi_project_id        = local.workload_identity_project_id
  data                 = lookup(each.value, "workload_identity", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns[local.st_namespace])
  viso_env             = var.viso_env
  create_kubernetes_sa = false
}

module "create_log_sink" {
  source = "../../modules/gcp_log_sink"
  for_each = {
    for app in local.platform_apps:
    app.app_name => app
    if lookup(app, "enabled", true) && var.viso_env == "dev" && contains(keys(app), "gcp_log_sink")
  }
  project_id   = var.project_id
  service_name = each.value.app_name
  sink_filter  = each.value.gcp_log_sink.sink_filter
}



locals {
  platform_apps = {
    ca_collection_coordinator = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      app_name             = "xdr-st-${var.lcaas}-ca-collection-coordinator"
      service_account_name = "ca-collection-coordinator-pod"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "ca_collection_fsi_priority1_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority2_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority3_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority4_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority5_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_eai_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_service_bus_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_asset_discovery_subs", null),
          lookup(var.subscriptions_output, "cloud_assets_full_discovery_sub", null),
          lookup(var.subscriptions_output, "ca_collection_service_bus_sub", null),
        ]
        buckets = [[lookup(var.buckets_output, "dp_asset_discovery_bucket", null), "ReadAndWrite"]]
      }
    }
    ca_collection_fsi_workers = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      app_name             = "xdr-st-${var.lcaas}-ca-collection-fsi-workers"
      service_account_name = "ca-collection-worker-pod"
      workload_identity = {
	additional_project_roles = ["roles/cloudprofiler.agent"]
        topics = [
          lookup(var.topics_output, "ca_collection_fsi_priority1_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority2_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority3_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority4_tasks_topic", null),
          lookup(var.topics_output, "ca_collection_fsi_priority5_tasks_topic", null),
          lookup(var.topics_output, "dp_collector_assets_topic", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "ca_collection_fsi_priority1_tasks_sub", null),
          lookup(var.subscriptions_output, "ca_collection_fsi_priority2_tasks_sub", null),
          lookup(var.subscriptions_output, "ca_collection_fsi_priority3_tasks_sub", null),
          lookup(var.subscriptions_output, "ca_collection_fsi_priority4_tasks_sub", null),
          lookup(var.subscriptions_output, "ca_collection_fsi_priority5_tasks_sub", null),
        ]
        buckets = [[lookup(var.buckets_output, "cloud_asset_inventory_bucket", null), "ReadAndWrite"]]
      }
    }
    ca_collection_eai_workers = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-ca-collection-eai-workers"
      service_account_name = "ca-collection-eai-worker-pod"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "dp_collector_assets_topic", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "ca_collection_eai_tasks_sub", null),
        ]
        buckets = [[lookup(var.buckets_output, "cloud_asset_inventory_bucket", null), "ReadAndWrite"]]
      }
    }
    cwp_vulnerability_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-vulnerability-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-vulnerability-analyzer"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "uvem_events", null), "ReadAndWrite"],
        ],
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_scan_results_vulnerability_analyzer", null),
        ],
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "uvem_protofinding_emits_topic", null),
          lookup(var.topics_output, "uvem_events_topic", null),
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_upsert", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "uvem_events_topic", ""), "roles/pubsub.publisher"],
        ]
      }
    }
    cwp_vulnerability_re_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-vulnerability-re-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-vulnerability-re-analyzer"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"],
        ],
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_re_evaluate_jobs_vulnerability_analyzer", null),
        ],
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cwp_re_evaluate_jobs", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "uvem_protofinding_emits_topic", null),
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cwp_re_evaluate_jobs", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_upsert", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_malware_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-malware-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-malware-analyzer"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_scan_results_malware_analyzer", null),
        ]
        topics = [
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_finding_emits", null), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_upsert", null), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", null), "roles/pubsub.viewer"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataEditor"]
        ]
      }
    }
    cwp_malware_detection_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-malware-detection-service"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-malware-detection-service"
    }
    dp-uai-assets-association = {
      enabled     = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-uai-assets-association"
      service_account_name = "dp-uai-assets-association"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]
        ]
        topics = [
          lookup(var.topics_output, "dp_asset_association_change_feed_topic", null),
          lookup(var.topics_output, "dp_uai_asset_association_ingestion_errors_topic", null),
          lookup(var.topics_output, "dp_uai_asset_association_observations_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "dp_collector_assets_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_association_observations_topic", ""), "roles/pubsub.subscriber"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_uai_asset_observations_sub", null),
        ],
      }
    }
    dp-uai-assets-migration = {
      enabled     = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-uai-assets-migration"
      service_account_name = "dp-uai-assets-migration-pod"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataEditor"]
        ]
        buckets = [
          [lookup(var.buckets_output, "asset_migrations_bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "dp_uai_asset_migrations_errors", null), "ReadAndWrite"]
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_change_feed_topic", null),
          lookup(var.topics_output, "dp_uai_asset_migrations_topic", null),
          lookup(var.topics_output, "dp_uai_asset_migration_errors_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_uai_asset_migrations_subs", null),
        ],
      }
    }
    dp-uai-assets = {
      enabled     = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-uai-assets"
      service_account_name = "dp-uai-assets-pod"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "autocomplete"), "roles/bigquery.dataEditor"]
        ]
        buckets = [
          [lookup(var.buckets_output, "cloud_asset_inventory_bucket", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "dp_asset_ingestion_errors", null), "ReadAndWrite"]
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_change_feed_topic", null),
          lookup(var.topics_output, "dp_collector_assets_topic", null),
          lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_uai_asset_observations_subs", null),
          lookup(var.subscriptions_output, "dp_collector_assets_subs", null),
          lookup(var.subscriptions_output, "dp_uai_asset_refresh_sub", null),
        ],
      }
    }
    external_integration_forwarder = {
      enabled              = local.enable_cortex_platform && var.viso_env == "dev"
      app_name             = "xdr-st-${var.lcaas}-external-integration"
      service_account_name = "external-integration"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "external_integration_data_forwarder_sub", null),
        ]
      }
    }
    cortex_platform = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-platform"
      service_account_name = "platform"
      mt_dedicated_group   = true
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "platform_data", null), "ReadAndWrite"]
        ]
      }
    }
    cloud_onboarding_manager = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cloud-onboarding-manager"
      service_account_name = "cloud-onboarding-manager"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]
        ]
        topics = [
          lookup(var.topics_output, "cloud_accounts", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
          lookup(var.topics_output, "cloud_accounts_full_discovery_topic", null),
          lookup(var.topics_output, "cloud_onboarding_tasks", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cloud_health_monitoring_sub", null),
          lookup(var.subscriptions_output, "cloud_health_monitoring_statuses_sub", null),
          lookup(var.subscriptions_output, "cloud_onboarding_tasks_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cloud_onboarding_templates", null), "ReadAndWrite"]
        ]
      }
    }
    cloud_onboarding_health_manager = {
      enabled              = lookup(var.overrides, "enable_cloud_onboarding_manager_health", false)
      app_name             = "xdr-st-${var.lcaas}-cloud-onboarding-health-manager"
      service_account_name = "onboarding-health-manager"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]
        ]
        topics = [
          lookup(var.topics_output, "cloud_accounts", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
          lookup(var.topics_output, "cloud_accounts_full_discovery_topic", null),
          lookup(var.topics_output, "cloud_onboarding_tasks", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cloud_health_monitoring_sub", null),
          lookup(var.subscriptions_output, "cloud_health_monitoring_statuses_sub", null),
          lookup(var.subscriptions_output, "cloud_onboarding_tasks_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cloud_onboarding_templates", null), "ReadAndWrite"]
        ]
      }
    }
    dp-uai-findings = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-uai-findings"
      service_account_name = "dp-uai-findings"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "autocomplete"), "roles/bigquery.dataEditor"]
        ]
        topics = [
          lookup(var.topics_output, "dp_finding_ingestion_errors", null),
          lookup(var.topics_output, "dp_finding_revisions", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_finding_emits_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "dp_finding_emits", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "dp_finding_error", null), "projects/${var.project_id}/roles/AllowBucketsReadAndWriteAccess"],
          [lookup(var.buckets_output, "dp_finding_revisions", null), "roles/storage.objectCreator"],
        ]
      }
    }
    cwp_secret_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-secret-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-secret-analyzer"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_scan_results_secret_analyzer", null),
        ]
        topics = [
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_upsert", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_trusted_image_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-trusted-image-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-trusted-image-analyzer"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_trust_evaluation_assets", null), "AllowBucketsReadAccess"],
        ]
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_trust_evaluation_asset_sub", null),
        ]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
      }
    }
    cwp_k8s_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-k8s-api"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-k8s-api"
      workload_identity = {
        bq_job_user = true
        artifact_registry_permissions = true
        artifact_registry_download = true
        bq = [
          [lookup(var.bq_output, "k8s_connector_observability"), "roles/bigquery.dataEditor"]
        ]
        topics = [
          lookup(var.topics_output, "cwp_k8s_data", null),
          lookup(var.topics_output, "cwp_scan_results", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "cwp_k8s_connector_logs", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_k8s_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"]
        ]
      }
    }
    cwp_k8s_content = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-k8s-content"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      mt_dedicated_group   = true
      service_account_name = "cwp-k8s-content"
      workload_identity = {
        artifact_registry_permissions = true
        artifact_registry_download = true
      }
    }
    cwp_sp_bc_distributor = {
        enabled              = local.enable_cortex_platform
        app_name             = "xdr-st-${var.lcaas}-cwp-sp-bc-distributor"
        namespace            = module.create_namespace.ns[local.cwp_namespace]
        service_account_name = "cwp-sp-bc-distributor"
        workload_identity = {
          buckets = [
            [lookup(var.buckets_output, "cwp_sp_bc_data", null), "ReadAndWrite"],
            [lookup(var.buckets_output, "cwp_sp_bc_data", null), "AllowBucketsDeleteObject"],
          ]
          topics = [
            lookup(var.topics_output, "cloud_health_monitoring", null),
          ]
          additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
        }
    }
    cwp_billing = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-billing"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-billing"
      workload_identity = {
        bq_job_user = true,
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
        ]
      }
    }
    cwp_rules_management = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-rules-management"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-rules-management"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_policy_rules_notification", null),
          lookup(var.topics_output, "cwp_issue_closing_topic", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_issue_closing_sub", null)
        ]
      }
    }
    cts = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cts"
      service_account_name = "cts-pod"
      namespace            =  module.create_namespace.ns[local.cortex_cts_namespace]
      workload_identity = {
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
        kms_crypto_keys = [
          [local.cts_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"]
        ]
      }
    }
    cwp_api = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      app_name             = "xdr-st-${var.lcaas}-cwp-api"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-api"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
        ],
        additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
      }
    }
    cwp_ads_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-api"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-api"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
      }
    }
    cwp_sp_snapshot = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-snapshot"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-snapshot"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
        ]
      }
    }
    cwp_ci_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ci-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-ci-analyzer"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
        ],
        topics = [
          lookup(var.topics_output, "cwp_scan_results", null),
        ],
      }
    }
    cwp_scan_results_enricher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-scan-results-enricher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-scan-results-enricher"
      mt_dedicated_group   = true
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cwp_scan_result_uploads", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cwp_sp_bc_data", null), "ReadAndWrite"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_scan_results_enricher", null),
          lookup(var.subscriptions_output, "cwp_scan_result_uploads_notifications_enricher", null),
        ]
        topics = [
          lookup(var.topics_output, "cwp_enriched_scan_results", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cwp_enriched_container_scan_results_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cwp_enriched_scan_results", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
      }
    }
    cwp_core_asset_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-core-asset-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-core-asset-analyzer"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "ReadAndWrite"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_scan_results_core_asset_analyzer", null),
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
      }
    }
    cwp_scanner_version_publisher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-scanner-version"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-scanner-version-publisher"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cwp_scanner_version_change_feed", null),
        ]
      }
    }
    cwp_registry_scan_orchestrator = {
      enabled = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-registry-scan-orchestrator"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-registry-scan-orchestrator"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_registry_scan_orchestrator_asset_change_feed_sub", null),
          lookup(var.subscriptions_output, "cwp_registry_scan_orchestrator_sp_lifecycle_events_sub", null),
          lookup(var.subscriptions_output, "cwp_registry_scan_requests_orchestrator_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "cwp_registry_scan_orchestrator_requests", null),
          lookup(var.topics_output, "cwp_scanner_version_change_feed", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
      }
    }
    cwp_registry_discovery_orchestrator = {
      enabled = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-registry-discovery-orchestrator"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-registry-disc-orchestrator"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_registry_discovery_orchestrator_asset_change_feed_sub", null),
          lookup(var.subscriptions_output, "cwp_registry_discovery_orchestrator_sp_lifecycle_events_sub", null),
          lookup(var.subscriptions_output, "cwp_registry_disc_requests_orchestrator_dlq", null),
          lookup(var.subscriptions_output, "cwp_registry_discovery_orchestrator_asset_change_feed_sub", null),
          lookup(var.subscriptions_output, "cwp_registry_discovery_orchestrator_sp_lifecycle_events_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
      }
    }
    cwp_serverless_scan_orchestrator = {
      enabled = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-serverless-scan-orchestrator"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-serverless-scan-orch"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_serverless_scan_orchestrator_asset_change_feed_sub", null),
          lookup(var.subscriptions_output, "cwp_serverless_scan_orchestrator_sp_lifecycle_events_sub", null),
          lookup(var.subscriptions_output, "cwp_serverless_scan_requests_orchestrator", null),
        ]
        topics = [
          lookup(var.topics_output, "cwp_serverless_scan_requests", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
      }
    }
    cwp_scan_spec_manager = {
      enabled = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-scan-spec-manager"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-spec-manager"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
      }
    }
    cwp_k8s_inventory_publisher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-k8s-inventory-publisher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-k8s-inventory-publisher"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_k8s_data_inventory_sub", null)
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_k8s_data", null), "ReadAndWrite"],
        ]
      }
    }
    cwp_compliance_uai_scanner = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-k8s-uai-scanner"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-compliance-uai-scanner"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_scan_results", null), "ReadAndWrite"],
        ]
      }
    }
    cwp_k8s_rules_calculator = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-k8s-rules-calculator"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-k8s-rules-calculator"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_policy_rules_notification_sub", null)
        ]
      }
    }
    cwp_compliance_publisher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-compliance-publisher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-compliance-publisher"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_scan_results_compliance_publisher", null),
          lookup(var.subscriptions_output, "cwp_policy_rules_notification_compliance_publisher", null),
        ]
        topics = [
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_scan_logs_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_upsert", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "dp_scan_logs", null), "ReadAndWrite"],
        ]
      }
    }
    cwp_compliance_calc = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-compliance-calc"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-compliance-calc"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-cwp-compliance-xdr-calc"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_agent_downloads", null), "ReadAndWrite"],
        ]
      }
    }
    cwp_compliance_agent_rules_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-compliance-agent-rules-api"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-compliance-agent-rules-api"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-cwp-compliance-agent-rules-api"
      workload_identity = {
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
        topics = [
          lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", ""), "roles/pubsub.publisher"],
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_agent_downloads", null), "ReadAndWrite"],
        ]
      }
    }
    cwp_sp_workload_orchestration = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-workload-orchestration"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-workload-orchestration"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_sp_bc_data", null), "AllowBucketsReadAccess"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_sp_lifecycle_events", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_sp_bc_control_workload_orchestration", null),
        ]
        kms_crypto_keys = [
           [local.sp_jwt_kms_keyring, "roles/cloudkms.signer"],[local.sp_jwt_kms_keyring, "roles/cloudkms.publicKeyViewer"]
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    cwp_ads_scan_status_observer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-scan-status-observer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-scan-status-observer"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_sp_lifecycle_events_ads_result_sync_sub", null),
        ]
      }
    }
    cwp_ads_prioritization = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-prioritization"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-prioritization"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_ads_snapshots = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-snapshots"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-snapshots"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_ads_grouping = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-grouping"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-grouping"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_ads_scanner_launch = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-scanner-launch"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-scanner-launch"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-cortex-tenant-st-cwp-ads-scanner-launch"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_ads_account_cleaner = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-account-cleaner"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-account-cleaner"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    cwp_ads_platform_sync = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-ads-platform-sync"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-ads-platform-sync"
      workload_identity = {
        bq_job_user = true
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cloud_onboarding_tasks", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataViewer"],
        ]
      }
    }
    cwp_sp_bc_fetcher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-bc-fetcher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-bc-fetcher"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cwp_sp_bc_objects_topic", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    cwp_sp_bc_object_handler = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-bc-object-handler"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-bc-object-handler"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_sp_bc_data", null), "ReadAndWrite"],
        ],
        topics = [
          lookup(var.topics_output, "cwp_sp_bc_objects_topic", null),
          lookup(var.topics_output, "cwp_sp_bc_results_topic", null),
          lookup(var.topics_output, "cwp_sp_bc_control_topic", null),
          lookup(var.topics_output, "cwp_scan_results", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_sp_bc_objects_sub", null),
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    cwp-cna-engine-cron-job =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-cna-engine"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-cna"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-cwp-cna-engine"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"]
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_upsert", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_finding_emits_sub", null),
        ],
      }
    }
    cwp_pc1_migration = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-pc1-migration"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-pc1-migration"
      workload_identity = { }
    }
    xdr-agent-asset-cron-job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-xdr-agent-asset"
      service_account_name = "xdr-agent-asset"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    modules-issue-ingester = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-modules-issue-ingester"
      service_account_name = "modules-issue-ingester"
      workload_identity = {
        bq_job_user = true
        topics = [
          lookup(var.topics_output, "ap_issue_ingestion_errors", null),
          lookup(var.topics_output, "ap_issue_create", null),
          lookup(var.topics_output, "ap_issue_ingest_feedback", null),
          lookup(var.topics_output, "ap_issue_update", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "ap_issue_upsert_sub", null)
        ]
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"]
        ]
      }
    }
    issue-fetcher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-issue-fetcher"
      service_account_name = "issue-fetcher"
      mt_dedicated_group   = true
      workload_identity = {
        bq_job_user = true
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        topics = [
          lookup(var.topics_output, "ap_issue_ingest_feedback", null),
          lookup(var.topics_output, "ap_issue_update", null),
          lookup(var.topics_output, "edr_topic", null),
          lookup(var.topics_output, "playbook_execution_topic", null),
          lookup(var.topics_output, "artifact_extraction_topic", null),
          lookup(var.topics_output, "analytics-task-processor", null),
          lookup(var.topics_output, "slack_notification_topic", null),
          lookup(var.topics_output, "itdr_update_topic", null),
          lookup(var.topics_output, "task_processor_topic", null),
          lookup(var.topics_output, "ap_issue_ingestion_errors", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "ap_issue_create_sub", null)
        ]
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"]
        ]
        buckets = [
          [lookup(var.buckets_output, "alert_original_bucket", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"]
        ]
      }
    }
    issue-updater = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-issues-updater"
      service_account_name = "issues-updater"
      mt_dedicated_group   = true
      workload_identity = {
        topics = [
          lookup(var.topics_output, "ap_issue_ingestion_errors", null),
          lookup(var.topics_output, "task_processor_topic", null),
          lookup(var.topics_output, "itdr_update_topic", null),
          lookup(var.topics_output, "object_mirroring_service_topic", null),
        ]
        topics_mt = [[local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]]
        bq_job_user = true
        subscriptions = [
          lookup(var.subscriptions_output, "ap_issue_update_sub", null)
        ]
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"]
        ]
      }
    }
    apisec_grouping_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-grouping-service"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-grouping-service"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "apisec_analytics", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform", ""), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "apisec_grouped_transactions_topic", null),
          lookup(var.topics_output, "apisec_grouped_transactions_analytics_bq_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "apisec_transactions_grouping_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"],
        ]
      }
    }
    apisec_enricher_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-enricher-service"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-enricher-service"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-apisec-enr-mt"
      workload_identity = {
        bq_job_user = true
        topics = [
          lookup(var.topics_output, "apisec_transactions_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "apisec_dp_bus_enricher", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_bus_topic", ""), "roles/pubsub.viewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "ipl-edr-data", null), "AllowBucketsReadAccess"]
        ]
      }
    }
    apisec-asset-manager = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-asset-manager"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-asset-manager"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "dp_asset_ingestion_errors", null), "AllowBucketsReadAccess"],
        ]
        bq = [
          [var.bq_output["asset_inventory"], "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "apisec_grouped_asset_manager_sub", null),
          lookup(var.subscriptions_output, "apisec_dp_uai_asset_ingestion_errors_sub", null),
          lookup(var.subscriptions_output, "apisec_spec_asset_manager_sub", null),
          lookup(var.subscriptions_output, "apisec_asset_updates_sub", null),
        ]
      }
    }
    apisec-risk-engine = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-risk-engine"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-risk-engine"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-apisec-risk-engine-mt"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "apisec_bff_user_uploads", null), "AllowBucketsReadAccess"],
        ]
        topics = [
          var.topics_output["dp_finding_emits"],
          var.topics_output["ap_issue_upsert"],
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [var.topics_output["dp_finding_emits"], "roles/pubsub.viewer"],
          [var.topics_output["ap_issue_upsert"], "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "ap_issue_ingestion_errors_risk_sub", null),
          lookup(var.subscriptions_output, "apisec_grouped_risk_sub", null),
          lookup(var.subscriptions_output, "dp_finding_ingestion_errors_risk_sub", null),
          lookup(var.subscriptions_output, "apisec_spec_risk_engine_sub", null),
        ],
        bq = [
          [lookup(var.bq_output, "public_platform", ""), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", ""), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true
        bq_data_viewer = true
      }
    }

    itdr-ca-realtime-cron =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-itdr-ca-realtime-cron"
      service_account_name = "itdr-ca-realtime-cron"
      workload_identity = {
        bq_job_user = true
        bq_data_viewer = true
        additional_sa_roles = [
                          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
                        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "analytics", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ],
        topics = [
          lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", ""), "roles/pubsub.publisher"],
        ]
        buckets = [
          [lookup(var.buckets_output, "agent-itdr-ca-integrations", null), "roles/storage.admin"],
        ]
      }
    }

    itdr-daily-risk-cron =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-itdr-daily-risk-cron"
      service_account_name = "itdr-daily-risk-cron"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataEditor"],
        ]
        bq_job_user = true
        bq_data_viewer = true
        topics = [
          lookup(var.topics_output, "itdr_case_changes_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
      }
    }

    itdr-ca-baseline-cron =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-itdr-ca-baseline-cron"
      service_account_name = "itdr-ca-baseline-cron"
      workload_identity = {
        bq_job_user = true
        bq_data_viewer = true
        additional_sa_roles = [
                          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
                        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "analytics", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ],
        topics = [
          lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", ""), "roles/pubsub.publisher"],
        ]
        buckets = [
          [lookup(var.buckets_output, "agent-itdr-ca-integrations", null), "roles/storage.admin"],
        ]
      }
    }

    itdr-secret-rotation-cron =  {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-secret-rotation-cron"
      service_account_name = "itdr-secret-rotation-cron"
      workload_identity = {
        secret_manager_secrets = [
          [lookup(var.secret_manager_secrets_output, "itdr_weak_passwords_salt", ""), "roles/secretmanager.secretVersionManager"],
        ]
      }
    }

    itdr-asset-retention =  {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-asset-retention"
      service_account_name = "itdr-asset-retention"
      workload_identity = {
        bq_job_user = true
        bq_data_viewer = true
        bq = [
            [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataViewer"],
            [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
            [lookup(var.bq_output, "public_access_views", null), "roles/bigquery.dataViewer"],
        ],
        buckets = [
          [lookup(var.buckets_output, "dp_asset_ingestion_errors", null), "AllowBucketsReadAccess"]
        ],
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", null), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", null), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "itdr_case_changes_topic", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "itdr_data_pipeline_asset_ingestion_errors_sub", null),
        ]
      }
    }

    itdr-bigquery-migrator = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-itdr-bigquery-migrator"
      service_account_name = "itdr-bigquery-migrator"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true
        bq_data_viewer = true
      }
    }
    itdr_data_pipeline = {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-data-pipeline"
      service_account_name = "itdr-data-pipeline"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-itdr-data-pipeline"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "analytics", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ],
        bq_job_user = true
        bq_data_viewer = true
        buckets = [
          [lookup(var.buckets_output, "agent-itdr-integrations", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "dp_asset_ingestion_errors", null), "AllowBucketsReadAccess"]
        ],
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "itdr_case_changes_topic", null),
          lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", null),
          lookup(var.topics_output, "dp_finding_emits", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "itdr-data_pipeline_dss_sync_sub", null),
          lookup(var.subscriptions_output, "itdr_data_pipeline_case_changes_sub", null),
          lookup(var.subscriptions_output, "itdr_data_pipeline_asset_ingestion_errors_sub", null),
          lookup(var.subscriptions_output, "dp_finding_ingestion_errors_itdr_sub", null),
          lookup(var.subscriptions_output, "agent_mgmt_outgoing_ext_integrations_itdr_weak_passwords_sub", null),
          lookup(var.subscriptions_output, "itdr_data_pipeline_asset_updates_sub", null),
          lookup(var.subscriptions_output, "itdr_data_pipeline_dp_bus_sub", null),
        ]
        secret_manager_secrets = [
          [lookup(var.secret_manager_secrets_output, "itdr_weak_passwords_salt", ""), "roles/secretmanager.secretAccessor"],
        ]
      }
    }
    itdr_asset_updates_syncer = {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-asset-updates-syncer"
      service_account_name = "itdr-asset-updates-syncer"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "itdr_asset_updates_topic", null),
        ]
      }
    }
    ciem-bigquery-migrator = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-ciem-bigquery-migrator"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-bigquery-migrator"
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "ciem"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataEditor"],
        ]
        bq_job_user = true
      }
    }
    ciem-api = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-api-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-api"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-api"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
	  [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataViewer"],
	  [lookup(var.bq_output, "public_access_views", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
	  lookup(var.topics_output, "bq_stats_topic", null),
	  lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_ciem", null),
	  lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_ciem_result", null),
    lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_other", null),
	  lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_other_result", null),
	]
	subscriptions = [
          lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_ciem_sub", null),
          lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_ciem_result_sub", null),
          lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_other_sub", null),
          lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_other_result_sub", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"],
        ]
      }
    }
    ciem-ipc = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-ipc-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-ipc"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-ipc"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "dspm", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "classification_mgmt", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
        ],
        buckets = [
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
        ],
        topics = [
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),]
      }
    }
    ciem-access-table-cron-job =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-ciem-access-table-cron-job"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-access-table-cron-job"
      workload_identity = {
        bq_job_user    = true
        bq_data_viewer = true
        bq = [
          [lookup(var.bq_output, "ciem", ""), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", ""), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "dspm", ""), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "classification_mgmt", ""), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", ""), "roles/bigquery.dataViewer"]
        ]
        topics = [
            lookup(var.topics_output, "bq_stats_topic", "")
        ]
        topic_additional_roles = [
        ]
      }
    }
    ciem_epc = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-epc-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-epc"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-epc"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataViewer"],
        ],
        buckets = [
          [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"]
        ],
        topics = [
          lookup(var.topics_output, "ciem_epc_task_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),

        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "ciem_epc_task_topic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"],

        ]
        subscriptions = [
          lookup(var.subscriptions_output, "ciem_epc_task_sub", null),
        ]

      }
    }


    ciem-pre-calculation = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-pre-calculation-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-pre-calculation"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-pre-calculation"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataEditor"],
        ],
      }
    }

        ciem-rule-scanner-ciem = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-rule-scanner-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-rule-scanner-ciem"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-rule-scanner-ciem"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ],
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_scan_logs_topic", null),
        ]
      }
    }
    ciem-rule-scanner-dspm = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-dspm-rule-scanner-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-rule-scanner-dspm"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-rule-scanner-dspm"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ],
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_scan_logs_topic", null),
        ]
      }
    }
    ciem-rule-scanner-aispm = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-aispm-rule-scanner-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-rule-scanner-aispm"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-rule-scanner-aispm"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ],
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_scan_logs_topic", null),
        ]
      }
    }
    ciem-account-manager = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-account-manager-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-account-manager"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-account-manager"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.metadataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "ciem_epc_task_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_health_monitoring_statuses", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"]
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "ciem_epc_task_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "ciem_epc_task_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cloud_health_monitoring", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cloud_health_monitoring_statuses", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cloud_health_monitoring_statuses", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    ciem-db-maintenance = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-db-maintenance-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-db-maintenance"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-db-maintenance"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataEditor"],
        ]
      }
    }
    dbre-operator = {
      skip_app             = true
      app_name             = "xdr-st-${var.lcaas}-dbre-operator"
      enabled              = local.enable_cortex_platform
      service_account_name = "dbre-operator"
      workload_identity = {
       buckets = [
          [lookup(var.buckets_output, "dbre_backups", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "dbre_backups", null), "AllowBucketsWriteAndDeleteObject"],
        ],
      }
    }
    ciem-lap = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-lap-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-lap"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-lap"
      workload_identity = {
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"],
        ]
      }
    }

    ciem-static-evidenc = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-metrics-publisher-mt"
      app_name             = "xdr-st-${var.lcaas}-ciem-static-evidence"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "ciem-static-evidence"
      workload_identity = {
        topics = [
	  lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_ciem", null),
	  lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_ciem_result", null),
	]
	subscriptions = [
          lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_ciem_sub", null),
          lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_ciem_result_sub", null),
        ]
	additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
	  [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataEditor"],
	  [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
	  [lookup(var.bq_output, "public_access_views", null), "roles/bigquery.dataViewer"],
        ],
      }
    }

    dspm-aispm-static-evidence = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-ciem-metrics-publisher-mt"
      app_name             = "xdr-st-${var.lcaas}-dspm-aispm-static-evidence"
      namespace            = module.create_namespace.ns[local.ciem_namespace]
      service_account_name = "dspm-aispm-static-evidence"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_other", null),
          lookup(var.topics_output, "ciem_issue_evidence_recalc_jobs_other_result", null),
	      ]
        subscriptions = [
                lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_other_sub", null),
                lookup(var.subscriptions_output, "ciem_issue_evidence_recalc_jobs_other_result_sub", null),
        ]
        additional_project_roles = ["projects/${var.project_id}/roles/GetTopicsIamPolicies"]
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ciem", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_access_views", null), "roles/bigquery.dataViewer"],
        ],
      }
    }

    cwp-sbom-analyzer = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-crtx-cwp-sbom-analyzer-mt"
      app_name             = "xdr-st-${var.lcaas}-cwp-sbom-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sbom-analyzer"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "external_data"), "roles/bigquery.dataEditor"],
        ]
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "ext_logs_bucket", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "uvem_events", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "external_logs_topic", null),
          lookup(var.topics_output, "uvem_events_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "external_logs_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "uvem_events_topic", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_scan_results_sbom_analyzer", null),
        ]
      }
    }
    cwp-sp-account-controller = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      dedicated_group_name = "crtx-ads-shared-acc"
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-account-controller"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-account-controller"
      workload_identity = {
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    cwp-sp-bc-log-ingestion = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-bc-log-ingestion"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-bc-log-ingestion"
      workload_identity = {
        additional_project_roles = concat(
          [
            "roles/logging.logWriter",
          ],
          var.is_fedramp ? [] : ["roles/cloudprofiler.agent"],
        )
        buckets = [
          [lookup(var.buckets_output, "cwp_sp_bc_data", null), "AllowBucketsReadAccess"],
        ],
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_sp_bc_control_sub", null),
        ]
      }
      gcp_log_sink = {
        sink_filter = "logName=~\"scan-platform\""
        project_id  = var.project_id
      }
    }
    cwp-sp-bc-metrics-ingestor = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-bc-metrics-ingestor"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-bc-metrics-ingestor"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_sp_bc_data", null), "AllowBucketsReadAccess"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_sp_bc_metrics_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_sp_bc_control_metrics_sub", null),
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    cwp-sp-health-tracker =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-sp-health-tracker"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-sp-health-tracker"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "cloud_health_monitoring", null),
          lookup(var.topics_output, "cloud_onboarding_tasks", null),
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
    }
    apisec_inspection_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-inspection-service"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-inspection-service"
      mt_dedicated_group   = true
      workload_identity = {
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "apisec_inspection_alerts", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "ap_issue_upsert", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "apisec_inspection_alerts", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "apisec_grouped_inspection_sub", null),
        ]
        bq = [
          [lookup(var.bq_output, "apisec_analytics", null), "roles/bigquery.dataEditor"]
        ]
      }
    }
    apisec-issuer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-issuer"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-issuer"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "agent_mgmt_ext_integrations_apisec_issuer_sub", null),
          lookup(var.subscriptions_output, "apisec_inspection_alerts_apisec_issuer_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "apisec_inspection_alerts", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    
    apisec-spec-service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-spec-service"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-spec-service"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "apisec_bff_user_uploads", null), "ReadAndWrite"],
        ]
        bq_job_user    = true
        bq_data_viewer = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "apisec_spec_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "dp_bus_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "apisec_spec_topic", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    apisec-spec-service-cron-job =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-spec"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-spec-cron-job"
      workload_identity = {
        bq_job_user    = true
        bq_data_viewer = true
        bq = [
          [lookup(var.bq_output, "public_platform", ""), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", ""), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "apisec_spec_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "apisec_asset_updates_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "apisec_spec_topic", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    apisec-issue-patcher-cron-job =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-issue-patcher"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-issue-patcher-cron-job"
      workload_identity = {
        bq_job_user    = true
        bq_data_viewer = true
        bq = [
          [lookup(var.bq_output, "public_platform", ""), "roles/bigquery.dataViewer"],
        ]
        topics = [
	        lookup(var.topics_output, "ap_issue_upsert", null),
 	        lookup(var.topics_output, "bq_stats_topic", null),
        ]
      }
    }
    apisec-spec-gate-cron-job =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-spec-gate"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-spec-gate-cron-job"
      workload_identity = {
        bq_job_user = true
        buckets = [[lookup(var.buckets_output, "apisec_bff_user_uploads", null), "ReadAndWrite"]]
        bq = [
          [lookup(var.bq_output, "public_platform", ""), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", ""), "roles/bigquery.dataViewer"],
        ]
        topics = [
	        lookup(var.topics_output, "bq_stats_topic", null),
        ]
      }
    }
    apisec-scan-manager-service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-scan-manager-service"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-scan-manager-service"
      workload_identity = {
        buckets = [[lookup(var.buckets_output, "apisec_scan_results", null), "AllowBucketsWriteAndDeleteObject"]]
      }
    }
    apisec_bff_service = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-bff-service"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-bff-service"
      mt_dedicated_group   = true
      workload_identity = {
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "apisec_bff_user_uploads", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "apisec_bff_user_uploads", null), "roles/storage.objectUser"]
	      ]
	bq = [
	  [lookup(var.bq_output, "public_access_views", null), "projects/${var.project_id}/roles/${local.create_bq_tables_role}"]
	]
        bq_job_user = true
        bq_data_viewer = true
        additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]

      }
    }
    apisec-asset-manager-cron-job =  {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-apisec-asset-mgr"
      namespace            = module.create_namespace.ns[local.apisec_namespace]
      service_account_name = "apisec-asset"
      workload_identity = {
        bq_job_user    = true
        bq_data_viewer = true
        bq = [
          [var.bq_output["asset_inventory"], "roles/bigquery.dataViewer"],
          [var.bq_output["public_platform"], "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "apisec_asset_updates_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
        ]
      }
    }
    uvem_netscan_processor = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-uvem-netscan-processor"
      service_account_name = "uvem-netscan-processor"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-xdr-wi-uvem-netscan-processor"
      workload_identity = {
        bq_job_user = true
        read_session_user = true
        bq = [
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_association_observations_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "dp_uai_asset_association_ingestion_errors_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_asset_association_change_feed_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_change_feed_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "uvem_netscan_results_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "uvem_protofinding_emits_topic", ""), "roles/pubsub.publisher"]
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "dp_uai_asset_ingestion_errors_uvem_netscan_sub", null),
          lookup(var.subscriptions_output, "dp_asset_association_change_feed_uvem_netscan_sub", null),
          lookup(var.subscriptions_output, "dp_uai_asset_change_feed_uvem_netscan_sub", null),
          lookup(var.subscriptions_output, "uvem_netscan_results_sub", null),
          lookup(var.subscriptions_output, "uvem_vxp_protofinding_emits_sub", null)
        ]
        buckets = [
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"]
        ]
        kms_crypto_keys = [
          [local.uvem_kms_keyring, "roles/cloudkms.cryptoKeyEncrypterDecrypter"]
        ]
      }
    }
    uvem_netscan_controller = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-uvem-netscan-controller"
      service_account_name = "uvem-netscan-controller"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-xdr-wi-uvem-netscan-controller"
      workload_identity = {
        bq_job_user = true
        read_session_user = true
        bq = [
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
        ]
      }
    }
    uvem_vip_api = {
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      app_name             = "xdr-st-${var.lcaas}-uvem-vip-api"
      service_account_name = "uvem-vip-api"
      workload_identity = {
        bq_job_user = true
        read_session_user = true
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
        ]
        buckets = [
          [lookup(var.buckets_output, "uvem_events", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "uvem_events_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "uvem_events_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "uvem_events_topic", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "uvem_vip_events_sub", null)
        ]
        kms_crypto_keys = [
          [local.uvem_kms_keyring, "roles/cloudkms.cryptoKeyEncrypterDecrypter"]
        ]
      }
    }
    uvem_vxp_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-uvem-vxp-api"
      service_account_name = "uvem-vxp-api"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-xdr-wi-uvem-vxp-api"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "uvem_events", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_finding_revisions", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "uvem_events_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "ap_issue_ingest_feedback", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "uvem_events_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "uvem_events_topic", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "vxp_comp_controls_asset_change_feed_sub", null),
          lookup(var.subscriptions_output, "ap_issue_ingest_feedback_sub_vxp", null),
          lookup(var.subscriptions_output, "uvem_vxp_protofinding_emits_sub", null),
          lookup(var.subscriptions_output, "uvem_protofinding_ingestion_errors_vxp_sub", null),
          lookup(var.subscriptions_output, "dp_finding_ingestion_errors_vxp_sub", null),
          lookup(var.subscriptions_output, "dp_finding_revisions_sub", null),
          lookup(var.subscriptions_output, "uvem_vxp_events_sub", null)
        ]
        bq_job_user = true
        read_session_user = true
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
      }
    }
    cwp_rules_watcher = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-rules-watcher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      mt_dedicated_group   = true
      service_account_name = "cwp-rules-watcher"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "cwp_policy_rules_notification", null),
        ]
      }
    }
    cwp-re-evaluation-cron-job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-re-evaluation"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-re-evaluation"
    }
    itdr_risk_processor_service = {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-risk-processor-service"
      service_account_name = "itdr-risk-processor-service"
      workload_identity = {
        subscriptions = [
          lookup(var.subscriptions_output, "itdr_update_risk_processor_sub", null),
        ]
        topics = [
          lookup(var.topics_output, "itdr_case_changes_topic", null),
          lookup(var.topics_output, "itdr_update_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        bq = [
          [lookup(var.bq_output, "itdr"), "roles/bigquery.dataEditor"],
        ]
        bq_job_user = true
        bq_data_viewer = true
      }
    }
    platform_compliance_calc = {
      enabled              = local.enable_cortex_platform
      app_name             = "platform-compliance-calc"
      service_account_name = "platform-compliance-calc"
      mt_dedicated_group   = true
      dedicated_group_name = "gcplocal-cwp-compliance-xdr-calc"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataEditor"],
        ]
      }
    }
    cwp-image-analyzer-cron-job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-img-analyzer-cron-job"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-image-analyzer-cron-job"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "asset_inventory"), "roles/bigquery.dataEditor"]
        ],
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
        ]
        additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
      }
    }
    dp-analytics-scan-logs = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-analytics-scan-logs"
      service_account_name = "dp-analytics-scan-logs"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "dp_scan_logs_ingestion_errors_topic", null),
        ],
        subscriptions = [
          lookup(var.subscriptions_output, "dp_scan_logs_sub", null),
        ],
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "autocomplete"), "roles/bigquery.dataEditor"],
        ]
        buckets = [
          [lookup(var.buckets_output, "dp_scan_logs", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "dp_scan_logs_ingestion_errors", null), "ReadAndWrite"]
        ],
      }
    }
    cwp-image-analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-image-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-be-core"
      service_account_name = "cwp-image-analyzer"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "dp_uai_asset_change_feed_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_image_analyzer_asset_change_feed_sub", null),
        ]
      }
    }

    cwp_serverless_api = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-serverless-api"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-serverless-api"
      workload_identity = {
        topics = [
          lookup(var.topics_output, "agent_management_reported_issues", null),
          lookup(var.topics_output, "log_processor_topic", null)
        ]
      }
    }
    cwp_containers_analyzer = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-containers-analyzer"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      service_account_name = "cwp-containers-analyzer"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_enriched_scan_results", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cwp_enriched_container_scan_results", null), "ReadAndWrite"]
        ]
        topics = [
          lookup(var.topics_output, "cwp_enriched_scan_results", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "uvem_protofinding_emits_topic", null)
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_enriched_container_scan_results_containers_analyzer_sub", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataEditor"]
        ]
      }
    }
    # CRTX-172365: Findings Promotion Pipeline (FPP)
    cwp_fpp_dispatcher_cron_job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-fpp-dispatcher"
      service_account_name = "cwp-fpp-dispatcher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "dp_asset_export", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "cwp_fpp_policy_evaluation", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_fpp_asset_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cwp_fpp_asset_topic", null), "roles/pubsub.viewer"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true
      }
    }
    cwp_fpp_dispatcher_2_cron_job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-fpp-dispatcher-2"
      service_account_name = "cwp-fpp-dispatcher-2"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "dp_asset_export", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "cwp_fpp_policy_evaluation", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_fpp_asset_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cwp_fpp_asset_topic", null), "roles/pubsub.viewer"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true
      }
    }
    cwp_fpp_worker = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-fpp-worker"
      service_account_name = "cwp-fpp-worker"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "dp_asset_export", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "cwp_fpp_policy_evaluation", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_fpp_policy_evaluation_topic", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "uvem_protofinding_emits_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cwp_fpp_policy_evaluation_topic", null), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_fpp_asset_sub", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true
      }
    }
    cwp_fpp_policy_evaluator = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-fpp-policy-evaluator"
      service_account_name = "cwp-fpp-policy-evaluator"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_fpp_policy_evaluation", null), "AllowBucketsReadAccess"],
        ]
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cwp_fpp_policy_evaluation_sub", null),
        ]
      }
    }

    cwp_trust_evaluation_dispatcher_cron_job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-trust-dispatcher"
      service_account_name = "cwp-trust-dispatcher"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "cwp_trust_evaluation_assets", null), "ReadAndWrite"],
        ]
        topics = [
          lookup(var.topics_output, "cwp_trust_evaluation_asset_topic", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true
      }
    }

    itdr_api = {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-api"
      service_account_name = "itdr-api"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "agent-itdr-integrations", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "agent-itdr-ca-integrations", null), "roles/storage.objectAdmin"],
        ]
        topics = [
          lookup(var.topics_output, "agent_management_incoming_external_integrations_topic", null),
          lookup(var.topics_output, "itdr_mfa_tokens_topic", null),
        ]
        additional_sa_roles = [
                      "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
                    ]
      }
    }
    itdr_audits = {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr_audits"
      service_account_name = "itdr-audits"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "itdr", null), "roles/bigquery.dataEditor"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "itdr_cap_reports_sub", null),
        ]
      }
    }
    itdr_cap_block_responder = {
      enabled              = var.enable_itdr
      app_name             = "xdr-st-${var.lcaas}-itdr-cap-block-responder"
      service_account_name = "itdr-cap-block-responder"
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "agent-itdr-ca-integrations", null), "roles/storage.objectAdmin"],
        ]
        topics = [
          lookup(var.topics_output, "itdr_cap_responder_agent_tokens_topic", null),
          lookup(var.topics_output, "itdr_cap_responder_tokens_result_topic", null),
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "itdr_cap_responder_block_email_sub", null),
          lookup(var.subscriptions_output, "itdr_cap_responder_block_mfa_sub", null),
          lookup(var.subscriptions_output, "itdr_cap_responder_tokens_sub", null),
        ]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
      }
    }
    cwp-runtime-image-relations-publisher-cron-job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-cwp-runtime-image-relations-publisher-cron-job"
      namespace            = module.create_namespace.ns[local.cwp_namespace]
      team                 = "cwp-celtics"
      service_account_name = "cwp-relations-publisher"
      workload_identity = {
        bq_job_user = true
        bq = [
          [lookup(var.bq_output, "cwp_asset_relations"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"]
        ],
        topics = [
          lookup(var.topics_output, "dp-uai-asset-observations", null),
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
      }
    }
  }
}

# becuse of cycle error i cant use this local in workload identity block of cts
resource "google_service_account_iam_member" "sa_key_permissions" {
  depends_on         = [module.platform_workload_identity]
  count              = local.enable_cortex_platform ? length(local.prisma_service_accounts) : 0
  member             = "serviceAccount:${local.platform_apps.cts.service_account_name}@${var.project_id}.iam.gserviceaccount.com"
  role               = "roles/iam.serviceAccountKeyAdmin"
  service_account_id = "projects/${var.project_id}/serviceAccounts/${local.prisma_service_accounts[count.index]}"
}

