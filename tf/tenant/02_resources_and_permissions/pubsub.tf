module "create_topic" {
  source = "../../modules/topic"
  for_each = {
    for topic_key, v in local.topics :
    topic_key => v
    if lookup(v, "enabled", true)
  }
  depends_on                     = [module.create_bq]
  default_byok_key_id            = var.enable_byok ? module.create_kms_crypto_key.st_kms_key_id["default_byok_key"] : ""
  enable_byok                    = var.enable_byok
  project_id                     = var.project_id
  topic_data                     = each.value
  topic_key                      = each.key
  subscriptions                  = each.value.sub
  sub_message_retention_duration = local.sub_message_retention_duration
  topic_iam_member               = lookup(each.value, "iam", {})
  project_iam_member             = lookup(each.value, "project_iam", {})
  topic_pubsub_schema            = lookup(each.value, "schema_settings", {})
  enable_cloud_posture           = var.enable_cloud_posture
}

module "create_topic_lf" {
  source               = "../../modules/topic"
  project_id           = var.project_id
  enable_byok          = false # false since topic is in MT project and not in customer's tenant
  topic_data           = local.lf_syslog_data_topic
  topic_key            = "lf_syslog_data_topic"
  subscriptions        = local.lf_syslog_data_topic.sub
  topic_iam_member     = lookup(local.lf_syslog_data_topic, "iam", {})
  project_iam_member   = lookup(local.lf_syslog_data_topic, "project_iam", {})
  topic_pubsub_schema  = lookup(local.lf_syslog_data_topic, "schema_settings", {})
  enable_cloud_posture = var.enable_cloud_posture
  providers = {
    google = google.mt
  }
}

locals {
  sub_message_retention_duration = var.viso_env == "dev" || var.tenant_type == "internal" ? "86400s" : "604800s"
  tsf_sa_suffix                  = var.viso_env == "dev" ? "dev" : "prod"
  permissions_sa_suffix          = var.viso_env == "dev" ? "-dev" : ""
  permissions_sa                 = "xdr-permission-bot${local.permissions_sa_suffix}-<EMAIL>"
  topics                         = local.general_topics
  durations = {
    two_days_in_seconds = 2 * 24 * 60 * 60
  }
  general_topics = {
    health_alerts = {
      enabled = var.is_xsiam
      name    = "health-alerts-${var.lcaas}"
      labels = {
        name = "health_alerts_topic"
        app  = "xsiam"
      }
      sub = {
        health_alerts_sub = {
          name                 = "health-alerts-${var.lcaas}_sub"
          ack_deadline_seconds = 600
          labels = {
            name = "health_alerts_sub"
            app  = "xsiam"
          }
        }
      }
    }
    alerts_fetcher = {
      name = "alerts-fetcher-${var.lcaas}"
      labels = {
        name = "alerts_fetcher_topic"
        app  = "xdr-shared"
      }

      iam = {
        add_gcs_member = {
          enabled = var.enable_asm
          role    = "roles/pubsub.publisher"
          member  = "serviceAccount:${local.gcs_account}"
        }
      }

      sub = {
        alerts_fetcher_sub = {
          name                 = "alerts-fetcher"
          ack_deadline_seconds = 600
          labels = {
            name = "alerts_fetcher_sub"
            app  = "xdr"
          }
        }
      }
    }
    analytics-task-processor = {
      name = "analytics-task-processor-${var.lcaas}"
      labels = {
        name = "analytics_task_processor_topic"
        app  = "xdr"
      }
      sub = {
        analytics_task_processor_sub = {
          name                 = "analytics-task-processor-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "analytics_task_processor_sub"
            app  = "xdr"
          }
        }
      }
    }
    xsoar_artifacts_extraction_incident_notifications = {
      name = "xsoar-artifacts-extraction-incident-notifications"
      labels = {
        name = "xsoar_artifacts_extraction_incident_notifications_topic"
        app  = "xdr-shared"
      }

      sub = {
        xsoar_artifacts_extraction_incident_notifications_sub = {
          name                 = "xsoar-artifacts-extraction-incident-notifications-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "xsoar_artifacts_extraction_incident_notifications_sub"
            app  = "xdr"
          }
        }
      }
    }
    analytics_detection_hits = {
      name = "analytics-detection-hits-${var.lcaas}"
      labels = {
        name = "analytics-detection-hits"
        app  = "xdr"
      }
      sub = {
        alerts_emitter = {
          name                 = "alerts-emitter"
          message_retention_duration = lookup(var.overrides, "alerts_emitter_message_retention_duration", local.sub_message_retention_duration)
          ack_deadline_seconds = 130
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "alerts-emitter"
            app  = "xdr"
          }
        }
      }
    }

    bq_errors_topic = {
      name = "bq-errors-${var.lcaas}"
      labels = {
        name = "bq_errors_topic"
        app  = "xdr"
      }
      sub = {
        edr_bq_errors_sub = {
          name                 = "edr-bq-errors-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "edr_bq_errors_sub"
            app  = "xdr"
          }
        }
      }
    }
    cloud_accounts = {
      name = "cloud-accounts-${var.lcaas}"
      labels = {
        name = "cloud_accounts_topic"
        app  = "xdr"
      }
      sub = {
        cloud_accounts_log_collection_sub = {
          name                 = "cloud-accounts-log-collection-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "cloud_accounts_log_collection_sub"
            app  = "xdr"
          }
        }
        xsoar_cloud_accounts_sub = {
          name                 = "xsoar-cloud-accounts-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "xsoar_cloud_accounts_sub"
            app  = "xdr"
          }
        }
        dspm_oo_boarder_cloud_accounts_sub = {
	  name                 = "dspm-oo-boarder-cloud-accounts-${var.lcaas}-sub"
	  enabled              = var.enable_cloud_posture
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-oo-boarder-cloud-accounts"
            app  = "dspm"
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-oo-boarder-cloud-accounts-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
        cwp_scan_spec_manager_cloud_accounts_sub = {
          name                 = "cwp-scan-spec-manager-cloud-accounts-${var.lcaas}-sub"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "cwp-scan-spec-manager-cloud-accounts-sub"
            app  = "cwp"
          }
        }
      }
    }
    cloud_accounts_full_discovery_topic = {
      enabled = local.enable_cortex_platform
      name    = "cloud-accounts-full-discovery-${var.lcaas}"
      labels  = {
        name = "cloud_accounts_topic"
        app  = "platform"
      }
      sub = {
        cloud_assets_full_discovery_sub = {
          name                 = "cloud-assets-full-discovery-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "cloud-assets-discovery"
            app  = "platform"
          }
        }
      }
    }
    collection_responses_topic = {
      enabled = local.enable_cortex_platform
      name = "collection-responses-${var.lcaas}"
      labels = {
        name = "collection_responses_topic"
        app  = "xdr"
      }
      iam = {
        harvester_publisher_responses = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-harvester@xdr-harvester-${var.viso_env}-01.iam.gserviceaccount.com"
        }
        hydra_publisher_responses = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        collection_responses_sub = {
          name                 = "collection-responses-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "collection_responses_sub"
            app  = "xdr"
          }
        }
      }
    }
    collection_priority_topic = {
      enabled = local.enable_cortex_platform
      name = "collection-priority-${var.lcaas}"
      labels = {
        name = "collection_priority_topic"
        app  = "xdr"
      }
      iam = {
        kube_sa_harvester = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-harvester@xdr-harvester-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        collection_priority_sub = {
          name                 = "collection-priority-sub"
          ack_deadline_seconds = 180
          message_retention_duration = "172800s"
          labels = {
            name = "collection_priority_sub"
            app  = "xdr"
          }
        }
      }
    }
    ca_collection_fsi_priority1_tasks_topic = {
      name = "ca-collection-fsi-priority1-tasks-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_fsi_priority1_tasks_topic"
        app  = "cortex-platform"
      }
      sub = {
        ca_collection_fsi_priority1_tasks_sub = {
          name                 = "ca-collection-fsi-priority1-tasks-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          message_retention_duration = "172800s"
          labels = {
            name = "ca-collection-fsi-priority1-tasks-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ca_collection_fsi_priority2_tasks_topic = {
      name = "ca-collection-fsi-priority2-tasks-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_fsi_priority2_tasks_topic"
        app  = "cortex-platform"
      }
      sub = {
        ca_collection_fsi_priority2_tasks_sub = {
          name                 = "ca-collection-fsi-priority2-tasks-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          message_retention_duration = "172800s"
          labels = {
            name = "ca-collection-fsi-priority2-tasks-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ca_collection_fsi_priority3_tasks_topic = {
      name = "ca-collection-fsi-priority3-tasks-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_fsi_priority3_tasks_topic"
        app  = "cortex-platform"
      }
      sub = {
        ca_collection_fsi_priority3_tasks_sub = {
          name                 = "ca-collection-fsi-priority3-tasks-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          message_retention_duration = "172800s"
          labels = {
            name = "ca-collection-fsi-priority3-tasks-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ca_collection_fsi_priority4_tasks_topic = {
      name = "ca-collection-fsi-priority4-tasks-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_fsi_priority4_tasks_topic"
        app  = "cortex-platform"
      }
      sub = {
        ca_collection_fsi_priority4_tasks_sub = {
          name                 = "ca-collection-fsi-priority4-tasks-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          message_retention_duration = "172800s"
          labels = {
            name = "ca-collection-fsi-priority4-tasks-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ca_collection_fsi_priority5_tasks_topic = {
      name = "ca-collection-fsi-priority5-tasks-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_fsi_priority5_tasks_topic"
        app  = "cortex-platform"
      }
      sub = {
        ca_collection_fsi_priority5_tasks_sub = {
          name                 = "ca-collection-fsi-priority5-tasks-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          message_retention_duration = "172800s"
          labels = {
            name = "ca-collection-fsi-priority5-tasks-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ca_collection_service_bus_topic = {
      name = "ca-collection-service-bus-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_service_bus_topic"
        app  = "xdr"
      }
      sub = {
        ca_collection_service_bus_sub = {
          name                 = "ca-collection-service-bus-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "ca-collection-service-bus-${var.lcaas}-sub"
            app  = "xdr"
          }
        }
      }
    }
    ca_collection_eai_tasks_topic = {
      name = "ca-collection-eai-tasks-${var.lcaas}"
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      labels = {
        name = "ca_collection_eai_tasks_topic"
        app  = "xdr"
      }
      sub = {
        ca_collection_eai_tasks_sub = {
          name                 = "ca-collection-eai-tasks-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          message_retention_duration = "172800s"
          labels = {
            name = "ca-collection-eai-tasks-${var.lcaas}-sub"
            app  = "xdr"
          }
        }
      }
    }
    cloud_onboarding_tasks = {
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      name   = "cloud-onboarding-tasks-${var.lcaas}"
      labels = {
        name = "cloud_onboarding_tasks"
        app  = "xdr"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        cloud_onboarding_tasks_sub = {
          enabled              = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
          name                 = "cloud-onboarding-tasks-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "cloud_onboarding_tasks_sub"
            app  = "xdr"
          }
        }
      }
    }
    cloud_health_monitoring = {
      name = "cloud-health-monitoring-${var.lcaas}"
      labels = {
        name = "cloud_health_monitoring"
        app  = "xdr"
      }
      sub = {
        cloud_health_monitoring_sub = {
          name                 = "cloud-health-monitoring-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "cloud_health_monitoring_sub"
            app  = "xdr"
          }
        }
      }
    }
    cloud_health_monitoring_statuses = {
      name = "cloud-health-monitoring-statuses-${var.lcaas}"
      labels = {
        name = "cloud_health_monitoring_statuses"
        app  = "xdr"
      }
      sub = {
        cloud_health_monitoring_statuses_sub = {
          name                       = "cloud-health-monitoring-statuses-sub"
          ack_deadline_seconds       = 180
          message_retention_duration = "86400s"
          labels = {
            name = "cloud_health_monitoring_statuses_sub"
            app  = "xdr"
          }
        }
      }
    }
    chrome_app = {
      name = "chrome-app-topic"
      labels = {
        name = "chrome_app_topic"
        app  = "xdr"
      }
      sub = {
        chrome_app = {
          name                 = "chrome-app-subscription"
          ack_deadline_seconds = 600
          labels = {
            name = "chrome_app_subscription"
            app  = "xdr"
          }
        }
      }
    }
    dp_uai_asset_refresh = {
      name = "dp-uai-asset-refresh-${var.lcaas}"
      labels = {
        name = "dp_uai_asset_refres_topic"
        app  = "platform"
      }
      sub = {
        dp_uai_asset_refresh_sub = {
          name = "dp-uai-asset-refresh-${var.lcaas}-sub"
          labels = {
            name = "dp_uai_asset_refresh_sub"
            app  = "platform"
          }
        }
      }
    }
    dp_finding_emits = {
      name = "dp-finding-emits-${var.lcaas}"
      labels = {
        name = "dp_finding_emits_topic"
        app  = "platform"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        dp_finding_emits_sub = {
          name                 = "dp-finding-emits-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dp_finding_emits_sub"
            app  = "platform"
          }
        }
      }
    }
    dp_finding_ingestion_errors = {
      name = "dp-finding-ingestion-errors-${var.lcaas}"
      labels = {
        name = "dp_finding_ingestion_errors"
        app  = "platform"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }

      sub = {
        dp_finding_ingestion_errors_ciem_sub = {
          name                 = "dp-finding-ingestion-errors-ciem-${var.lcaas}-sub"
          filter               = "attributes.product = \"CIEM\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "dp-finding-ingestion-errors-ciem-sub"
            app  = "ciem"
          }
        },
        dp_finding_ingestion_errors_cas_sub = {
          name                 = "dp-finding-ingestion-errors-cas-${var.lcaas}-sub"
          filter               = "attributes.product = \"CAS\""
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "dp_finding_ingestion_errors_cas_sub"
            app  = "cas"
          }
        },
        dp_finding_ingestion_errors_new_persistence_cas_sub = {
          name                 = "dp-finding-ingestion-errors-new-persistence-cas-${var.lcaas}-sub"
          filter               = "attributes.product = \"CAS\""
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "dp_finding_ingestion_errors_new_persistence_cas_sub"
            app  = "cas"
          }
        },
        dp_finding_ingestion_errors_risk_sub = {
          name                 = "dp-finding-ingestion-errors-risk-sub-${var.lcaas}"
          filter               = "attributes.product = \"API_SECURITY\""
          ack_deadline_seconds = 600
          labels = {
            name = "dp_finding_ingestion_errors_risk_sub"
            app  = "platform"
          }
        },
        dp_finding_ingestion_errors_itdr_sub = {
          enabled = var.enable_itdr
          name                 = "dp-finding-ingestion-errors-${var.lcaas}-itdr-sub"
          filter               = "attributes.product = \"ITDR\""
          ack_deadline_seconds = 600
          labels = {
            name = "dp_finding_ingestion_errors_itdr_sub"
            app  = "platform"
          }
        },
        dp_finding_ingestion_errors_asm_sub = {
          name                 = "dp-finding-ingestion-errors-asm-${var.lcaas}-sub"
          filter               = "attributes.product = \"ASM\""
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "dp-finding-ingestion-errors-asm-sub"
            app  = "asm"
          }
        },
        dp_finding_ingestion_errors_platform_sub = {
          name                 = "dp-finding-ingestion-errors-${var.lcaas}-platform-sub"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          filter               = "attributes.product = \"Platform Discovery\""
          labels = {
            name = "dp-finding-ingestion-errors-platform-sub"
            app  = "xdr"
          }
        }
      }
    }
    dml_broker_wef_topic = {
      name = "broker-wef-${var.lcaas}"
      labels = {
        name = "dml_broker_wef_topic"
        app  = "dml"
      }
      iam = {
        agent_gateway_broker_wef_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        dml_broker_wef_sub = {
          name                 = "dml_broker_wef"
          ack_deadline_seconds = 600
          labels = {
            name = "dml_broker_wef_sub"
            app  = "dml"
          }
        }
      }
    }
    dp_asset_discovery_topic = {
      enabled = (local.enable_cortex_platform && contains(["ent_plus"], var.product_code)) || var.enable_cloud_posture
      name = "dp-asset-discovery-${var.lcaas}"
      labels = {
        name = "dp-asset-discovery"
        app  = "dml"
      }
      sub = {
        dp_asset_discovery_subs = {
          name                 = "dp-asset-discovery-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "dp-asset-discovery-sub"
            app  = "dml"
          }
        }
      }
    }
    dp_asset_association_change_feed_topic = {
      name = "dp-asset-association-change-feed-${var.lcaas}"
      labels = {
        name = "dp-asset-association-change-feed"
        app  = "dml"
      }
      sub = {
        dp_asset_association_change_feed_sub = {
          name                 = "dp-asset-association-change-feed-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-asset-association-change-feed-sub"
            app  = "dml"
          }
        },
        dp_asset_association_change_feed_uvem_netscan_sub = {
          name = "dp-asset-association-change-feed-uvem-netscan-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "dp-asset-association-change-feed-uvem-netscan-sub"
            app = "uvem"
          }
        }
      }
    }
    dp_uai_asset_association_observations_topic = {
      name = "dp-uai-asset-association-observations-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-association-observations"
        app  = "dml"
      }
      sub = {
        dp_uai_asset_observations_sub = {
          name                 = "dp-uai-asset-association-observations-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-association-observations-sub"
            app  = "dml"
          }
        }
      }
    }
    dp_uai_asset_association_ingestion_errors_topic = {
      name = "dp-uai-asset-association-ingestion-errors-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-association-ingestion-errors"
        app  = "dml"
      }
      sub = {
        dp_uai_asset_association_ingestion_errors_sub = {
          name                 = "dp-uai-asset-association-ingestion-errors-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-association-ingestion-errors-sub"
            app  = "dml"
          }
        }
        dp_uai_asset_association_ingestion_errors_uvem_netscan_sub = {
          name                 = "dp-uai-asset-association-ingestion-errors-uvem-netscan-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-association-ingestion-errors-uvem-netscan-sub"
            app  = "uvem"
          }
        }
      }
    }
    dp_uai_asset_migration_errors_topic = {
      name = "dp-uai-asset-migration-errors-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-migration-errors"
        app  = "dml"
      }
      message_retention_duration = "432000s"
      sub = {
          dp_uai_asset_migration_errors_platform = {
              name                 = "dp-uai-asset-migration-errors-${var.lcaas}-platform-sub"
              enabled              = local.enable_cortex_platform
              ack_deadline_seconds = 600
              filter               = "attributes.product = \"Platform Discovery\""
              labels = {
                name = "dp-uai-asset-migration-errors-platform"
                app  = "xdr"
              }
        }
      }
    }
    dp_uai_asset_observations_topic = {
      name = "dp-uai-asset-observations-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-observations"
        app  = "dml"
      }
      sub = {
        dp_uai_asset_observations_subs = {
          name                 = "dp-uai-asset-observations-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-observations-sub"
            app  = "dml"
          }
        }

      }
    }
    dp_uai_asset_ingestion_errors_topic = {
      name = "dp-uai-asset-ingestion-errors-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-ingestion-errors"
        app  = "dml"
      }
      sub = {
        dp_uai_asset_ingestion_errors_ciem_sub = {
          name                 = "dp-uai-asset-ingestion-errors-ciem-${var.lcaas}-sub"
          filter               = "attributes.product = \"CIEM\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "dp-uai-asset-ingestion-errors-ciem-sub"
            app  = "ciem"
          }
        },
        dp_uai_asset_ingestion_errors_cas_sub = {
          name                 = "dp-uai-asset-ingestion-errors-cas-${var.lcaas}-sub"
          filter               = "attributes.product = \"CAS\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "dp-uai-asset-ingestion-errors-cas-sub"
            app  = "cas"
          }
        },
        dp_uai_asset_ingestion_errors_new_persistence_cas_sub = {
          name                 = "dp-uai-asset-ingestion-errors-new-persistence-cas-${var.lcaas}-sub"
          filter               = "attributes.product = \"CAS\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "dp_uai_asset_ingestion_errors_new_persistence_cas_sub"
            app  = "cas"
          }
        },
        apisec_dp_uai_asset_ingestion_errors_sub = {
          name                 = "apisec-dp-uai-asset-ingestion-errors-sub-${var.lcaas}"
          filter               = "attributes.product = \"API_SECURITY\""
          ack_deadline_seconds = 600
          labels = {
            name = "apisec-dp-uai-asset-ingestion-errors-sub"
            app  = "xdr"
          }
        },
        dp_uai_asset_ingestion_errors_asm_sub = {
          name                 = "dp-uai-asset-ingestion-errors-asm-${var.lcaas}-sub"
          filter               = "attributes.product = \"ASM\""
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-ingestion-errors-asm-sub"
            app  = "asm"
          }
        },
        dp_uai_asset_ingestion_errors_uvem_netscan_sub = {
          name                 = "dp-uai-asset-ingestion-errors-uvem-netscan-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "dp-uai-asset-ingestion-errors-uvem-netscan-sub"
            app  = "uvem"
          }
        },
        dp_uai_asset_ingestion_errors_platform = {
          name                 = "dp-uai-asset-ingestion-errors-${var.lcaas}-platform-sub"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          filter               = "attributes.product = \"Platform Discovery\""
          labels = {
            name = "dp-uai-asset-ingestion-errors-platform"
            app  = "xdr"
          }
        },
        dspm_datasource_dp_uai_asset_ingestion_errors_sub = {
          name                 = "dspm-datasource-dp-uai-asset-ingestion-errors-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          filter               = "attributes.product = \"DSPM\""
          labels = {
            name = "dspm-datasource-dp-uai-asset-ingestion-errors-sub"
            app  = "dspm"
          }
        },
        itdr_data_pipeline_asset_ingestion_errors_sub = {
          enabled = var.enable_itdr
          name                 = "itdr-data-pipeline-asset-ingestion-errors-${var.lcaas}-sub"
          filter               = "attributes.product = \"ITDR\""
          ack_deadline_seconds = 600
          labels = {
            name = "itdr-data-pipeline-asset-ingestion-errors-sub"
            app  = "itdr-data-pipeline"
          }
        },
      }
    }
    dp_uai_asset_change_feed_topic = {
      name = "dp-uai-asset-change-feed-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-change-feed"
        app  = "dml"
      }
      sub = {
        cloudsec_asset_change_sub = {
          enabled = local.enable_cortex_platform
          name    = "cloudsec-asset-change-sub-${var.lcaas}"
          labels = {
            name = "cloudsec-asset-change-subscription"
            app  = "cloudsec"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              minimum_backoff = "10s"
            }
          ]
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/cloudsec-inline-scanner-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
        dp_uai_asset_change_feed_uvem_vxp_sub = {
          name                 = "dp-uai-asset-change-feed-${var.lcaas}-uvem-vxp-sub"
          enabled              = local.enable_cortex_platform
          filter               = "attributes.product = \"INBOUND_VM\""
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-change-feed-uvem-vxp-sub"
            app  = "uvem-vxp"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
        }
        dp_uai_asset_change_feed_uvem_netscan_sub = {
          name = "dp-uai-asset-change-feed-uvem-netscan-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-change-feed-uvem-netscan-sub"
            app  = "uvem"
          }
        }
        dspm_fda_asset_change_feed_sub = {
          name                 = "dspm-fda-asset-change-feed-${var.lcaas}-sub"
          enabled              = var.enable_cloud_posture
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "fda-asset-change-feed-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-fda-asset-change-feed-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
        }
        dspm_mac_aispm_asset_change_feed_sub = {
          name                 = "dspm-mac-aispm-asset-change-feed-${var.lcaas}-sub"
          enabled              = var.enable_cloud_posture
          filter                     = "attributes.product = \"AISPM\""
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "mac-aispm-asset-change-feed-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-mac-aispm-asset-change-feed-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
        }
        dspm_crespo_resource_listener_asset_change_feed_sub = {
          name                 = "dspm-crespo-resource-listener-asset-change-feed-${var.lcaas}-sub"
          enabled              = var.enable_cloud_posture
          ack_deadline_seconds = 600
          labels = {
            name = "crespo-resource-listener-asset-change-feed-sub"
            app  = "dspm"
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-crespo-resource-listener-asset-change-feed-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
        cwp_image_analyzer_asset_change_feed_sub = {
          name                 = "cwp-image-analyzer-asset-change-feed-${var.lcaas}-sub"
          enabled              = local.enable_cortex_platform
          labels = {
            name = "cwp-image-analyzer-asset-change-feed-sub"
            app  = "cwp"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_registry_scan_orchestrator_asset_change_feed_sub = {
          name                 = "cwp-registry-scan-orchestrator-asset-change-feed-${var.lcaas}-sub"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "cwp-registry-scan-orchestrator-asset-change-feed-sub"
            app  = "cwp"
          }
        }
        cwp_registry_discovery_orchestrator_asset_change_feed_sub = {
          name                 = "cwp-registry-disc-orchestrator-asset-change-feed-${var.lcaas}-sub"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "cwp-registry-disc-orchestrator-asset-change-feed-sub"
            app  = "cwp"
          }
        }
        cwp_serverless_scan_orchestrator_asset_change_feed_sub = {
          name                 = "cwp-serverless-scan-orchestrator-asset-change-feed-${var.lcaas}-sub"
          enabled              = local.enable_cortex_platform
          filter               = "attributes.product = \"Platform Discovery\""
          ack_deadline_seconds = 600
          labels = {
            name = "cwp-serverless-scan-orchestrator-asset-change-feed-sub"
            app  = "cwp"
          }
        }
        vxp_comp_controls_asset_change_feed_sub = {
          name                 = "vxp-comp-controls-asset-change-feed-${var.lcaas}-sub"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "vxp-comp-controls-asset-change-feed-sub"
            app  = "vxp"
          }
        }
      }
    }
    dp_uai_asset_groups_change_feed_topic = {
      name = "dp-uai-asset-groups-change-feed-${var.lcaas}"
      labels = {
        name = "dp-uai-asset-groups-change-feed"
        app  = "dml"
      }
      sub = {
      }
    }
    cloudsec_inline_scanner_dlq = {
      name    = "cloudsec-inline-scanner-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cloudsec-inline-scanner-dlq"
        app  = "cloudsec"
      }
      message_retention_duration = "432000s"
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        },
      }
      sub = {
        cloudsec-inline-scanner-dlq-sub = {
          enabled = local.enable_cortex_platform
          name    = "cloudsec-inline-scanner-dlq-${var.lcaas}"
          labels = {
            name = "cloudsec-inline-scanner-dlq"
            app  = "cloudsec"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    dp_collector_assets_topic = {
      name    = "dp-collector-assets-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dp-collector-assets"
        app  = "dml"
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        dp_collector_assets_subs = {
          name                 = "dp-collector-assets-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-collector-assets-sub"
            app  = "dml"
          }
        }
      }
    }
    dp_uai_asset_migrations_topic = {
      name    = "dp-uai-asset-migrations-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dp-uai-asset-migrations"
        app  = "dml"
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        dp_uai_asset_migrations_subs = {
          name                 = "dp-uai-asset-migrations-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            name = "dp-uai-asset-migrations-sub"
            app  = "dml"
          }
        }
      }
    }
    edr_raw_topic = {
      name = "edr-raw-${var.lcaas}"
      labels = {
        name = "edr_raw_topic"
        app  = "xdr"
      }
      iam = {
        agent_gateway_edr_raw_topic_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        edr_raw_sub = {
          name                         = "edr-raw-pipeline-${var.lcaas}-sub"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          ack_deadline_seconds         = 180
          retain_acked_messages        = true
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          labels = {
            name = "edr_raw_pipeline_sub"
            app  = "xdr"
          }
        }
      }
    }
    edr_topic = {
      name = "edr-${var.lcaas}"
      labels = {
        name = "edr_topic"
        app  = "xdr-shared"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        edr_sub_matchingservice = {
	  name                 = "edr-matching-service"
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "60s"
          }]
          ack_deadline_seconds = 130
          labels = {
            name = "edr_matchingservice_sub"
            app  = "xdr"
          }
        }
      }
    }
    external_agent_management_messages_topic = {
      name = "external-agent-management-messages-${var.lcaas}"
      labels = {
        name = "external-agent-management-messages"
        app  = "xdr"
      }
      sub = {
        external_agent_management_messages_sub = {
          name = "external-agent-management-messages-${var.lcaas}-sub"
          labels = {
            name = "external-agent-management-messages-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    external_topic = {
      name = "external-${var.lcaas}"
      labels = {
        name = "external_topic"
        app  = "xdr-shared"
      }
      sub = {
        ext_matching_service = {
	  name                 = "ext-matching-service"
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "60s"
          }]
          ack_deadline_seconds = 130
          labels = {
            name = "external_sub"
            app  = "xdr"
          }
        }
      }
    }
    identity_risk_score_updates = {
      enabled = (var.is_xdr || var.is_xsiam) && var.enable_pipeline
      name    = "identity-risk-score-updates-${var.lcaas}"
      labels = {
        app  = "xdr"
        name = "identity_risk_score_updates_topic"
      }
      sub = {
        risk_score_cie_updates = {
          name                 = "risk-score-cie-updates"
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
          labels = {
            app  = "xdr"
            name = "risk_score_cie_updates_sub"
          }
        }
      }
    }
    uvem_vxp_protofinding_emits_dlq = {
      name = "uvem-vxp-protofinding-emits-dlq-${var.lcaas}"
      labels = {
        name = "uvem_vxp_protofinding_emits_dlq"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        uvem_vxp_protofinding_emits_dlq_sub = {
          name = "uvem-vxp-protofinding-emits-dlq-${var.lcaas}"
          labels = {
            name = "uvem_vxp_protofinding_emits_sub_dlq"
            app  = "xdr"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    uvem_protofinding_emits_topic = {
      name = "uvem-protofinding-emits-${var.lcaas}"
      labels = {
        name = "uvem_protofinding_emits"
        app  = "xdr"
      }
      sub = {
        uvem_vxp_protofinding_emits_sub = {
          name = "uvem-vxp-protofinding-emits-${var.lcaas}-sub"
          labels = {
            name = "uvem_vxp_protofinding_emits_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/uvem-vxp-protofinding-emits-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }
    uvem_vxp_events_dlq = {
      name = "uvem-vxp-events-dlq-${var.lcaas}"
      labels = {
        name = "uvem_vxp_events_dlq"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        uvem_vxp_events_dlq_sub = {
          name = "uvem-vxp-events-dlq-${var.lcaas}"
          labels = {
            name = "uvem_vxp_events_sub_dlq"
            app  = "xdr"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    uvem_vip_events_dlq = {
      name = "uvem-vip-events-dlq-${var.lcaas}"
      labels = {
        name = "uvem_vip_events_dlq"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        uvem_vxp_events_dlq_sub = {
          name = "uvem-vip-events-dlq-${var.lcaas}"
          labels = {
            name = "uvem_vip_events_sub_dlq"
            app  = "xdr"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    uvem_events_topic = {
      name = "uvem-events-${var.lcaas}"
      labels = {
        name = "uvem_events"
        app  = "xdr"
      }
      sub = {
        uvem_vxp_events_sub = {
          name = "uvem-vxp-events-${var.lcaas}-sub"
          labels = {
            name = "uvem_vxp_events_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/uvem-vxp-events-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
        uvem_vip_events_sub = {
          name = "uvem-vip-events-${var.lcaas}-sub"
          labels = {
            name = "uvem_vip_events_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/uvem-vip-events-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }
    uvem_protofinding_ingestion_errors_dlq = {
      name = "uvem-protofinding-ingestion-errors-vxp-dlq-${var.lcaas}"
      labels = {
        name = "uvem_protofinding_ingestion_errors_dlq"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        uvem_protofinding_ingestion_errors_dlq_sub = {
          name = "uvem-protofinding-ingestion-errors-vxp-dlq-${var.lcaas}"
          labels = {
            name = "uvem_protofinding_ingestion_errors_dlq_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    uvem_netscan_results_topic = {
      name = "uvem-netscan-results-${var.lcaas}"
      labels = {
        name = "uvem_netscan_results"
        app = "xdr"
      }
      sub = {
        uvem_netscan_results_sub = {
          name = "uvem-netscan-results-sub-${var.lcaas}"
          labels = {
            name = "uvem_netscan_results_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dp_finding_ingestion_errors_vxp_dlq = {
      name = "dp-finding-ingestion-errors-vxp-dlq-${var.lcaas}"
      labels = {
        name = "dp_finding_ingestion_errors_vxp_dlq"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dp_finding_ingestion_errors_vxp_dlq_sun = {
          name = "dp-finding-ingestion-errors-vxp-dlq-${var.lcaas}"
          labels = {
            name = "dp_finding_ingestion_errors_vxp_dlq_sun"
            app  = "xdr"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    uvem_protofinding_ingestion_errors_topic = {
      name = "uvem-protofinding-ingestion-errors-${var.lcaas}"
      labels = {
        name = "uvem_protofinding_ingestion_errors"
        app  = "xdr"
      }
      sub = {
        uvem_protofinding_ingestion_errors_vxp_sub = {
          name = "uvem-protofinding-ingestion-errors-vxp-${var.lcaas}-sub"
          labels = {
            name = "uvem_protofinding_ingestion_errors_vxp_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/uvem-protofinding-ingestion-errors-vxp-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
        dp_finding_ingestion_errors_vxp_sub = {
          name = "dp-finding-ingestion-errors-vxp-${var.lcaas}-sub"
          labels = {
            name = "dp_finding_ingestion_errors_vxp_sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dp-finding-ingestion-errors-vxp-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }
    cortex_gateway_messages_processor = {
      name = "cortex-gw-messages-processor-${var.lcaas}"
      labels = {
        name = "cortex_gw_messages_processor"
        app  = "xdr"
      }
      iam = {
        cortex_gateway_messages_processor_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:backend@xdr-gateway-${var.multi_project_postfix}-01.iam.gserviceaccount.com"
        },
        viso_router_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:viso-router@xdr-gateway-${var.multi_project_postfix}-01.iam.gserviceaccount.com"
        },
        cortex_gateway_celery_worker_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:celery-worker@xdr-gateway-${var.multi_project_postfix}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        cortex_gateway_messages_processor_sub = {
          name                         = "cortex-gw-messages-processor-sub-${var.lcaas}"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", true)
          ack_deadline_seconds         = 180
          retain_acked_messages        = true
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          labels = {
            name = "cortex_gw_messages_processor_sub"
            app  = "xdr"
          }
        }
      }
    }
    cold_storage_aggregation = {
      retain_acked_messages      = false
      enabled                    = var.cold_retention || local.enable_cortex_platform
      name                       = "cold_storage_datasets_aggregator-${var.lcaas}"
      message_retention_duration = "604800s"
      labels = {
        name = "cold_storage_aggregation_topic"
        app  = "dml"
      }
      sub = {
        cold-storage-aggregation-sub = {
          name                         = "cold_storage_datasets_aggregator-${var.lcaas}-sub"
          ack_deadline_seconds         = 600
          enable_exactly_once_delivery = true
          labels = {
            name = "cold_storage_aggregation_sub"
            app  = "dml"
          }
        }
      }
    }
    bq_stats_topic = {
      name                       = "bq-stats-${var.lcaas}"
      message_retention_duration = "604800s"
      labels = {
        name = "bq_stats_topic"
        app  = "xdr"
      }
      sub = {
        bq_stats_sub = {
          name                 = "bq-stats-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "bq_stats_sub"
            app  = "xdr"
          }
        }
      }
    }
    edr_vdi_topic = {
      name = "edr-vdi-${var.lcaas}"
      labels = {
        name = "edr_vdi_topic"
        app  = "xdr"
      }
      sub = {
        edr_vdi_sub = {
          name                 = "edr-vdi-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "edr_vdi_sub"
            app  = "xdr"
          }
        }
      }
    }
    event_forwarding = {
      enabled = var.egress_enabled
      name    = "event-forwarding-${var.lcaas}"
      labels = {
        name = "event_forwarding_topic"
        app  = "xdr"
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        event-forwarding-sub = {
          name                 = "event-forwarding-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "event_forwarding_sub"
            app  = "xdr"
          }
          iam = {
            event_forwarding_pubsub_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:event-forwarding-viewer@${var.project_id}.iam.gserviceaccount.com"
            }
          }
        }
      }
    }
    event_forwarding_external = {
      enabled = var.egress_enabled
      name    = "event-forwarding-external-${var.lcaas}"
      labels = {
        name = "event_forwarding_external_topic"
        app  = "xdr"
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        event-forwarding-external-sub = {
          name                 = "event-forwarding-external-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "event_forwarding_external_sub"
            app  = "xdr"
          }
          iam = {
            event_forwarding_pubsub_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:event-forwarding-viewer@${var.project_id}.iam.gserviceaccount.com"
            }
          }
        }
      }
    }
    external_integration_data_forwarder = {
      name = "external-integration-data-forwarder-${var.lcaas}"
      labels = {
        name = "external_integration_topic"
        app  = "prisma"
      }
      sub = {
        external_integration_data_forwarder_sub = {
          name = "external-integration-data-forwarder-${var.lcaas}-sub"
          labels = {
            name = "external_integration_sub"
            app  = "prisma"
          }
        }
      }
    }
    external_logs_topic = {
      name = "ext-logs-${var.lcaas}"
      labels = {
        name = "external_logs_topic"
        app  = "dml"
      }
      iam = {
        agent_gateway_topic_ext_logs_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
        harvester_ext_logs_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-harvester@xdr-harvester-${var.viso_env}-01.iam.gserviceaccount.com"
        }
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        inr-ext-logs-sub = {
          name                 = "inr-ext-logs-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "inr_ext_logs_sub"
            app  = "xdr"
          }
        }
        xql-ext-logs-sub = {
          name                         = "xql-ext-logs-${var.lcaas}-sub"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          ack_deadline_seconds         = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          labels = {
            name = "xql-ext-logs-sub"
            app  = "xql"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
        }
        xql-ext-logs-2-sub = {
          enabled                      = lookup(var.overrides, "enable_xql_fdr_engine", false)
          name                         = "xql-ext-logs-2-${var.lcaas}"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          ack_deadline_seconds         = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          labels = {
            name = "xql-ext-logs-2-sub"
            app  = "xql"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
        }
      }
    }
    external_notifications_topic = {
      name = "ext-notifications-${var.lcaas}"
      labels = {
        name = "external_notifications_topic"
        app  = "xdr-shared"
      }
      sub = {
        external_notifications_sub = {
          name                 = "ext-notifications-${var.lcaas}-sub"
          ack_deadline_seconds = 60
          labels = {
            name = "external_notifications_sub"
            app  = "xdr-shared"
          }
        }
      }
    }
    ingestion_errors_topic = {
      name = "ingestion-errors-${var.lcaas}"
      labels = {
        name = "ingestion_errors_topic"
        app  = "xdr"
      }
      sub = {
        ingestion_errors_sub_1 = {
          name                 = "error-sink1"
          ack_deadline_seconds = 600
          labels = {
            name = "ingestion_errors_sub"
            app  = "xdr"
          }
        }
      }
    }
    forensics_processor_topic = {
      message_retention_duration = "604800s"
      name                       = "forensics-processor-${var.lcaas}"
      labels = {
        name = "forensics_processor_topic"
        app  = "xdr"
      }
      sub = {
        forensics_processor_sub = {
          name                         = "forensics-processor-sub-${var.lcaas}"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", true)
          ack_deadline_seconds         = 600
          labels = {
            name = "forensics_processor_sub"
            app  = "xdr"
          }
        }
      }
    }
    lcaas_out_alerts_topic = {
      name = "lcaas-out-${var.lcaas}"
      labels = {
        name = "lcaas_out_alerts_topic"
        app  = "xdr"
      }
      sub = {
        lcaas_out_sub = {
          name                 = "lcaas-alerts"
          ack_deadline_seconds = 600
          labels = {
            name = "lcaas_out_alerts_sub"
            app  = "xdr"
          }
        }
      }
    }
    lcaas_topic = {
      name = "lcaas-${var.lcaas}"
      labels = {
        name = "lcaas_topic"
        app  = "xdr-shared"
      }
      iam = {
        add_gcs_member = {
          enabled = var.enable_replicator
          role    = "roles/pubsub.publisher"
          member  = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        lavawall_sub = {
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          name                         = "lavawall-${var.lcaas}"
          ack_deadline_seconds         = 120

          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          labels = {
            name = "lavawall-${var.lcaas}-sub"
            app  = "xdr"
          }
        }
      }
    }
    log_forwarding_topic = {
      name = "syslog-notification-${var.lcaas}"
      labels = {
        name = "log_forwarding_topic"
        app  = "xdr"
      }
      iam = {
        log_forwarding_st_topic_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-log-forwarding@xdr-log-forwarding-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        log_forwarding_sub = {
          name                 = "log-forwarding-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "log_forwarding_sub"
            app  = "xdr"
          }
        }
      }
    }
    log_processor_errors_topic = {
      name = "log-processor-errors-${var.lcaas}"
      labels = {
        name = "log_processor_errors_topic"
        app  = "xdr"
      }
      sub = {
        log_processor_errors_sub = {
          name                 = "log-processor-errors"
          ack_deadline_seconds = 180
          labels = {
            name = "log_processor_errors_sub"
            app  = "xdr"
          }
        }
      }
    }
    log_processor_topic = {
      name = "log-processor-${var.lcaas}"
      labels = {
        name = "log_processor_topic"
        app  = "xdr"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        log_processor_sub = {
          name                         = "log-processor"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          ack_deadline_seconds         = 600
          labels = {
            name = "log_processor_sub"
            app  = "xdr"
          }
        }
      }
    }
    object_mirroring_service_topic = {
      name = "object-mirroring-service-${var.lcaas}"
      labels = {
        name = "object_mirroring_service"
        app  = "xdr"
      }
      message_retention_duration = "604800s"
      retain_acked_messages = false
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
        pubsub_member_publisher_2 = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        object_mirroring_service_sub = {
          name = "object-mirroring-service-${var.lcaas}-sub"
          labels = {
            name = "object_mirroring_service_sub"
            app  = "xdr"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/object-mirroring-service-${var.lcaas}-dlq"
            max_delivery_attempts = 15
          }]
        }
      }
    }
    object_mirroring_service_topic_dlq = {
      name = "object-mirroring-service-${var.lcaas}-dlq"
      labels = {
        name = "object_mirroring_service_topic_dlq"
        app  = "xdr"
      }
      message_retention_duration = "604800s"
      retain_acked_messages = false
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        object_mirroring_service_sub_dlq = {
          name = "object-mirroring-service-${var.lcaas}-sub-dlq"
          labels = {
            name = "object_mirroring_service_sub_dlq"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    email_data = {
      name = "email-data-${var.lcaas}"
      labels = {
        name = "email-data"
        app  = "xsiam"
      }
      sub = {
        email_data_sub = {
          name                 = "email-data"
          ack_deadline_seconds = 600
          labels = {
            name = "email_data_sub"
            app  = "xsiam"
          }
        }
      }
    }
    email_data_attachments = {
      name = "email-data-attachments-${var.lcaas}"
      labels = {
        name = "email_data_attachments"
        app  = "xsiam"
      }
      sub = {
        email_data_attachments_sub = {
          name                 = "email-data-attachments"
          ack_deadline_seconds = 600
          labels = {
            name = "email_data_attachments_sub"
            app  = "xsiam"
          }
        }
      }
    }
    email_data_submitted_hashes = {
      name = "email-data-submitted-hashes-${var.lcaas}"
      labels = {
        name = "email_data_submitted_hashes"
        app  = "xsiam"
      }
      sub = {
        email_data_submitted_hashes_sub = {
          name                 = "email-data-submitted-hashes"
          ack_deadline_seconds = 60
          labels = {
            name = "email_data_submitted_hashes_sub"
            app  = "xsiam"
          }
        }
      }
    }
    mgmt_audit_notifier = {
      name = "mgmt-audit-notifier-${var.lcaas}"
      labels = {
        name = "mgmt_audit_notifier"
        app  = "xsoar"
      }
      sub = {
        mgmt_audit_notifier_sub = {
          name                 = "mgmt-audit-notifier"
          ack_deadline_seconds = 420
          labels = {
            name = "mgmt_audit_notifier_sub"
            app  = "xsoar"
          }
        }
      }
    }
    modification_topic = {
      name = "modification-topic-${var.lcaas}"
      labels = {
        name = "modification_topic"
        app  = "xdr"
      }
      sub = {
        modification_sub = {
          name                 = "modification-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "modification_sub"
            app  = "xdr"
          }
        }
      }
    }
    notifications_notifier = {
      name = "notifications-notifier-${var.lcaas}"
      labels = {
        name = "notifications_notifier"
        app  = "xsoar"
      }
      sub = {
        notifications_notifier_sub = {
          name                 = "notifications-notifier"
          ack_deadline_seconds = 420
          labels = {
            name = "notifications_notifier_sub"
            app  = "xsoar"
          }
        }
      }
    }
    permissions_auditing = {
      name = "permissions-auditing"
      labels = {
        name = "permissions_auditing"
        app  = "xdr"
      }
      iam = {
        permissions_auditing_publisher = {
          enabled = !var.is_fedramp
          role    = "roles/pubsub.publisher"
          member  = "serviceAccount:${local.permissions_sa}"
        }
      }
      sub = {
        permissions_auditing_sub = {
          name                 = "permissions-auditing-sub"
          ack_deadline_seconds = 420
          labels = {
            name = "permissions_auditing_sub"
            app  = "xdr"
          }
        }
      }
    }
    slack_notification_topic = {
      name = "slack-notification-${var.lcaas}"
      labels = {
        name = "slack_notification_topic"
        app  = "xdr"
      }
      sub = {
        slack_notification_sub = {
          name                 = "slack-notification-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "slack_notification_sub"
            app  = "xdr"
          }
        }
      }
    }
    stitched_topic = {
      name = "stitched-${var.lcaas}"
      labels = {
        name = "stitched-${var.lcaas}-topic"
        app  = "xdr"
      }
      sub = {
        stitched_sub = {
          name                 = "stitched-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "stitched-${var.lcaas}-sub"
            app  = "xdr"
          }
        }
      }
    }
    task_processor_topic = {
      name = "task-processor-${var.lcaas}"
      labels = {
        name = "task_processor_topic"
        app  = "xdr"
      }
      sub = {
        task_processor_sub = {
          name                 = "task-processor-${var.lcaas}-sub"
          ack_deadline_seconds = 360
          retry_policy = [
            {
              minimum_backoff = "10s"
              maximum_backoff = "360s"
            },
          ]
          labels = {
            name = "task_processor_sub"
            app  = "xdr"
          }
        }
      }
    }
    xql-deadletter-topic = {
      name = "xql-dl-${var.lcaas}"
      labels = {
        name = "xql-deadletter"
        app  = "xql"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        xql-deadletter-sub = {
          name                 = "xql-dl-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "600s"
          }]
          labels = {
            name = "xql-deadletter_sub"
            app  = "xql"
          }
        }
      }
    }
    xql-ingester-errors = {
      name = "xql-ingester-errors-${var.lcaas}"
      labels = {
        name = "xql-ingester-errors"
        app  = "xql"
      }
      sub = {
        xql-ingester-errors-sub = {
          name                 = "xql-ingester-errors-sub"
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "600s"
          }]
          labels = {
            name = "xql-ingester-errors-sub"
            app  = "xql"
          }
        }
      }
    }
    inventory_topic = {
      name = "inventory-${var.lcaas}"
      labels = {
        name = "inventory-${var.lcaas}-topic"
        app  = "xdr"
      }
      iam = {
        harvester_publisher_responses = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-harvester@xdr-harvester-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        inventory_sub = {
          name                 = "inventory-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "inventory-${var.lcaas}-sub"
            app  = "xdr"
          }
        }
      }
    }
    # XDR-37008
    dlq-collection-topic = {
      name = "dlq-collection-${var.lcaas}"
      labels = {
        name = "dlq-collection"
        app  = "collection"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        harvester_publisher_responses = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-harvester@xdr-harvester-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-collection-sub = {
          name                 = "dlq-collection-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "dlq-collection-sub"
            app  = "collection"
          }
        }
      }
    }

    # XDR-34598-Merge-Fix
    # Adding new Topic XDR-34598 -----------------
    dlq-ext-topic = {
      name = "dlq-ext-${var.lcaas}"
      labels = {
        name = "dlq-ext"
        app  = "ext" # verify Namespace
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-ext-sub = {
          name                 = "dlq-ext-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          # If not set, the default retry policy is applied. This generally implies that messages will be retried as soon as possible for healthy subscribers.
          /* retry_policy = [{  # Confirm
            maximum_backoff = "600s"
            minimum_backoff = "600s"
          }] */
          labels = {
            name = "dlq-ext-sub"
            app  = "ext"
          }
        }
      }
    }
    dlq-xql-topic = {
      name = "dlq-xql-${var.lcaas}"
      labels = {
        name = "dlq-xql"
        app  = "xql"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-xql-sub = {
          name                 = "dlq-xql-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "dlq-xql-sub"
            app  = "xql"
          }
        }
      }
    }
    dlq-pz-topic = {
      name = "dlq-pz-${var.lcaas}"
      labels = {
        name = "dlq-pz"
        app  = "pz"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-pz-sub = {
          name                 = "dlq-pz-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "dlq-pz-sub"
            app  = "pz"
          }
        }
      }
    }
    dlq-edr-topic = {
      name = "dlq-edr-${var.lcaas}"
      labels = {
        name = "dlq-edr"
        app  = "edr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-edr-sub = {
          name                 = "dlq-edr-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "dlq-edr-sub"
            app  = "edr"
          }
        }
      }
    }
    dlq-dms-topic = {
      name = "dlq-dms-${var.lcaas}"
      labels = {
        name = "dlq-dms"
        app  = "dms"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-dms-sub = {
          name                 = "dlq-dms-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "dlq-dms-sub"
            app  = "dms"
          }
        }
      }
    }
    playbook_execution_topic = {
      name = "playbook-execution-${var.lcaas}"
      labels = {
        name = "playbook-execution"
        app  = "xsoar"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        playbook_execution_sub = {
          name                 = "playbook-execution-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "10s"
          }]
          labels = {
            name = "playbook-execution-sub"
            app  = "xsoar"
          }
        }
      }
    }
    alerts_to_xsoar_topic = {
      name = "alerts-to-xsoar-${var.lcaas}"
      labels = {
        name = "alerts-to-xsoar"
        app  = "xsoar"
      }
      sub = {
        alerts_to_xsoar_sub = {
          name                 = "alerts-to-xsoar-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "alerts-to-xsoar-sub"
            app  = "xsoar"
          }
        }
      }
    }
    artifact_extraction_topic = {
      name = "artifact-extraction-${var.lcaas}"
      labels = {
        name = "artifact-extraction"
        app  = "xsoar"
      }
      sub = {
        artifact_extraction_sub = {
          name                 = "artifact-extraction-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "artifact-extraction-sub"
            app  = "xsoar"
          }
        }
      }
    }
    tech_support_file_retrieval_topic = {
      enabled = var.is_fedramp ? false : true
      name    = "tech-support-file-retrieval"
      labels = {
        name = "tech-support-file-retrieval"
        app  = "xdr"
      }
      iam = {
        tech_support_file_publisher = {
          enabled = var.is_fedramp ? false : true
          role    = "roles/pubsub.publisher"
          member  = "serviceAccount:tech-support-file-global-${local.tsf_sa_suffix}@xdr-shared-services-prod-eu-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        tech_support_file_retrieval_sub = {
          enabled              = var.is_fedramp ? false : true
          name                 = "tech-support-file-retrieval-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "tech-support-file-retrieval-sub"
            app  = "xdr"
          }
        }
      }
    }
    dml_script_results_topic = {
      enabled = true
      name    = "dml-script-results-${var.lcaas}"
      labels = {
        name = "dml-script-results-${var.lcaas}"
        app  = "dml"
      }
      sub = {
        dml_script_results_sub = {
          enabled              = true
          name                 = "dml-script-results-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "dml-script-results-sub${var.lcaas}"
            app  = "dml"
          }
        }
      }
    }
    xsoar_tim_indicator_failures_topic = {
      name = "xsoar-tim-indicator-failures-topic-${var.lcaas}"
      labels = {
        name = "xsoar-tim-indicator-failures-topic"
        app  = "xsoar"
      }
      sub = {
        xsoar_tim_indicator_failures_sub = {
          name                 = "xsoar-tim-indicator-failures-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          labels = {
            name = "xsoar-tim-indicator-failures-sub"
            app  = "xsoar"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
        }
      }
    }
    mail_events_topic = {
      name = "mail-events-${var.lcaas}"
      labels = {
        name = "mail_events_topic"
        app  = "xdr"
      }
      sub = {
        mail_events_sub = {
          name                 = "mail-events-${var.lcaas}-sub"
          ack_deadline_seconds = 180
          labels = {
            name = "mail_events_sub"
            app  = "xdr"
          }
        }
      }
    }
    metrics_aggregator_topic = {
      name = "metrics-aggregator-${var.lcaas}"
      labels = {
        name = "metrics-aggregator"
        app  = "xdr"
      }
      sub = {
        metrics_aggregator_sub = {
          name                         = "metrics-aggregator-${var.lcaas}-sub"
          ack_deadline_seconds         = 30
          retain_acked_messages        = true
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          enable_message_ordering      = lookup(var.overrides, "enable_message_ordering", true)
          labels = {
            name = "metrics-aggregator-sub"
            app  = "xdr"
          }
        }
      }
    }
    xpanse_manual_scan_results_topic = {
      enabled = var.enable_asm && var.viso_env != "prod-fr"
      name    = "xpanse-manual-scan-results-${var.lcaas}"
      labels = {
        name = "xpanse_manual_scan_results"
        app  = "xpanse"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        xpanse_manual_scan_results_sub = {
          name                 = "xpanse-manual-scan-results"
          ack_deadline_seconds = 600
          labels = {
            name = "xpanse_manual_scan_results"
            app  = "xpanse"
          }
        }
      }
    }
    xsoar_incidents_to_create_topic_dlq = {
      name = "xsoar-incidents-to-create-${var.lcaas}-dlq"
      labels = {
        name = "xsoar_incidents_to_create_topic_dlq"
        app  = "xsoar"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        xsoar_incidents_to_create_sub_dlq = {
          name = "xsoar-incidents-to-create-${var.lcaas}-sub-dlq"
          labels = {
            name = "xsoar_incidents_to_create_sub_dlq"
            app  = "xsoar"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    xsoar_incidents_to_create_topic = {
      name = "xsoar-incidents-to-create-${var.lcaas}"
      labels = {
        name = "xsoar_incidents_to_create_topic"
        app  = "xsoar"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
        pubsub_member_publisher_2 = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        xsoar_incidents_to_create_sub = {
          name = "xsoar-incidents-to-create-${var.lcaas}-sub"
          labels = {
            name = "xsoar_incidents_to_create_sub"
            app  = "xsoar"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 300
          retry_policy = [{
            maximum_backoff = "300s"
            minimum_backoff = "10s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/xsoar-incidents-to-create-${var.lcaas}-dlq"
            max_delivery_attempts = 15
          }]
        }
      }
    }
    xsoar_indicator_enrichment_results = {
      name = "xsoar-indicator-enrichment-results-${var.lcaas}"
      labels = {
        name = "xsoar-indicator-enrichment-results"
        app  = "xsoar"
      }
      sub = {
        xsoar_indicator_enrichment_results_sub = {
          name = "xsoar-indicator-enrichment-results-sub-${var.lcaas}"
          labels = {
            name = "xsoar-indicator-enrichment-results-sub"
            app  = "xsoar"
          }
          ack_deadline_seconds = 300
          retry_policy = [{
            maximum_backoff = "300s"
          }]
        }
      }
    }
    xsoar_indicator_score_updates = {
      name = "xsoar-indicator-score-updates-${var.lcaas}"
      labels = {
        name = "xsoar-indicator-score-updates"
        app  = "xsoar"
      }
      sub = {
        xsoar_indicator_score_updates_sub = {
          name = "xsoar-indicator-score-updates-sub-${var.lcaas}"
          labels = {
            name = "xsoar-indicator-score-updates-sub"
            app  = "xsoar"
          }
          ack_deadline_seconds = 300
          retry_policy = [{
            maximum_backoff = "300s"
          }]
        }
      }
    }
    xsoar_indicator_timeline_comments = {
      name = "xsoar-indicator-timeline-comments-${var.lcaas}"
      labels = {
        name = "xsoar-indicator-timeline-comments"
        app  = "xsoar"
      }
      sub = {
        xsoar_indicator_timeline_comments_sub = {
          name = "xsoar-indicator-timeline-comments-sub-${var.lcaas}"
          labels = {
            name = "xsoar-indicator-timeline-comments-sub"
            app  = "xsoar"
          }
          ack_deadline_seconds = 300
          retry_policy = [{
            maximum_backoff = "300s"
          }]
        }
      }
    }
    xsoar_relationships = {
      name = "xsoar-relationships-${var.lcaas}"
      labels = {
        name = "xsoar-relationships"
        app  = "xsoar"
      }
      sub = {
        xsoar_relationships_sub = {
          name = "xsoar-relationships-sub-${var.lcaas}"
          labels = {
            name = "xsoar-relationships-sub"
            app  = "xsoar"
          }
          ack_deadline_seconds = 300
          retry_policy = [{
            maximum_backoff = "300s"
          }]
        }
      }
    }
    xsoar_tags_field_values = {
      name = "xsoar-tags-field-values-${var.lcaas}"
      labels = {
        name = "xsoar-tags-field-values"
        app  = "xsoar"
      }
      sub = {
        xsoar_tags_field_values_sub = {
          name = "xsoar-tags-field-values-sub-${var.lcaas}"
          labels = {
            name = "xsoar-tags-field-values-sub"
            app  = "xsoar"
          }
          ack_deadline_seconds = 300
          retry_policy = [{
            maximum_backoff = "300s"
          }]
        }
      }
    }
    vulnerability_and_compliance_scans = {
      name = "vulnerability-and-compliance-scans-${var.lcaas}"
      labels = {
        name = "vulnerability-and-compliance-scans"
        app  = "xdr"
      }
      sub = {
        vulnerability_and_compliance_scans_sub = {
          name = "vulnerability-and-compliance-scans"
          labels = {
            name = "vulnerability-and-compliance-scans"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          iam = {
            vulnerability_and_compliance_scans_pubsub_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:prisma-console@${var.project_id}.iam.gserviceaccount.com"
            }
          }
        }
        vulnerability_and_compliance_scans_sub_debug = {
          name = "vulnerability-and-compliance-scans-debug"
          labels = {
            name = "vulnerability-and-compliance-scans-debug"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          iam = {
            vulnerability_and_compliance_scans_debug_pubsub_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:prisma-console@${var.project_id}.iam.gserviceaccount.com"
            }
          }
        }
      }
    }
    cwp_k8s_data = {
      name = "cwp-k8s-data-${var.lcaas}"
      labels = {
        name = "cwp-k8s-data"
        app  = "xdr"
      }
      sub = {
        cwp_k8s_data_inventory_sub = {
          name = "cwp-k8s-data-inventory-sub-${var.lcaas}"
          labels = {
            name = "cwp-k8s-data-inventory-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    cwp_k8s_connector_logs = {
      enabled = local.enable_cortex_platform
      name    = "cwp-k8s-connector-logs-${var.lcaas}"
      labels = {
        name = "cwp-k8s-connector-logs"
        app  = "xdr"
      }
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      schema_settings = {
        cwp_k8s_log_schema = {
          enabled     = true
          name        = "cwp-k8s-connector-logs-schema"
          type        = "AVRO"
          schema_path = "${path.module}/pubsub_topic_schema/cwp-k8s-observability-logs-schema.json"
          encoding    = "JSON"
        }
      }
      sub = {
        bq_sub = {
          enabled                 = local.enable_cortex_platform
          name                    = "cwp-k8s-connector-logs-bq-sub-${var.lcaas}"
          topic                   = "cwp-k8s-connector-logs-${var.lcaas}"
          ack_deadline_seconds    = 60
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "30s"
              minimum_backoff = "5s"
            }
          ]
          labels = {
            name = "cwp-k8s-connector-logs"
            app  = "xdr"
          }
          bigquery_config = [{
            use_topic_schema = true
            write_metadata   = true
            table            = "${var.project_id}.cwp_k8s_connector_observability_${var.lcaas}.connector_logs"
          }]
        }
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    cwp_sp_lifecycle_events = {
      name                       = "cwp-sp-lifecycle-events-${var.lcaas}"
      message_retention_duration = "${local.durations.two_days_in_seconds}s" # TLDO-2669
      labels = {
        name = "cwp-sp-lifecycle-events"
        app  = "xdr"
      }
      sub = {
        cwp_sp_lifecycle_events_ads_result_sync_sub = {
          name = "cwp-sp-lifecycle-events-ads-result-sync-sub-${var.lcaas}"
          labels = {
            name = "cwp-sp-lifecycle-events-ads-result-sync"
            app  = "xdr"
          }
          filter               = "attributes.module = \"ads\""
          ack_deadline_seconds = 600
        }
        cwp_registry_scan_orchestrator_sp_lifecycle_events_sub = {
          enabled = local.enable_cortex_platform
          name = "cwp-registry-scan-orchestrator-sp-lifecycle-events-sub-${var.lcaas}"
          labels = {
            name = "cwp-registry-scan-orchestrator-sp-lifecycle-events-sub"
            app  = "xdr"
          }
          filter               = "attributes.module = \"cwp\""
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_registry_discovery_orchestrator_sp_lifecycle_events_sub = {
          name = "cwp-registry-disc-orchestrator-sp-lifecycle-events-sub-${var.lcaas}"
          labels = {
            name = "cwp-registry-disc-orchestrator-sp-lifecycle-events-sub"
            app  = "xdr"
          }
          filter               = "attributes.module = \"cwp\""
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_serverless_scan_orchestrator_sp_lifecycle_events_sub = {
          name = "cwp-serverless-scan-orchestrator-sp-lifecycle-events-sub-${var.lcaas}"
          labels = {
            name = "cwp-serverless-scan-orchestrator-sp-lifecycle-events-sub"
            app  = "xdr"
          }
          filter               = "attributes.module = \"cwp\""
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        dspm_datasource_sp_lifecycle_events_sub = {
          name = "dspm-datasource-sp-lifecycle-events-sub-${var.lcaas}"
          labels = {
            name = "dspm-datasource-sp-lifecycle-events-sub"
            app  = "dspm"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    cwp_policy_rules_notification = {
      name = "cwp-policy-rules-notification-${var.lcaas}"
      labels = {
        name = "cwp-policy-rules-notification"
        app  = "xdr"
      }
      sub = {
        cwp_policy_rules_notification_sub = {
          name = "cwp-policy-rules-notification-sub-${var.lcaas}"
          labels = {
            name = "cwp-policy-rules-notification-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        cwp_policy_rules_notification_compliance_publisher = {
          name = "cwp-policy-rules-notification-compliance-publisher-${var.lcaas}"
          labels = {
            name = "cwp-policy-rules-notification-compliance-publisher"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    app-hub-observability = {
      name = "app-hub-observability-${var.lcaas}"
      labels = {
        name    = "app-hub-observability-${var.lcaas}"
        app     = "xsiam"
        product = "application-hub"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        app-hub-observability = {
          name                         = "app-hub-observability-${var.lcaas}"
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", false)
          ack_deadline_seconds         = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            }
          ]
          labels = {
            name = "app-hub-observability-${var.lcaas}"
            app  = "xsiam"
          }
        }
      }
    }
    ap-communication = {
      name                       = "ap-communication-${var.lcaas}"
      message_retention_duration = "604800s"
      labels = {
        name = "ap-communication-${var.lcaas}"
        app  = "xdr"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:app-proxy@xdr-app-proxy-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {
        ap-communication-sub = {
          name                 = "ap-communication"
          ack_deadline_seconds = 300
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            }
          ]
          labels = {
            name = "ap-communication"
            app  = "xdr"
          }
        }
      }
    }
    ipl_asset_changes_topic = {
      name = "ipl-asset-changes-${var.lcaas}"
      labels = {
        name = "ipl_asset_changes"
        app  = "xsiam"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        ipl_asset_changes_sub = {
          name                 = "ipl-asset-changes-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "ipl_asset_changes_sub"
            app  = "xsiam"
          }
        }
      }
    }
    cwp_scan_result_uploads_notifications = {
      enabled = local.enable_cortex_platform
      name    = "cwp-scan-result-uploads-notifications-${var.lcaas}"
      labels = {
        name = "cwp_scan_result_uploads_notifications_topic"
        app  = "prisma"
      }
      sub = {
        cwp_scan_result_uploads_notifications_enricher = {
          enabled = local.enable_cortex_platform
          name    = "cwp-scan-result-uploads-notifications-enricher-sub-${var.lcaas}"
          filter  = "attributes.eventType = \"OBJECT_FINALIZE\""
          labels = {
            name = "cwp_scan_result_uploads_subscription"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
      iam = {
        gcs_pub = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
        service_pub = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    cwp_scan_results = {
      enabled = local.enable_cortex_platform
      name    = "cwp-scan-results-${var.lcaas}"
      labels = {
        name = "cwp_scan_results_topic"
        app  = "prisma"
      }
      sub = {
        cwp_scan_results_enricher = {
          enabled = local.enable_cortex_platform
          name    = "cwp-scan-results-enricher-${var.lcaas}"
          filter  = "attributes.module != \"dspm\""
          labels = {
            name = "cwp_scan_results_subscription"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        dspm_crespo_midfielder_scan_results_sub = {
          name                 = "dspm-crespo-midfielder-scan-results-${var.lcaas}-sub"
          enabled              = var.enable_cloud_posture
          ack_deadline_seconds = 600
          labels = {
            name = "crespo-midfielder-scan-results-sub"
            app  = "dspm"
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-crespo-midfielder-scan-results-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
        dspm_datasource_scan_results_sub = {
          name                 = "dspm-datasource-scan-results-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "datasource-scan-results-sub"
            app  = "dspm"
          }
        }
      }
    }
    dspm_datasource_account_topic = {
      enabled = local.enable_cortex_platform
      name    = "dspm-datasource-account-${var.lcaas}"
      labels = {
        name = "dspm_datasource_account_topic"
        app  = "dspm"
      }
      sub = {
        dspm_datasource_dashboard_account_sub = {
          name                 = "dspm-datasource-dashboard-account-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-datasource-dashboard-account-sub"
            app  = "dspm"
          }
        }
      }
    }
    dspm_datasource_scan_now_topic = {
      enabled = local.enable_cortex_platform
      name    = "dspm-datasource-scan-now-${var.lcaas}"
      labels = {
        name = "dspm_datasource_scan_now_topic"
        app  = "dspm"
      }
      sub = {
        dspm_datasource_worker_scan_now_sub = {
          name                 = "dspm-datasource-worker-scan-now-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "dspm_datasource_worker_scan_now_sub"
            app  = "dspm"
          }
        }
      }
    }
    dspm_datasource_onboarding_topic = {
      enabled = local.enable_cortex_platform
      name    = "dspm-datasource-onboarding-${var.lcaas}"
      labels = {
        name = "dspm_datasource_onboarding_topic"
        app  = "dspm"
      }
      sub = {
        dspm_datasource_worker_onboarding_sub = {
          name                 = "dspm-datasource-worker-onboarding-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-datasource-worker-onboarding-sub"
            app  = "dspm"
          }
        },
        dspm_voyager_onboarding_sub = {
          name                 = "dspm-voyager-onboarding-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-voyager-onboarding-sub"
            app  = "dspm"
          }
        },
        classification_mgmt_datasource_onboarding_sub = {
          name                 = "classification-mgmt-datasource-onboarding-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels = {
            name = "classification-mgmt-datasource-onboarding-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
        }
      }
    }
    dspm_voyager_listing_topic = {
      enabled = local.enable_cortex_platform
      name    = "dspm-voyager-listing-${var.lcaas}"
      labels  = {
        name = "dspm-voyager-listing-topic"
        app  = "dspm"
      }
      sub = {
        dspm_voyager_listing_sub = {
          name                 = "dspm-voyager-listing-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels               = {
            name = "dspm-voyager-listing-sub"
            app  = "dspm"
          }
        },
        dspm_voyager_shared_links_sub = {
          name                 = "dspm-voyager-shared-links-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels               = {
            name = "dspm-voyager-shared-links-sub"
            app  = "dspm"
          }
        }
      }
    }
    dspm_voyager_listing_result_topic = {
      enabled = local.enable_cortex_platform
      name    = "dspm-voyager-listing-result-${var.lcaas}"
      labels  = {
        name = "dspm-voyager-listing-result-topic"
        app  = "dspm"
      }
      sub = {
        dspm_voyager_listing_result_sub = {
          name                 = "dspm-voyager-listing-result-sub-${var.lcaas}"
          enabled              = local.enable_cortex_platform
          ack_deadline_seconds = 600
          labels               = {
            name = "dspm-voyager-listing-result-sub"
            app  = "dspm"
          }
        }
      }
    }
    cwp_registry_scan_orchestrator_requests = {
      enabled = local.enable_cortex_platform
      name    = "cwp-registry-scan-orchestrator-requests-${var.lcaas}"
      labels = {
        name = "cwp_registry_scan_orchestrator_requests_topic"
        app  = "prisma"
      }
      sub = {
        cwp_registry_scan_requests_orchestrator_sub = {
          enabled = local.enable_cortex_platform
          name    = "cwp-registry-scan-orchestrator-requests-sub-${var.lcaas}"
          labels = {
            name = "cwp_registry_scan_requests_orc_sub"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    cwp_registry_discovery_requests = {
      enabled = local.enable_cortex_platform
      name    = "cwp-registry-disc-requests-${var.lcaas}"
      labels = {
        name = "cwp_registry_disc_requests_topic"
        app  = "prisma"
      }
      sub = {
        cwp_registry_disc_requests_orchestrator_dlq = {
          enabled = local.enable_cortex_platform
          name    = "cwp-registry-scan-requests-orchestrator-dlq-sub-${var.lcaas}"
          labels = {
            name = "cwp_registry_disc_requests_orc_sub"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    cwp_serverless_scan_requests = {
      enabled = local.enable_cortex_platform
      name    = "cwp-serverless-scan-requests-${var.lcaas}"
      labels = {
        name = "cwp_serverless_scan_requests_topic"
        app  = "prisma"
      }
      sub = {
        cwp_serverless_scan_requests_orchestrator = {
          enabled = local.enable_cortex_platform
          name    = "cwp-serverless-scan-requests-orchestrator-${var.lcaas}"
          labels = {
            name = "cwp_serverless_scan_requests_orc_sub"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    cwp_scanner_version_change_feed = {
      enabled = local.enable_cortex_platform
      name    = "cwp-scanner-version-change-feed-${var.lcaas}"
      labels = {
        name = "cwp_scanner_version_cf_topic"
        app  = "prisma"
      }
      sub = {}
    }
    cwp_enriched_scan_results = {
      enabled = local.enable_cortex_platform
      name    = "cwp-enriched-scan-results-${var.lcaas}"
      labels = {
        name = "cwp_enriched_scan_results_topic"
        app  = "prisma"
      }
      sub = {
        dspm_crespo_midfielder_enriched_scan_results_subs = {
          enabled = var.enable_cloud_posture
          name = "dspm-crespo-midfielder-enriched-scan-results-${var.lcaas}-subs"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-crespo-midfielder-enriched-scan-results-subs"
            app  = "dspm"
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-crespo-midfielder-enriched-scan-results-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
        dspm_mac_enriched_scan_results_sub = {
          enabled = local.enable_cortex_platform
          name = "dspm-mac-enriched-scan-results-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-mac-enriched-scan-results-sub"
            app  = "dspm-dt"
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-mac-enriched-scan-results-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
        cwp_enriched_scan_results_core_asset_analyzer = {
          enabled = local.enable_cortex_platform
          name    = "cwp-enriched-scan-results-core-asset-analyzer-${var.lcaas}"
          labels = {
            name = "cwp_enriched_scan_results_core_asset_analyzer_subscription"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_enriched_scan_results_sbom_analyzer = {
          enabled = local.enable_cortex_platform
          name    = "cwp-enriched-scan-results-sbom-analyzer-${var.lcaas}"
          labels = {
            name = "cwp_enriched_scan_results_sbom_analyzer_subscription"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_enriched_scan_results_vulnerability_analyzer = {
          enabled = local.enable_cortex_platform
          name    = "cwp-enriched-scan-results-vulnerability-analyzer-${var.lcaas}"
          labels = {
            name = "cwp_enriched_scan_results_vulnerability_analyzer_subscription"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "300s"
            }
          ]
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_enriched_scan_results_secret_analyzer = {
          enabled = local.enable_cortex_platform
          name    = "cwp-enriched-scan-results-secret-analyzer-${var.lcaas}"
          labels = {
            name = "cwp_enriched_scan_results_secret_analyzer"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "300s"
            }
          ]
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_enriched_scan_results_malware_analyzer = {
          enabled = local.enable_cortex_platform
          name    = "cwp-enriched-scan-results-malware-analyzer-${var.lcaas}"
          labels = {
            name = "cwp_enriched_scan_results_malware_analyzer"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "300s"
            }
          ]
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
        cwp_enriched_scan_results_compliance_publisher = {
          enabled = local.enable_cortex_platform
          name    = "cwp-enriched-scan-results-compliance-publisher-${var.lcaas}"
          labels = {
            name = "cwp-enriched-scan-results-compliance-publisher"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }

    cwp_re_evaluate_jobs = {
      enabled = local.enable_cortex_platform
      name    = "cwp-re-evaluate-jobs-${var.lcaas}"
      labels = {
        name = "cwp_re_evaluate_jobs_topic"
        app  = "prisma"
      }
      sub = {
        cwp_re_evaluate_jobs_vulnerability_analyzer = {
          enabled = local.enable_cortex_platform
          name    = "cwp-re-evaluate-jobs-vulnerability-analyzer-${var.lcaas}"
          labels = {
            name = "cwp_re_evaluate_jobs_vulnerability_analyzer_subscription"
            app  = "prisma"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }

    cwp_sp_bc_results_topic = {
      name = "cwp-sp-bc-results-${var.lcaas}"
      labels = {
        name = "cwp-sp-bc-results"
        app  = "xdr"
      }
      sub = {}
    }
    cwp_sp_bc_objects_topic = {
      name = "cwp-sp-bc-objects-${var.lcaas}"
      labels = {
        name = "cwp-sp-bc-objects"
        app  = "xdr"
      }
      sub = {
        cwp_sp_bc_objects_sub = {
          name = "cwp-sp-bc-objects-sub-${var.lcaas}"
          labels = {
            name = "cwp-sp-bc-objects-sub"
            app  = "xdr"
          }
        }
      }
    }
    cwp_sp_bc_control_topic = {
      name = "cwp-sp-bc-control-${var.lcaas}"
      labels = {
        name = "cwp-sp-bc-control"
        app  = "xdr"
      }
      sub = {
        cwp_sp_bc_control_sub = {
          name   = "cwp-sp-bc-control-sub-${var.lcaas}"
          filter = "attributes.type = \"log\""
          labels = {
            name = "cwp-sp-bc-control-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        cwp_sp_bc_control_workload_orchestration = {
          name   = "cwp-sp-bc-control-workload-orchestration-${var.lcaas}"
          filter = "attributes.type = \"status\""
          labels = {
            name = "cwp-sp-bc-control-workload-orchestration-sub"
            app  = "xdr"
          }
        }
        cwp_sp_bc_control_metrics_sub = {
          name   = "cwp-sp-bc-control-metrics-${var.lcaas}-sub"
          filter = "attributes.type = \"metrics\""
          labels = {
            name = "cwp-sp-bc-control-metrics-sub"
            app  = "xdr"
          }
        }
      }
    }
    cwp_sp_bc_metrics_topic = {
      name = "cwp-sp-bc-metrics-${var.lcaas}"
      labels = {
        name = "cwp-sp-bc-metrics"
        app  = "xdr"
      }
      sub = {
        cwp_sp_bc_metrics_sub = {
          name = "cwp-sp-bc-metrics-${var.lcaas}-sub"
          labels = {
            name = "cwp-sp-bc-metrics-sub"
            app  = "xdr"
          }
        }
      }
    }
    ap_issue_ingest_feedback = {
      name = "ap-issue-ingest-feedback-${var.lcaas}"
      labels = {
        name = "ap_issue_ingest_feedback"
        app  = "xdr-shared"
      }
      sub = {
        ap_issue_ingest_feedback_sub = {
          name                       = "ap-issue-ingest-feedback-sub-${var.lcaas}"
          message_retention_duration = "86400s"
          labels = {
            name = "ap_issue_ingest_feedback_sub"
            app  = "xdr"
          }
        }
        ap_issue_ingest_feedback_sub_cas = {
          name                       = "ap-issue-ingest-feedback-cas-${var.lcaas}-sub"
          message_retention_duration = "86400s"
          filter                     = "attributes.product = \"CAS\""
          ack_deadline_seconds       = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap_issue_ingest_feedback_sub_cas"
            app  = "cas"
          }
        }
        ap_issue_ingest_feedback_sub_new_persistence_cas = {
          name                       = "ap-issue-ingest-feedback-new-persistence-cas-${var.lcaas}-sub"
          message_retention_duration = "86400s"
          filter                     = "attributes.product = \"CAS\""
          ack_deadline_seconds       = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap_issue_ingest_feedback_sub_new_persistence_cas"
            app  = "cas"
          }
        }
        ap_issue_ingest_feedback_sub_vxp = {
          name                       = "ap-issue-ingest-feedback-vxp-${var.lcaas}-sub"
          message_retention_duration = "86400s"
          filter                     = "attributes.product = \"VULNERABILITY_MANAGEMENT\" AND attributes.feedback_type = \"QUOTA_EXCEEDED\""
          labels = {
            name = "ap_issue_ingest_feedback_sub_vxp"
            app  = "vxp"
          }
        }
      }
    }
    ap_issue_upsert = {
      name = "ap-issue-upsert-${var.lcaas}"
      labels = {
        name = "ap-issue-upsert"
        app  = "xdr"
      }
      sub = {
        ap_issue_upsert_sub = {
          name                 = "ap-issue-upsert-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-upsert-sub"
            app  = "xdr"
          }
        }
      }
    }
    apisec_inspection_alerts = {
      name = "apisec-inspection-alerts-${var.lcaas}"
      labels = {
        name = "apisec-inspection-alerts"
        app  = "xdr"
      }
      sub = {
        apisec_inspection_alerts_apisec_issuer_sub = {
          name                 = "apisec-inspection-alerts-apisec-issuer-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "apisec-inspection-alerts-apisec-issuer-sub"
            app  = "xdr"
          }
        }
      }
    }
    ap_issue_ingestion_errors = {
      name = "ap-issue-ingestion-errors-${var.lcaas}"
      labels = {
        name = "ap-issue-ingestion-errors"
        app  = "xdr"
      }
      sub = {
        ap_issue_ingestion_errors_ciem_sub = {
          name                       = "ap-issue-ingestion-errors-ciem-${var.lcaas}-sub"
          message_retention_duration = "86400s"

          filter               = "attributes.product = \"CIEM\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-ciem-sub"
            app  = "ciem"
          }
        },
        ap_issue_ingestion_errors_dspm_sub = {
          name                       = "ap-issue-ingestion-errors-dspm-${var.lcaas}-sub"
          message_retention_duration = "86400s"

          filter               = "attributes.product = \"DSPM\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-dspm-sub"
            app  = "ciem"
          }
        },
        ap_issue_ingestion_errors_aispm_sub = {
          name                       = "ap-issue-ingestion-errors-aispm-${var.lcaas}-sub"
          message_retention_duration = "86400s"

          filter               = "attributes.product = \"AISPM\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-aispm-sub"
            app  = "ciem"
          }
        },
        ap_issue_ingestion_errors_cas_sub = {
          name                       = "ap-issue-ingestion-errors-cas-${var.lcaas}-sub"
          message_retention_duration = "86400s"

          filter               = "attributes.product = \"CAS\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-cas-sub"
            app  = "cas"
          }
        },
        ap_issue_ingestion_errors_new_persistence_cas_sub = {
          name                       = "ap-issue-ingestion-errors-new-persistence-cas-${var.lcaas}-sub"
          message_retention_duration = "86400s"

          filter               = "attributes.product = \"CAS\""
          ack_deadline_seconds = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap_issue_ingestion_errors_new_persistence_cas_sub"
            app  = "cas"
          }
        },
        ap_issue_ingestion_errors_sub = {
          name                       = "ap-issue-ingestion-errors-${var.lcaas}"
          message_retention_duration = "86400s"
          ack_deadline_seconds       = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-sub"
            app  = "xdr"
          }
        },
        ap_issue_ingestion_errors_risk_sub = {
          name                       = "ap-issue-ingestion-errors-risk-sub-${var.lcaas}"
          message_retention_duration = "86400s"
          filter                     = "attributes.product = \"API_SECURITY\""
          ack_deadline_seconds       = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-risk-sub"
            app  = "xdr"
          }
        },
        ap_issue_ingestion_errors_asm_sub = {
          name                       = "ap-issue-ingestion-errors-asm-${var.lcaas}-sub"
          message_retention_duration = "86400s"
          filter                     = "attributes.product = \"ASM\""
          enabled                    = local.enable_cortex_platform
          ack_deadline_seconds       = 180
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "120s"
            }
          ]
          labels = {
            name = "ap-issue-ingestion-errors-asm-subscriber"
            app  = "asm"
          }
        }
      }
    }
    ap_issue_create = {
      name = "ap-issue-create-${var.lcaas}"
      labels = {
        name = "ap-issue-create"
        app  = "xdr"
      }
      sub = {
        ap_issue_create_sub = {
          name                 = "ap-issue-create-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "ap-issue-create-sub"
            app  = "xdr"
          }
        }
      }
    }
    ap_issue_update = {
      name = "ap-issue-update-${var.lcaas}"
      labels = {
        name = "ap-issue-update"
        app  = "xdr"
      }
      sub = {
        ap_issue_update_sub = {
          name                 = "ap-issue-update-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "ap-issue-update-sub"
            app  = "xdr"
          }
        }
      }
    }
    apisec_asset_updates_topic = {
      name = "apisec-asset-updates-${var.lcaas}"
      labels = {
        name = "apisec-asset-updates"
        app  = "xdr"
      }
      sub = {
        apisec_asset_updates_sub = {
          name = "apisec-asset-updates-asset-manager-sub-${var.lcaas}"
          labels = {
            name = "apisec-asset-updates-asset-manager-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    apisec_transactions_topic = {
      name = "apisec-transactions-${var.lcaas}"
      labels = {
        name = "apisec-transactions"
        app  = "xdr"
      }
      sub = {
        apisec_transactions_grouping_sub = {
          name = "apisec-transactions-grouping-sub-${var.lcaas}"
          labels = {
            name = "apisec-transactions-grouping-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    apisec_grouped_transactions_topic = {
      name = "apisec-grouped-transactions-${var.lcaas}"
      labels = {
        name = "apisec-grouped-transactions"
        app  = "xdr"
      }
      sub = {
        apisec_grouped_asset_manager_sub = {
          name = "apisec-grouped-asset-manager-sub-${var.lcaas}"
          labels = {
            name = "apisec-grouped-asset-manager-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        apisec_grouped_inspection_sub = {
          name = "apisec-grouped-inspection-sub-${var.lcaas}"
          filter  = "attributes.vendor != \"panw\" AND attributes.product != \"agent\""
          labels = {
            name = "apisec-grouped-inspection-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        apisec_grouped_risk_sub = {
          name = "apisec-grouped-risk-sub-${var.lcaas}"
          labels = {
            name = "apisec-grouped-risk-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/apisec-grouped-risk-sub-dlq-${var.lcaas}"
            max_delivery_attempts = 10
          }]
        }
      }
    }
    apisec-grouped-risk-sub-dlq = {
      name = "apisec-grouped-risk-sub-dlq-${var.lcaas}"
      labels = {
        name = "apisec-grouped-risk-sub-dlq"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        },
      }
      sub = {}
    }
    apisec_spec_topic = {
      name = "apisec-spec-${var.lcaas}"
      labels = {
        name = "apisec-spec"
        app  = "xdr"
      }
      sub = {
        apisec_spec_asset_manager_sub = {
          name = "apisec-spec-asset-manager-sub-${var.lcaas}"
          labels = {
            name = "apisec-spec-asset-manager-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        apisec_spec_risk_engine_sub = {
          name = "apisec-spec-risk-engine-sub-${var.lcaas}"
          filter = "attributes.operation = \"create\" OR attributes.operation = \"update\" OR attributes.operation = \"delete\""
          labels = {
            name = "apisec-spec-risk-engine-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    dp_bus_topic = {
      name = "dp-bus-${var.lcaas}"
      labels = {
        name = "dp-bus"
        app  = "xdr"
      }
      sub = {
        apisec_dp_bus_enricher = {
          name   = "apisec-dp-bus-enricher-sub-${var.lcaas}"
          filter = "attributes.apisec = \"true\""
          labels = {
            name = "apisec-dp-bus-enricher-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        itdr_data_pipeline_dp_bus_sub = {
          name   = "itdr-data-pipeline-dp-bus-sub-${var.lcaas}"
          filter = "attributes.vendor = \"panw_ad_hygiene\""
          labels = {
            name = "itdr-data-pipeline-dp-bus-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    dspm_asset_analysis_topic = {
      name    = "dspm-asset-analysis-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-asset-analysis-${var.lcaas}"
        app  = "dspm"
      }
      sub = {
        dspm_fda_asset_analysis_sub = {
          name    = "dspm-fda-asset-analysis-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-fda-asset-analysis-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-fda-asset-analysis-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_file_analysis_topic = {
      name    = "dspm-file-analysis-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-file-analysis-${var.lcaas}"
        app  = "dspm"
      }
      sub = {
        dspm_fda_file_analysis_sub = {
          name    = "dspm-fda-file-analysis-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-fda-file-analysis-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-fda-file-analysis-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_asset_post_processing_request = {
      name    = "dspm-asset-post-processing-request-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-asset-post-processing-request-${var.lcaas}"
        app  = "dspm"
      }
      sub = {
        dspm_fda_asset_post_processing_request_sub = {
          name    = "dspm-fda-asset-post-processing-request-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-fda-asset-post-processing-request-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-fda-asset-post-processing-request-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_listing_data = {
      name    = "dspm-listing-data-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-listing-data-${var.lcaas}"
        app  = "dspm"
      }
      sub = {
        dspm_cb_listing_data_sub = {
          name    = "dspm-cb-listing-data-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-cb-listing-data-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-cb-listing-data-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_column_listing_data_topic = {
      name    = "dspm-column-listing-data-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-column-listing-data-${var.lcaas}"
        app  = "dspm"
        team = "dspm-dt"
      }
      sub = {
        dspm_cb_column_listing_data_sub = {
          name    = "dspm-cb-column-listing-data-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-cb-column-listing-data-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-cb-column-listing-data-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_asset_discovery_topic = {
      name    = "dspm-asset-discovery-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-asset-discovery-${var.lcaas}"
        app  = "dspm"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gs-project-accounts.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_mac_asset_discovery_sub = {
          name    = "dspm_mac_asset_discovery-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          filter = "attributes.eventType = \"OBJECT_FINALIZE\""
          labels = {
            name = "dspm_mac_asset_discovery-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm_mac_asset_discovery_dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_discovery_data_topic = {
      name    = "dspm-discovery-data-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-discovery-data-${var.lcaas}"
        app  = "dspm"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gs-project-accounts.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_discovery_data_sub = {
          name    = "dspm_discovery_data-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_discovery_data-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    dspm_outpost_orchestrator_joeker_topic = {
      name    = "dspm_outpost_orchestrator_joeker-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_outpost_orchestrator_joeker-${var.lcaas}"
        app  = "dspm"
      }

      sub = {
        joeker-pubsub = {
          name = "joeker-sub-${var.lcaas}"
          labels = {
            name = "joeker_sub"
            app  = "dspm"
          }
          ack_deadline_seconds = 600
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-outpost-orchestrator-joeker-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
      }
    }
    dspm_column_analysis_topic = {
      name    = "dspm-column-analysis-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm-column-analysis-${var.lcaas}"
        app  = "dspm"
        team = "dspm-dt"
      }
      sub = {
        dspm_fda_column_analysis_sub = {
          name    = "dspm-fda-column-analysis-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-fda-column-analysis-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-fda-column-analysis-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
        }
      }
    }
    ciem_epc_task_topic = {
      name    = "ciem-epc-task-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "ciem-epc-task"
        app  = "ciem-epc"
      }
      sub = {
        ciem_epc_task_sub = {
          name    = "ciem-epc-task-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "ciem-epc-task-sub"
            app  = "ciem-epc"
          }
          ack_deadline_seconds = 600
        }
      }
    }
    cloudsec_batch_verdicts = {
      name    = "cloudsec-batch-verdicts-notification-${var.lcaas}"
      enabled = true
      labels = {
        name = "cloudsec_batch_verdicts"
        app  = "cloudsec"
      }
      sub = {
        cloudsec_batch_verdicts = {
          name    = "cloudsec-batch-verdicts-notification-${var.lcaas}-sub"
          enabled = true
          labels = {
            name = "cloudsec_batch_verdicts"
            app  = "cloudsec"
          }
          enable_exactly_once_delivery = lookup(var.overrides, "enable_exactly_once_delivery", true)
          ack_deadline_seconds = 300
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/cloudsec-batch-verdicts-notification-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
      }
      iam = {
        gcs_pub = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
        service_pub = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    cloudsec_batch_verdicts_dlq = {
      name    = "cloudsec-batch-verdicts-notification-dlq-${var.lcaas}"
      enabled = true
      labels = {
        name = "cloudsec_batch_verdicts_dlq"
        app  = "cloudsec"
      }
      sub = {
        cloudsec_batch_verdicts_dlq = {
          name    = "cloudsec-batch-verdicts-notification-dlq-${var.lcaas}-sub"
          enabled = true
          labels = {
            name = "cloudsec_batch_verdicts_dlq"
            app  = "cloudsec"
          }
          ack_deadline_seconds = 300
        }
      }
      project_iam = {
        service_sub = {
          role   = "roles/pubsub.subscriber"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      iam = {
        service_pub = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    verdict_manager_topic = {
      name    = "verdict-manager-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cloudsec-verdict-manager"
        app  = "cloudsec"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        bq_sub = {
          enabled                 = local.enable_cortex_platform
          name                    = "verdict-manager-bq-sub-${var.lcaas}"
          topic                   = "verdict-manager-${var.lcaas}"
          ack_deadline_seconds    = 60
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "0s"
              minimum_backoff = "0s"
            }
          ]
          labels = {
            name = "cloudsec-verdict-manager-sub-to-bq-table"
            app  = "cloudsec"
          }
          bigquery_config = [{
            use_table_schema    = true
            write_metadata      = true
            table               = "${var.project_id}.cloudsec_${var.lcaas}.cloudsec_verdicts"
            drop_unknown_fields = true
          }]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/cloudsec-verdict-manager-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }

    verdict_manager_dlq = {
      name    = "cloudsec-verdict-manager-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "verdict-manager-dlq"
        app  = "cloudsec"
      }
      message_retention_duration = "432000s"
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        },
      }
      sub = {
        verdict-manager-dlq-sub = {
          enabled = local.enable_cortex_platform
          name    = "verdict-manager-dlq-${var.lcaas}"
          labels = {
            name = "verdict-manager-dlq"
            app  = "cloudsec"
          }
          ack_deadline_seconds = 10
        }
      }
    }

    cloudsec_xspm_verdicts_notification = {
      name = "cloudsec-xspm-verdicts-notification-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cloudsec-xspm-verdicts-notification"
        app = "cloudsec"
      }
      iam = {
        gcs_pub = {
          role = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
        service_pub = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cloudsec_xspm_verdicts_notification_sub = {
          name = "cloudsec-xspm-verdicts-notification-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          filter = "attributes.objectId != \"\" AND attributes.bucketId != \"\""
          labels = {
            name = "cloudsec-xspm-verdicts-notification-sub"
            app = "cloudsec"
          }
          dead_letter_policy = [{
            dead_letter_topic = "projects/${var.project_id}/topics/cloudsec-xspm-verdicts-notification-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
        }
      }
    }

    cloudsec_xspm_verdicts_notification_dlq = {
      name = "cloudsec-xspm-verdicts-notification-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cloudsec-xspm-verdicts-notification-dlq"
        app = "cloudsec"
      }
      message_retention_duration = "432000s"
      iam = {
        pubsub_member_publisher = {
          role = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {}
    }

    cloudsec-xspm-scanner-rules = {
      name    = "cloudsec-xspm-scanner-rules-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cloudsec-xspm-scanner-rules"
        app  = "cloudsec"
      }
      message_retention_duration = "432000s"
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        },
      }
      sub = {
        cloudsec-xspm-scanner-rules-sub = {
          enabled = local.enable_cortex_platform
          name    = "cloudsec-xspm-scanner-rules-${var.lcaas}-sub"
          labels = {
            name = "cloudsec-xspm-scanner-rules"
            app  = "cloudsec"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "10s"
              minimum_backoff = "10s"
            }
          ]
        }
      }
    }

    apisec_grouped_transactions_analytics_bq_topic = {
      name    = "apisec-grouped-transactions-analytics-bq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "apisec-grouped-transactions-analytics-bq"
        app  = "xdr"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        apisec_grouped_transactions_analytics_bq_sub = {
          name = "apisec-grouped-transactions-analytics-bq-sub-${var.lcaas}"
          labels = {
            name = "apisec-grouped-transactions-analytics-bq-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
        }
        bq_sub = {
          enabled                 = true
          name                    = "apisec-grouped-transactions-bq-sub-${var.lcaas}"
          topic                   = "apisec-grouped-transactions-analytics-bq-${var.lcaas}"
          ack_deadline_seconds    = 60
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "30s"
              minimum_backoff = "5s"
            }
          ]
          labels = {
            name = "apisec-grouped-transactions-analytics-bq-table"
            app  = "xdr"
          }
          bigquery_config = [{
            use_topic_schema    = false
            write_metadata      = true
            table               = "${var.project_id}.apisec_analytics_${var.lcaas}.apisec_grouped_transactions"
            use_table_schema    = true
            drop_unknown_fields = true
          }]
        }
      }
    }



    dspm_playmaker_resource_updates_topic = {
      enabled = local.enable_cortex_platform
      name    = "playmaker_resource_updates-${var.lcaas}"
      labels = {
        name = "playmaker_resource_updates"
        app  = "dspm"
      }
      sub = {
      }
    }

    dspm_outpost_orchestrator_job_registerer = {
      enabled = local.enable_cortex_platform
      name    = "outpost_orchestrator_job_registerer-${var.lcaas}"
      labels = {
        name = "outpost_orchestrator_job_registerer"
        app  = "dspm"
      }

      sub = {
      }
    }
    dspm_outpost_orchestrator_scan_job_scheduler_prepare_job_s3 = {
      enabled = local.enable_cortex_platform
      name    = "outpost_orchestrator_scan_job_scheduler-${var.lcaas}"
      labels = {
        name = "outpost_orchestrator_scan_job_scheduler"
        app  = "dspm"
      }
      sub = {
      }
    }
    cas_trigger_argo_wf = {
      name = "cas-trigger-argo-wf-${var.lcaas}"
      labels = {
        name = "cas-trigger-argo-wf"
        app  = "cas"
      }
      sub = {
        cas_trigger_argo_wf_sarif_sub = {
          name                 = "cas-trigger-argo-wf-sarif-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          filter = "hasPrefix(attributes.objectId, \"preSignedUrlUploads/sarif\")"
          labels = {
            name = "cas-trigger-argo-wf-sarif-sub-"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    cas_jenkins_trigger_argo_wf = {
      name = "cas-jenkins-trigger-argo-wf-${var.lcaas}"
      labels = {
        name = "cas-jenkins-trigger-argo-wf"
        app  = "cas"
      }
      sub = {
        cas_jenkins_state_trigger_argo_wf_plugin_sub = {
          name                 = "cas-jenkins-state-trigger-argo-wf-plugin-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          filter = "hasPrefix(attributes.objectId, \"preSignedUrlUploads/plugin/jenkins/state\")"
          labels = {
            name = "cas-jenkins-state-trigger-argo-wf-plugin-sub-"
            app  = "cas"
          }
        }
        cas_jenkins_logs_trigger_argo_wf_plugin_sub = {
          name                 = "cas-jenkins-logs-trigger-argo-wf-plugin-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          filter = "hasPrefix(attributes.objectId, \"preSignedUrlUploads/plugin/jenkins/logs\")"
          labels = {
            name = "cas-jenkins-logs-trigger-argo-wf-plugin-sub-"
            app  = "cas"
          }
        }
        cas_jenkins_pipelines_trigger_argo_wf_plugin_sub = {
          name                 = "cas-jenkins-pipelines-trigger-argo-wf-plugin-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          filter = "hasPrefix(attributes.objectId, \"preSignedUrlUploads/plugin/jenkins/pipelines\")"
          labels = {
            name = "cas-jenkins-pipelines-trigger-argo-wf-plugin-sub-"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    cas_webhooks = {
      name = "cas-webhooks-${var.lcaas}"
      labels = {
        name = "cas-webhooks"
        app  = "cas"
      }
      sub = {
        cas_webhooks_sub = {
          name                 = "cas-webhooks-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-webhooks-sub-"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_webhook_requests_ingestion = {
      enabled = local.enable_cortex_platform
      name = "cas-webhook-requests-ingestion-${var.lcaas}"
      labels = {
        name = "cas-webhook-requests-ingestion"
        app  = "cas"
      }
      sub = {
        cas_webhook_requests_ingestion_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-webhook-requests-ingestion-sub-${var.lcaas}"
          filter = "attributes.status = \"SUCCESS\""
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-webhook-requests-ingestion-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-webhook-requests-ingestion-sub"
            app  = "cas"
          }
        },
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_webhook_requests_ingestion_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-webhook-requests-ingestion-dlq-${var.lcaas}"
      labels = {
        name = "cas_webhook_requests_ingestion_dlq"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_webhook_requests_ingestion_dlq_SUB = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-webhook-requests-ingestion-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_webhook_requests_ingestion_dlq_sub"
            app  = "cas"
            team = "cas-flow"
          }
        }
      }
    }

    cas_pre_enrichment_scanner_reports_pr = {
      enabled = local.enable_cortex_platform
      name = "cas-pre-enrichment-scanner-reports-pr-${var.lcaas}"
      labels = {
        name = "cas-pre-enrichment-scanner-reports-pr"
        app  = "cas"
      }
      sub = {
        cas_pre_enrichment_scanner_reports_pr_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-pr-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-pre-enrichment-scanner-reports-pr-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-pre-enrichment-scanner-reports-pr-sub"
            app  = "cas"
          }
        },
        cas_pre_enrichment_scanner_reports_pr_errors_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-pr-errors-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-pre-enrichment-scanner-reports-pr-errors-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_pre_enrichment_scanner_reports_cli = {
      enabled = local.enable_cortex_platform
      name = "cas-pre-enrichment-scanner-reports-cli-${var.lcaas}"
      labels = {
        name = "cas-pre-enrichment-scanner-reports-cli"
        app  = "cas"
      }
      sub = {
        cas_pre_enrichment_scanner_reports_cli_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-cli-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-pre-enrichment-scanner-reports-cli-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-pre-enrichment-scanner-reports-cli-sub"
            app  = "cas"
          }
        },
        cas_pre_enrichment_scanner_reports_cli_errors_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-cli-errors-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-pre-enrichment-scanner-reports-cli-errors-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_pre_enrichment_scanner_reports_pr_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-pre-enrichment-scanner-reports-pr-dlq-${var.lcaas}"
      labels = {
        name = "cas_pre_enrichment_scanner_reports_pr_dlq"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_pre_enrichment_scanner_reports_pr_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-pr-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_pre_enrichment_scanner_reports_pr_dlq_sub"
            app  = "cas"
            team = "cas-stitch"
          }
        }
      }
    }


  cas_pre_enrichment_scanner_reports_periodic_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-pre-enrichment-scanner-reports-periodic-dlq-${var.lcaas}"
      labels = {
        name = "cas_pre_enrichment_scanner_reports_periodic_dlq"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_pre_enrichment_scanner_reports_periodic_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-periodic-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_pre_enrichment_scanner_reports_periodic_dlq_sub"
            app  = "cas"
            team = "cas-stitch"
          }
        }
      }
    }

    cas_pre_enrichment_scanner_reports_cli_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-pre-enrichment-scanner-reports-cli-dlq-${var.lcaas}"
      labels = {
        name = "cli"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_pre_enrichment_scanner_reports_cli_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-cli-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_pre_enrichment_scanner_reports_cli_dlq_sub"
            app  = "cas"
            team = "cas-stitch"
          }
        }
      }
    }

    cas_post_enrichment_scanner_reports_pr_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-post-enrichment-scanner-reports-pr-dlq-${var.lcaas}"
      labels = {
        name = "cas_post_enrichment_scanner_reports_pr_dlq"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_post_enrichment_scanner_reports_pr_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-post-enrichment-scanner-reports-pr-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_post_enrichment_scanner_reports_pr_dlq_sub"
            app  = "cas"
            team = "cas-stitch"
          }
        }
      }
    }

    cas_post_enrichment_scanner_reports_periodic_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-post-enrichment-scanner-reports-periodic-dlq-${var.lcaas}"
      labels = {
        name = "cas_post_enrichment_scanner_reports_periodic_dlq"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_post_enrichment_scanner_reports_periodic_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-post-enrichment-scanner-reports-periodic-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_post_enrichment_scanner_reports_periodic_dlq_sub"
            app  = "cas"
            team = "cas-stitch"
          }
        }
      }
    }

    cas_post_enrichment_scanner_reports_cli_dlq = {
      enabled = local.enable_cortex_platform
      name    = "cas-post-enrichment-scanner-reports-cli-dlq-${var.lcaas}"
      labels = {
        name = "cas_post_enrichment_scanner_reports_cli_dlq"
        app  = "cas"
        team = "cas-stitch"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_post_enrichment_scanner_reports_cli_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "cas-post-enrichment-scanner-reports-cli-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "cas_post_enrichment_scanner_reports_cli_dlq_sub"
            app  = "cas"
            team = "cas-stitch"
          }
        }
      }
    }

    cas_pre_enrichment_scanner_reports_periodic = {
      enabled = local.enable_cortex_platform
      name = "cas-pre-enrichment-scanner-reports-periodic-${var.lcaas}"
      labels = {
        name = "cas-pre-enrichment-scanner-reports-periodic"
        app  = "cas"
      }
      sub = {
        cas_pre_enrichment_scanner_reports_periodic_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-periodic-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-pre-enrichment-scanner-reports-periodic-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-pre-enrichment-scanner-reports-periodic-sub"
            app  = "cas"
          }
        },
        cas_pre_enrichment_scanner_reports_periodic_errors_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-pre-enrichment-scanner-reports-periodic-errors-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-pre-enrichment-scanner-reports-periodic-errors-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_post_enrichment_scanner_reports_pr = {
      enabled = local.enable_cortex_platform
      name = "cas-post-enrichment-scanner-reports-pr-${var.lcaas}"
      labels = {
        name = "cas-post-enrichment-scanner-reports-pr"
        app  = "cas"
      }
      sub = {
        cas_post_enrichment_actions_pr_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-post-enrichment-actions-pr-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/cas-post-enrichment-scanner-reports-pr-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
          labels = {
            name = "cas-post-enrichment-scanner-reports-pr-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_post_enrichment_scanner_reports_periodic = {
      enabled = local.enable_cortex_platform
      name = "cas-post-enrichment-scanner-reports-periodic-${var.lcaas}"
      labels = {
        name = "cas-post-enrichment-scanner-reports-periodic"
        app  = "cas"
      }
      sub = {
        cas_post_enrichment_actions_periodic_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-post-enrichment-actions-periodic-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/cas-post-enrichment-scanner-reports-periodic-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
          labels = {
            name = "cas-post-enrichment-actions-periodic-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_post_enrichment_scanner_reports_cli = {
      enabled = local.enable_cortex_platform
      name = "cas-post-enrichment-scanner-reports-cli-${var.lcaas}"
      labels = {
        name = "cas-post-enrichment-scanner-reports-cli"
        app  = "cas"
      }
      sub = {
        cas_post_enrichment_actions_cli_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-post-enrichment-actions-cli-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/cas-post-enrichment-scanner-reports-cli-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
          labels = {
            name = "cas-post-enrichment-actions-cli-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_post_persistence_pr = {
      enabled = local.enable_cortex_platform
      name = "cas-post-persistence-pr-${var.lcaas}"
      labels = {
        name = "cas-post-persistence-pr"
        app  = "cas"
      }
      sub = {
        cas_post_persistence_pr_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-post-persistence-pr-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-post-persistence-pr-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_post_persistence_cli = {
      enabled = local.enable_cortex_platform
      name = "cas-post-persistence-cli-${var.lcaas}"
      labels = {
        name = "cas-post-persistence-cli"
        app  = "cas"
      }
      sub = {
        cas_post_persistence_cli_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-post-persistence-cli-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-post-persistence-cli-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_post_persistence_periodic = {
      enabled = local.enable_cortex_platform
      name = "cas-post-persistence-periodic-${var.lcaas}"
      labels = {
        name = "cas-post-persistence-periodic"
        app  = "cas"
      }
      sub = {
        cas_post_persistence_periodic_sub = {
          enabled = local.enable_cortex_platform
          name                 = "cas-post-persistence-periodic-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-post-persistence-periodic-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_vcs_enrichment = {
      name = "cas-vcs-enrichment-${var.lcaas}"
      labels = {
        name = "cas-vcs-enrichment"
        app  = "cas"
      }
      sub = {
        cas_vcs_enrichment_cicd_sub = {
          name                 = "cas-vcs-enrichment-cicd-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas-vcs-enrichment-cicd-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    cas_periodic_scan = {
      name = "cas-periodic-scan-${var.lcaas}"
      labels = {
        name = "cas-periodic-scan"
        app  = "cas"
      }
      sub = {
        cas_periodic_scan_secrets_s_sub = {
          name                 = "cas-periodic-scan-secrets-s-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-scanner-tasks-dl-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-periodic-scan-secrets-s-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    cas_pr_scan = {
      name = "cas-pr-scan-${var.lcaas}"
      labels = {
        name = "cas-pr-scan"
        app  = "cas"
      }
      sub = {
        cas_pr_scan_secrets_s_sub = {
          name                 = "cas-pr-scan-secrets-s-sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-scanner-tasks-dl-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-pr-scan-secrets-s-sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }

    cas_scanner_tasks_deadletter = {
      name = "cas-scanner-tasks-dl-${var.lcaas}"
      labels = {
        name = "cas_scanner_tasks_deadletter"
        app  = "cas"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        cas_scanner_tasks_deadletter_sub = {
          name                 = "cas-scanner-tasks-dl-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "600s"
          }]
          labels = {
            name = "cas-scanner-tasks-deadletter-sub"
            app  = "cas"
          }
        }
      }
    }

    cas_cli_git_users_persistence = {
      name = "cas_cli_git_users_persistence-${var.lcaas}"
      labels = {
        name = "cas_cli_git_users_persistence"
        app  = "cas"
      }
      sub = {
        cas_cli_git_users_persistence_sub = {
          name                 = "cas_cli_git_users_persistence_sub-${var.lcaas}"
          ack_deadline_seconds = 180
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "cas_cli_git_users_persistence_sub"
            app  = "cas"
          }
        }
      }
      iam = {
        gcs_notifications_pubsub_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    deletion_api = {
      name = "deletion-api-${var.lcaas}"
      labels = {
        name = "deletion-api"
        app  = "cas"
      }
      sub = {
        deletion_api_sub = {
          name                    = "deletion-api-sub-${var.lcaas}"
          ack_deadline_seconds    = 180
          enable_message_ordering = true
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "120s"
          }]
          labels = {
            name = "deletion-api-sub"
            app  = "cas"
          }
        }
      }
    }
    dp_finding_revisions = {
      enabled = local.enable_cortex_platform
      name    = "dp-finding-revisions-${var.lcaas}"
      labels = {
        name = "dp_finding_revisions"
        app  = "platform"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
      sub = {
        dp_finding_revisions_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "dp-finding-revisions-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "10s"
          }]
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dlq-dp-finding-revisions-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
          labels = {
            name = "dp_finding_revisions_sub"
            app  = "platform"
          }
        }
      }
    }
    dlq_dp_finding_revisions = {
      enabled = local.enable_cortex_platform
      name    = "dlq-dp-finding-revisions-${var.lcaas}"
      labels = {
        name = "dlq_dp_finding_revisions"
        app  = "platform"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dlq-dp-finding-revisions-sub = {
          enabled              = local.enable_cortex_platform
          name                 = "dlq-dp-finding-revisions-sub-${var.lcaas}"
          ack_deadline_seconds = 120
          labels = {
            name = "dlq_dp_finding_revisions_sub"
            app  = "platform"
          }
        }
      }
    }
    dspm_fda_asset_analysis_dlq = {
      name    = "dspm-fda-asset-analysis-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_fda_asset_analysis_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_fda_asset_analysis_dlq_sub = {
          name    = "dspm-fda-asset-analysis-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_fda_asset_analysis_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_fda_file_analysis_dlq = {
      name    = "dspm-fda-file-analysis-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_fda_file_analysis_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_fda_file_analysis_dlq_sub = {
          name    = "dspm-fda-file-analysis-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_fda_file_analysis_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_fda_asset_post_processing_request_dlq = {
      name    = "dspm-fda-asset-post-processing-request-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_fda_asset_post_processing_request_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_fda_asset_post_processing_request_dlq_sub = {
          name    = "dspm-fda-asset-post-processing-request-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_fda_asset_post_processing_request_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_fda_asset_change_feed_dlq = {
      name    = "dspm-fda-asset-change-feed-dlq-${var.lcaas}"
      enabled = var.enable_cloud_posture
      labels = {
        name = "dspm_fda_asset_change_feed_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_fda_asset_change_feed_dlq_sub = {
          name    = "dspm-fda-asset-change-feed-dlq-sub-${var.lcaas}"
          enabled = var.enable_cloud_posture
          labels = {
            name = "dspm_fda_asset_change_feed_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_mac_aispm_asset_change_feed_dlq = {
      name    = "dspm-mac-aispm-asset-change-feed-dlq-${var.lcaas}"
      enabled = var.enable_cloud_posture
      labels = {
        name = "dspm_mac_aispm_asset_change_feed_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_mac_aispm_asset_change_feed_dlq_sub = {
          name    = "dspm-mac-aispm-asset-change-feed-dlq-sub-${var.lcaas}"
          enabled = var.enable_cloud_posture
          labels = {
            name = "dspm_mac_aispm_asset_change_feed_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_cb_listing_data_dlq = {
      name    = "dspm-cb-listing-data-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_cb_listing_data_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_cb_listing_data_dlq_sub = {
          name    = "dspm-cb-listing-data-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_cb_listing_data_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_ace_asset_change_feed_dlq = {
      name    = "dspm-ace-asset-change-feed-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_ace_asset_change_feed_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_ace_asset_change_feed_dlq_sub = {
          name    = "dspm-ace-asset-change-feed-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_ace_asset_change_feed_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_crespo_resource_listener_asset_change_feed_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-crespo-resource-listener-asset-change-feed-dlq-${var.lcaas}"
      labels = {
        name = "dspm-crespo-resource-listener-asset-change-feed-dlq"
        app  = "dspm"
      }
      message_retention_duration = "2592000s"

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      sub = {
        dspm_crespo_resource_listener_asset_change_feed_dlq_sub = {
          enabled              = var.enable_cloud_posture
          name                 = "dspm-crespo-resource-listener-asset-change-feed-dlq-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-crespo-resource-listener-asset-change-feed-dlq-sub"
            app  = "dspm"
          }
        }
      }
    }
    dspm_crespo_midfielder_scan_results_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-crespo-midfielder-scan-results-dlq-${var.lcaas}"
      labels = {
        name = "dspm-crespo-midfielder-scan-results-dlq"
        app  = "dspm"
      }
      message_retention_duration = "2592000s"

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      sub = {
        dspm_crespo_midfielder_scan_results_dlq_sub = {
          enabled              = var.enable_cloud_posture
          name                 = "dspm-crespo-midfielder-scan-results-dlq-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-crespo-midfielder-scan-results-dlq-sub"
            app  = "dspm"
          }
        }
      }
    }

    dspm_mac_enriched_scan_results_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-mac-enriched-scan-results-dlq-${var.lcaas}"
      labels = {
        name = "dspm-mac-enriched-scan-results-dlq"
        app  = "dspm-dt"
      }
      message_retention_duration = "2592000s"

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      sub = {
        dspm_mac_enriched_scan_results_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "dspm-mac-enriched-scan-results-dlq-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-crespo-midfielder-scan-results-dlq-sub"
            app  = "dspm-dt"
          }
        }
      }
    }

    dspm_crespo_midfielder_enriched_scan_results_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-crespo-midfielder-enriched-scan-results-dlq-${var.lcaas}"
      labels = {
        name = "dspm-crespo-midfielder-enriched-scan-results-dlq"
        app  = "dspm"
      }
      message_retention_duration = "2592000s"

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      sub = {
        dspm_crespo_midfielder_enriched_scan_results_dlq_sub = {
          enabled              = var.enable_cloud_posture
          name                 = "dspm-crespo-midfielder-enriched-scan-results-dlq-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-crespo-midfielder-enriched-scan-results-dlq-sub"
            app  = "dspm"
          }
        }
      }
    }
    dspm_oo_boarder_cloud_accounts_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-oo-boarder-cloud-accounts-dlq-${var.lcaas}"
      labels = {
        name = "dspm-oo-boarder-cloud-accounts-dlq"
        app  = "dspm"
      }
      message_retention_duration = "2592000s"

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      sub = {
        dspm_oo_boarder_cloud_accounts_dlq_sub = {
          enabled              = var.enable_cloud_posture
          name                 = "dspm-oo-boarder-cloud-accounts-dlq-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-oo-boarder-cloud-accounts-dlq-sub"
            app  = "dspm"
          }
        }
      }
    }

    dspm_outpost_orchestrator_joeker_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-outpost-orchestrator-joeker-dlq-${var.lcaas}"
      labels = {
        name = "dspm-outpost-orchestrator-joeker-dlq"
        app  = "dspm"
      }
      message_retention_duration = "2592000s"

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      sub = {
        dspm_outpost_orchestrator_joeker_dlq_sub = {
          enabled              = local.enable_cortex_platform
          name                 = "dspm-outpost-orchestrator-joeker-dlq-${var.lcaas}-sub"
          ack_deadline_seconds = 600
          labels = {
            name = "dspm-outpost-orchestrator-joeker-dlq-sub"
            app  = "dspm"
          }
        }
      }
    }

    dspm_ack_files_access_checker_dlq = {
      name    = "dspm-ack-files-access-checker-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_ack_files_access_checker_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_ack_files_access_checker_dlq_sub = {
          name    = "dspm-ack-files-access-checker-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_ack_files_access_checker_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_cb_column_listing_data_dlq = {
      name    = "dspm-cb-column-listing-data-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_cb_column_listing_data_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_cb_column_listing_data_dlq_sub = {
          name    = "dspm-cb-column-listing-data-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_cb_column_listing_data_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_fda_column_analysis_dlq = {
      name    = "dspm-fda-column-analysis-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_fda_column_analysis_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_fda_column_analysis_dlq_sub = {
          name    = "dspm-fda-column-analysis-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_fda_column_analysis_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_dpc_profile_update_dlq = {
      name    = "dspm-dpc-profile-update-dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_dpc_profile_update_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_dpc_profile_update_dlq_sub = {
          name    = "dspm-dpc-profile-update-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_dpc_profile_update_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    dspm_mac_asset_discovery_dlq = {
      name    = "dspm_mac_asset_discovery_dlq-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "dspm_mac_asset_discovery_dlq"
        app  = "dspm"
        team = "dspm-dt"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_mac_asset_discovery_dlq_sub = {
          name    = "dspm_mac_asset_discovery_dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm_mac_asset_discovery_dlq_sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          ack_deadline_seconds = 300
        }
      }
    }
    cwp_issue_closing_topic = {
      enabled = local.enable_cortex_platform
      name    = "cwp-issue-closing-${var.lcaas}"
      labels = {
        name = "cwp-issue-closing-topic"
        app  = "cortex-platform"
      }
      sub = {
        cwp_issue_closing_sub = {
          name = "cwp-issue-closing-${var.lcaas}-sub"
          labels = {
            name = "cwp-issue-closing-sub"
            app  = "cortex-platform"
          }
        }
      }
    }

    msft_lifecycle_notification_topic = {
      name = "msft-lifecycle-notification-${var.lcaas}"
      labels = {
        name = "msft-lifecycle-notification"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {}
    }
    msft_task_notification_topic = {
      name = "msft-task-notification-${var.lcaas}"
      labels = {
        name = "msft-task-notification"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {}
    }
    google_task_notification_topic = {
      name = "task-notification-google-${var.lcaas}"
      labels = {
        name = "google-task-notification"
        app  = "xdr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
        }
      }
      sub = {}
    }
    itdr_update_topic = {
      name   = "itdr-update-${var.lcaas}"
      labels = {
        name = "itdr-update"
        app  = "xdr"
      }
      sub = {
        itdr_update_risk_processor_sub = {
          enabled = var.enable_itdr
          name   = "itdr-update-risk-processor-${var.lcaas}-sub"
          labels = {
            name = "itdr-update-risk-processor-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    itdr_case_changes_topic = {
      name   = "itdr-case-changes-${var.lcaas}"
      labels = {
        name = "itdr-case-changes"
        app  = "xdr"
      }
      sub = {
        itdr_data_pipeline_case_changes_sub = {
          enabled = var.enable_itdr
          name   = "itdr-data-pipeline-case-changes-${var.lcaas}-sub"
          labels = {
            name = "itdr-data-pipeline-case-changes-sub"
            app  = "cortex-platform"
          },
        }

      }
    }
    classification_mgmt_data_pattern_update_topic = {
      name    = "classification-mgmt-data-pattern-update-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "classification-mgmt-data-pattern-update-${var.lcaas}"
        app  = "dspm"
      }
      sub = {
        classification_mgmt_data_pattern_update_sub = {
          name    = "classification-mgmt-data-pattern-update-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels  = {
            name = "classification-mgmt-data-pattern-update-${var.lcaas}-sub"
            app  = "dspm"
          }
        }
      }
    }
    classification_mgmt_profile_update_topic = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-profile-update-${var.lcaas}"
      labels = {
        name = "classification-mgmt-profile-update-topic"
        app  = "dspm"
      }
      sub = {
        dspm_dpc_profile_update_sub = {
          name    = "dspm-dpc-profile-update-${var.lcaas}-sub"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-dpc-profile-update-${var.lcaas}-sub"
            app  = "dspm"
            team = "dspm-dt"
          }
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/dspm-dpc-profile-update-dlq-${var.lcaas}"
            max_delivery_attempts = 15
          }]
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        },
        classification_mgmt_profile_update_sub = {
          name    = "classification-mgmt-profile-update-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "classification-mgmt-profile-update-sub-${var.lcaas}"
            app  = "dspm"
            team = "dspm-dt"
          }
        }
      }
    }
    classification_mgmt_global_settings_update_topic = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-global-settings-update-${var.lcaas}"
      labels = {
        name = "classification-mgmt-global-settings-update-topic"
        app  = "dspm"
      }
      sub = {
        classification_mgmt_global_settings_update_sub = {
          name = "classification-mgmt-global-settings-update-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "classification-mgmt-global-settings-update-sub-${var.lcaas}"
            app  = "dspm"
          }
        }
      }
    }
    classification_mgmt_profile_modification_request_topic = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-profile-modification-request-topic-${var.lcaas}"
      labels = {
        name = "classification-mgmt-profile-modification-request-topic"
        app  = "dspm"
        team = "dig-ddr"
      }
      sub = {
        classification_mgmt_profile_modification_request_sub = {
          enabled = local.enable_cortex_platform
          name    = "classification-mgmt-profile-modification-request-sub-${var.lcaas}"
          labels = {
            name = "classification-mgmt-profile-modification-request-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            }
          ]
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/classification-mgmt-profile-modification-request-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }
    classification_mgmt_data_pattern_modification_request_topic = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-data-pattern-modification-request-topic-${var.lcaas}"
      labels  = {
        name  = "classification-mgmt-data-pattern-modification-request-topic"
        app   = "dspm"
        team = "dig-ddr"
      }
      sub = {
        classification_mgmt_data_pattern_modification_request_sub = {
        enabled = local.enable_cortex_platform
        name    = "classification-mgmt-data-pattern-modification-request-sub-${var.lcaas}"
        labels = {
          name = "classification-mgmt-data-pattern-modification-request-sub"
          app  = "dspm"
          team = "dig-ddr"
        }
        ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
        iam = {
          pubsub_member_subscriber = {
            role   = "roles/pubsub.subscriber"
            member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
          }
        }
        dead_letter_policy = [
          {
            dead_letter_topic     = "projects/${var.project_id}/topics/classification-mgmt-data-pattern-modification-request-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }
        ]
      }}
    }

    classification_mgmt_profile_modification_request_dlq = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-profile-modification-request-dlq-${var.lcaas}"
      labels = {
        name = "classification-mgmt-profile-modification-request-dlq"
        app  = "dspm"
        team = "dig-ddr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        classification_mgmt_profile_modification_request_dlq_sub = {
          name    = "classification-mgmt-profile-modification-request-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "classification-mgmt-profile-modification-request-dlq"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 300
        }
      }
    }

    classification_mgmt_data_pattern_modification_request_dlq = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-data-pattern-modification-request-dlq-${var.lcaas}"
      labels  = {
        name  = "classification-mgmt-data-pattern-modification-request-dlq"
        app   = "dspm"
        team = "dig-ddr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        classification_mgmt_profile_modification_request_dlq_sub = {
          name    = "classification-mgmt-data-pattern-modification-request-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "classification-mgmt-data-pattern-modification-request-dlq-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 300
        }
      }
    }

    dspm_information_protection_label_mip_update_topic = {
      enabled = local.enable_cortex_platform
      name    = "dspm-information-protection-label-mip-update-topic-${var.lcaas}"
      labels  = {
        name  = "dspm-information-protection-label-mip-update-topic"
        app   = "dspm"
        team = "dig-ddr"
      }
      sub = {
        dspm_information_protection_label_mip_update_sub = {
          enabled = local.enable_cortex_platform
          name    = "dspm-information-protection-label-mip-update-sub-${var.lcaas}"
          labels = {
            name = "dspm-information-protection-label-mip-update-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/dspm-information-protection-label-mip-update-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }
    dspm_information_protection_label_mip_update_dlq = {
      enabled = local.enable_cortex_platform
      name    = "dspm-information-protection-label-mip-update-dlq-${var.lcaas}"
      labels = {
        name = "dspm-information-protection-label-mip-update-dlq"
        app  = "dspm"
        team = "dig-ddr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        dspm_information_protection_label_mip_update_dlq_sub = {
          name    = "dspm-information-protection-label-mip-update-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name = "dspm-information-protection-label-mip-update-dlq-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 300
        }
      }
    }

    classification_mgmt_ipl_modification_request_topic = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-ipl-modification-request-topic-${var.lcaas}"
      labels  = {
        name  = "classification-mgmt-ipl-modification-request-topic"
        app   = "dspm"
        team = "dig-ddr"
      }
      sub = {
        classification_mgmt_ipl_modification_request_sub = {
          enabled = local.enable_cortex_platform
          name    = "classification-mgmt-ipl-modification-request-sub-${var.lcaas}"
          labels = {
            name = "classification-mgmt-ipl-modification-request-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 600
          retry_policy = [{
            maximum_backoff = "600s"
            minimum_backoff = "30s"
          }]
          iam = {
            pubsub_member_subscriber = {
              role   = "roles/pubsub.subscriber"
              member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
            }
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/classification-mgmt-ipl-modification-request-dlq-${var.lcaas}"
              max_delivery_attempts = 5
            }
          ]
        }
      }
    }
    classification_mgmt_ipl_modification_request_dlq = {
      enabled = local.enable_cortex_platform
      name    = "classification-mgmt-ipl-modification-request-dlq-${var.lcaas}"
      labels = {
        name =  "classification-mgmt-ipl-modification-request-dlq"
        app  = "dspm"
        team = "dig-ddr"
      }
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        classification_mgmt_ipl_modification_request_dlq_sub = {
          name    = "classification-mgmt-ipl-modification-request-dlq-sub-${var.lcaas}"
          enabled = local.enable_cortex_platform
          labels = {
            name =  "classification-mgmt-ipl-modification-request-dlq-sub"
            app  = "dspm"
            team = "dig-ddr"
          }
          ack_deadline_seconds = 300
        }
      }
    }

    notification_scan_results_topic = {
      enabled = local.enable_cortex_platform
      name    = "notification-scan-results-topic-${var.lcaas}"
      labels = {
        name = "notification_scan_results_topic"
        app  = "dspm"
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gs-project-accounts.iam.gserviceaccount.com"
        }
      }
      sub = {
        notification_scan_results_sub = {
          name                 = "notification-scan-results-sub-${var.lcaas}"
          enabled              = var.enable_cloud_posture
          ack_deadline_seconds = 600
          labels = {
            name = "notification-scan-results-sub"
            app  = "dspm"
          }
          dead_letter_policy = [
            {
              dead_letter_topic     = "projects/${var.project_id}/topics/notification-scan-results-dlq-${var.lcaas}"
              max_delivery_attempts = 15
            }
          ]
        }
      }
    }

    notification_scan_results_dlq = {
      enabled = local.enable_cortex_platform
      name    = "notification-scan-results-dlq-${var.lcaas}"
      labels = {
        name = "notification-scan-results-dlq"
        app  = "dspm"
        team = "dig-ddr"
      }
      message_retention_duration = "2592000s"
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
      sub = {
        notification_scan_results_dlq_sub = {
          enabled              = var.enable_cloud_posture
          name                 = "notification-scan-results-dlq-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "notification-scan-results-dlq-sub"
            app  = "dspm"
          }
        }
      }
    }



    dp_scan_logs_topic = {
      enabled = local.enable_cortex_platform
      name    = "dp-scan-logs-${var.lcaas}"
      labels = {
        name = "dp-scan-logs-topic"
        app  = "cortex-platform"
      }
      sub = {
        dp_scan_logs_sub = {
          name = "dp-scan-logs-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "dp-scan-logs-sub"
            app  = "platform"
          }
        }
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    dss_sync_notifier_topic = {
      enabled = local.enable_cortex_platform
      name    = "dss-sync-notifier-${var.lcaas}"
      labels = {
        name = "dss-sync-notifier-topic"
        app  = "xdr"
      }
      sub = {
        itdr-data_pipeline_dss_sync_sub = {
          enabled = var.enable_itdr
          name = "itdr-data-pipeline-dss-sync-${var.lcaas}-sub"
          ack_deadline_seconds = 60
          labels = {
            name = "itdr-data-pipeline-dss-sync-sub"
            app  = "xdr"
          }
        }
      }
    }

    dp_scan_logs_ingestion_errors_topic = {
      enabled = local.enable_cortex_platform
      name    = "dp-scan-logs-ingestion-errors-${var.lcaas}"
      labels = {
        name = "dp-scan-logs-ingestion-errors-topic"
        app  = "cortex-platform"
      }
      sub = {
        dp_scan_logs_ingestion_errors_sub = {
          name = "dp-scan-logs-ingestion-errors-sub-${var.lcaas}"
          labels = {
            name = "dp-scan-logs-ingestion-errors-sub"
            app  = "platform"
          }
        }
      }
      iam = {
        add_gcs_member = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:${local.gcs_account}"
        }
      }
    }
    agent_management_reported_issues = {
      enabled = local.enable_cortex_platform
      name = "agent-management-reported-issues-${var.lcaas}"
      labels = {
        name = "agent-management-reported-issues-topic"
        app  = "cortex-platform"
      }
      sub = {
        agent_management_reported_issues_sub = {
          name = "agent-management-reported-issues-sub-${var.lcaas}"
          labels = {
            name = "agent-management-reported-issues-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    agent_management_outgoing_external_integrations_topic = {
      enabled = local.enable_cortex_platform
      name = "agent-management-outgoing-external-integrations-${var.lcaas}"
      labels = {
        name = "agent-management-outgoing-external-integrations-topic"
        app  = "cortex-platform"
      }
      sub = {
        agent_mgmt_outgoing_ext_integrations_itdr_weak_passwords_sub = {
          enabled = var.enable_itdr
          name = "agent-mgmt-outgoing-ext-integrations-${var.lcaas}-itdr-weak-passwords-sub"
          filter = "attributes.event_type = \"weakPasswordsReport\""
          labels = {
            name = "agent-mgmt-outgoing-ext-integrations-itdr-weak-passwords-sub"
            app  = "cortex-platform"
          }
        },
        itdr_cap_reports_sub = {
          enabled = var.enable_itdr
          name = "itdr-cap-reports-${var.lcaas}-sub"
          filter = "attributes.reportType = \"cap\""
          labels = {
            name = "itdr-cap-reports-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        },
        agent_mgmt_ext_integrations_apisec_issuer_sub = {
          enabled = local.enable_cortex_platform
          name = "agent-mgmt-ext-integrations-apisec-issuer-sub-${var.lcaas}"
          filter = "attributes.event_type = \"apisec_violation_issue_detected\""
          labels = {
            name = "agent-mgmt-ext-integrations-apisec-issuer-sub-${var.lcaas}"
            app  = "cortex-platform"
          }
        },
        itdr_cap_responder_block_mfa_sub = {
          enabled = var.enable_itdr
          name = "itdr-cap-responder-block-mfa-${var.lcaas}-sub"
          filter = "attributes.reportType=\"cap\" AND attributes.subType=\"mfa\""
          labels = {
            name = "itdr-cap-responder-block-mfa-${var.lcaas}-sub"
            app  = "itdr"
          }
        },
        itdr_cap_responder_block_email_sub = {
          enabled = var.enable_itdr
          name = "itdr-cap-responder-block-email-${var.lcaas}-sub"
          filter = "attributes.reportType=\"cap\" AND (attributes.subType=\"block\" OR attributes.subType=\"mfa\")"
          labels = {
            name =  "itdr-cap-responder-block-email-${var.lcaas}-sub"
            app  = "itdr"
          }
        }
      }
    }
    cwp_enriched_container_scan_results_topic = {
      name = "cwp-enriched-container-scan-results-${var.lcaas}"
      labels = {
        name = "cwp-enriched-container-scan-results"
        app  = "xdr"
      }
      sub = {
        cwp_enriched_container_scan_results_containers_analyzer_sub = {
          name = "cwp-enriched-container-scan-results-containers-analyzer-${var.lcaas}-sub"
          labels = {
            name = "cwp-enriched-container-scan-results-containers-analyzer-sub"
            app  = "xdr"
          }
          ack_deadline_seconds = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "30s"
            },
          ]
        }
      }
    }
    # CRTX-172365: Findings Promotion Pipeline (FPP)
    cwp_fpp_asset_topic = {
      name = "cwp-fpp-asset-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cwp-fpp-asset"
        app  = "cwp"
      }
      sub = {
        cwp_fpp_asset_sub = {
          name = "cwp-fpp-asset-${var.lcaas}-sub"
          labels = {
            name = "cwp-fpp-asset-sub"
            app  = "cwp"
          }
        }
      }
    }
    cwp_fpp_policy_evaluation_topic = {
      name = "cwp-fpp-policy-evaluation-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cwp-fpp-policy-evaluation"
        app  = "cwp"
      }
      sub = {
        cwp_fpp_policy_evaluation_sub = {
          name = "cwp-fpp-policy-evaluation-${var.lcaas}-sub"
          labels = {
            name = "cwp-fpp-policy-evaluation-sub"
            app  = "cwp"
          }
        }
      }
    }

    cwp_trust_evaluation_asset_topic = {
      name = "cwp-trust-evaluation-asset-${var.lcaas}"
      enabled = local.enable_cortex_platform
      labels = {
        name = "cwp-trust-evaluation-asset"
        app  = "cwp"
      }
      sub = {
        cwp_trust_evaluation_asset_sub = {
          name = "cwp-trust-evaluation-asset-${var.lcaas}-sub"
          labels = {
            name = "cwp-trust-evaluation-asset"
            app  = "cwp"
          }
        }
      }
    }

  xsoar_playbook_tasks_bq_topic = {
      name    = "xsoar-playbook-tasks-bq"
      labels = {
        name = "xsoar-playbook-tasks-bq"
        app  = "xsoar"
      }
      sub = {}
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    xsoar_playbook_runs_bq_topic = {
      name    = "xsoar-playbook-runs-bq"
      labels = {
        name = "xsoar-playbook-runs-bq"
        app  = "xsoar"
      }
      sub = {}
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    xsoar_relationships_bq_topic = {
      name    = "xsoar-relationships-bq"
      labels = {
        name = "xsoar-relationships-bq"
        app  = "xsoar"
      }
      sub = {}
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    xsoar_execution_metrics_bq_topic = {
      name    = "xsoar-execution-metrics-bq"
      labels = {
        name = "xsoar-execution-metrics-bq"
        app  = "xsoar"
      }
      sub = {}
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    xsoar_indicators_bq_replica_topic = {
      name    = "xsoar-indicators-bq-replica"
      labels = {
        name = "xsoar-indicators-bq-replica"
        app  = "xsoar"
      }
      sub = {}
      project_iam = {
        add_bq_permis1 = {
          role   = "roles/bigquery.dataEditor"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
        add_bq_permis2 = {
          role   = "roles/bigquery.metadataViewer"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }

    xsoar_indicators_bq_replica_dlq_topic = {
      name    = "xsoar-indicators-bq-replica-dlq"
      labels = {
        name = "xsoar-indicators-bq-replica-dlq"
        app  = "xsoar"
      }
      sub = {}
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }

    xsoar_execution_metrics_bq_dlq_topic = {
      name    = "xsoar-execution-metrics-bq-dlq"
      labels = {
        name = "xsoar-execution-metrics-bq-dlq"
        app  = "xsoar"
      }
      sub = {}
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }

    xsoar_relationships_bq_dlq_topic = {
      name    = "xsoar-relationships-bq-dlq"
      labels = {
        name = "xsoar-relationships-bq-dlq"
        app  = "xsoar"
      }
      sub = {}
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }

    xsoar_playbook_runs_bq_dlq_topic = {
      name    = "xsoar-playbook-runs-bq-dlq"
      labels = {
        name = "xsoar-playbook-runs-bq-dlq"
        app  = "xsoar"
      }
      sub = {}
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }

    xsoar_playbook_tasks_bq_dlq_topic = {
      name    = "xsoar-playbook-tasks-bq-dlq"
      labels = {
        name = "xsoar-playbook-tasks-bq-dlq"
        app  = "xsoar"
      }
      sub = {}
      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }
    }
    ha_mirroring = {
      name = "ha-mirroring"
      labels = {
        name = "ha-mirroring"
        app  = "xsiam"
      }
      sub = {
        ha_mirroring_sub = {
          name = "ha-mirroring-sub"
          ack_deadline_seconds = 300
          retry_policy = [
            {
              maximum_backoff = "100s"
              minimum_backoff = "10s"
            }
          ]
          labels = {
            name = "ha-mirrorings-sub"
            app  = "xsiam"
          }
        }
      }
    }
    high_availability_tenants_sync = {
      enabled = lookup(var.overrides, "enable_primary_playbook_mirroring", false)
      name = "high-availability-tenants-sync"
      labels = {
        name = "high_availability_tenants_sync"
        app  = "xsiam"
      }
      sub = {
        high_availability_tenants_sync_sub = {
          name = "high-availability-tenants-sync-sub"
          labels = {
            name = "high_availability_tenants_sync-sub"
            app  = "xsiam"
          }
        }
      }
    }

    cas_persistence_periodic = {
      name    = "cas-persistence-periodic-${var.lcaas}"
      labels = {
        name = "cas_persistence_periodic_${var.lcaas}"
        app  = "cas"
      }
      message_retention_duration = "86400s"

      sub = {
        cas_persistence_periodic_metedata = {
          name                 = "cas-persistence-periodic-metedata-sub-${var.lcaas}"
          filter                     = "attributes.event_type = \"METADATA\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-periodic-metedata-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_periodic_large_reports = {
          name                 = "cas-persistence-periodic-large-reports-sub-${var.lcaas}"
          filter                     = "attributes.reportSizeClass = \"LARGE\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-periodic-large-reports-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_periodic_small_reports = {
          name                 = "cas-persistence-periodic-small-reports-sub-${var.lcaas}"
          filter                     = "attributes.reportSizeClass = \"SMALL\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-periodic-small-reports-sub-${var.lcaas}"
            app  = "cas"
          }
        }

      }
    }

    cas_persistence_pr = {
      name    = "cas-persistence-pr-${var.lcaas}"
      labels = {
        name = "cas_persistence_pr_${var.lcaas}"
        app  = "cas"
      }
      message_retention_duration = "86400s"
      sub = {
        cas_persistence_pr_metadata = {
          name                 = "cas-persistence-pr-metadata-sub-${var.lcaas}"
          filter                     = "attributes.event_type = \"METADATA\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-pr-metadata-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_pr_large_reports = {
          name                 = "cas-persistence-pr-large-reports-sub-${var.lcaas}"
          filter                     = "attributes.reportSizeClass = \"LARGE\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-pr-large-reports-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_pr_small_reports = {
          name                 = "cas-persistence-pr-small-reports-sub-${var.lcaas}"
          filter                     = "attributes.reportSizeClass = \"SMALL\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-pr-small-reports-sub-${var.lcaas}"
            app  = "cas"
          }
        }
      }
    }

    cas_persistence_cli = {
      name    = "cas-persistence-cli-${var.lcaas}"
      labels = {
        name = "cas_persistence_cli_${var.lcaas}"
        app  = "cas"
      }
      message_retention_duration = "86400s"

      sub = {
        cas_persistence_cli_metadata = {
          name                 = "cas-persistence-cli-metadata-sub-${var.lcaas}"
          filter                     = "attributes.event_type = \"METADATA\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-cli-metadata-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_cli_large_reports = {
          name                 = "cas-persistence-cli-large-reports-sub-${var.lcaas}"
          filter                     = "attributes.reportSizeClass = \"LARGE\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-cli-large-reports-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_cli_small_reports = {
          name                 = "cas-persistence-cli-small-reports-sub-${var.lcaas}"
          filter                     = "attributes.reportSizeClass = \"SMALL\""
          ack_deadline_seconds       = 30
          enable_message_ordering = true
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "60s"
            }
          ]
          dead_letter_policy = [{
            dead_letter_topic     = "projects/${var.project_id}/topics/cas-persistence-dlq-${var.lcaas}"
            max_delivery_attempts = 5
          }]
          labels = {
            name = "cas-persistence-cli-small-reports-sub-${var.lcaas}"
            app  = "cas"
          }
        }
      }
    }

    cas_persistence_dlq = {
      name    = "cas-persistence-dlq-${var.lcaas}"
      labels = {
        name = "cas_persistence_dlq_${var.lcaas}"
        app  = "cas"
      }

      iam = {
        pubsub_member_publisher = {
          role   = "roles/pubsub.publisher"
          member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        }
      }

      message_retention_duration = "604800s"

      sub = {
        cas_persistence_metadata_dlq = {
          name                 = "cas-persistence-metadata-dlq-sub-${var.lcaas}"
          filter                     = "attributes.event_type = \"METADATA\""

          labels = {
            name = "cas-persistence-metadata-dlq-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_periodic_reports_dlq = {
          name                 = "cas-persistence-periodic-reports-dlq-sub-${var.lcaas}"
          filter               = "attributes.scan_type = \"PERIODIC\" AND NOT attributes.event_type = \"METADATA\""

          labels = {
            name = "cas-persistence-periodic-reports-dlq-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_pr_reports_dlq = {
          name                 = "cas-persistence-pr-reports-dlq-sub-${var.lcaas}"
          filter                     = "attributes.scan_type = \"PR\" AND NOT attributes.event_type = \"METADATA\""

          labels = {
            name = "cas-persistence-pr-reports-dlq-sub-${var.lcaas}"
            app  = "cas"
          }
        },
        cas_persistence_cli_reports_dlq = {
          name                 = "cas-persistence-cli-reports-dlq-sub-${var.lcaas}"
          filter               = "attributes.scan_type = \"CLI\" AND NOT attributes.event_type = \"METADATA\""

          labels = {
            name = "cas-persistence-cli-reports-dlq-sub-${var.lcaas}"
            app  = "cas"
          }
        }
      }
    }

    agent_management_incoming_external_integrations_topic = {
      name = "agent-management-incoming-external-integrations-${var.lcaas}"
      labels = {
        name = "agent-management-incoming-external-integrations"
        app  = "cortex-platform"
      }
      sub = {
        agent_management_incoming_integrations_module_tag_changes_sub = {
          name = "agent-management-incoming-integrations-${var.lcaas}-module-tag-changes"
          filter = "attributes.eventType = \"MODULE_TAG_CHANGE\""
          labels = {
            name = "agent-management-incoming-integrations-module-tag-changes"
            app  = "cortex-platform"
          }
          ack_deadline_seconds         = 600
          retry_policy = [
            {
              maximum_backoff = "600s"
              minimum_backoff = "5s"
            }
          ]
        }
      }
    }
    ciem_issue_evidence_recalc_jobs_ciem = {
      name = "ciem-issue-evidence-recalc-jobs-ciem-${var.lcaas}"
      labels = {
        name = "ciem-issue-evidence-recalc-jobs-ciem"
        app  = "cortex-platform"
      }
      sub = {
        ciem_issue_evidence_recalc_jobs_ciem_sub = {
          name                 = "ciem-issue-evidence-recalc-jobs-ciem-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "ciem-issue-evidence-recalc-jobs-ciem-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ciem_issue_evidence_recalc_jobs_ciem_result = {
      name = "ciem-issue-evidence-recalc-jobs-ciem-result-${var.lcaas}"
      labels = {
        name = "ciem-issue-evidence-recalc-jobs-ciem-result"
        app  = "cortex-platform"
      }
      sub = {
        ciem_issue_evidence_recalc_jobs_ciem_result_sub = {
          name                 = "ciem-issue-evidence-recalc-jobs-ciem-result-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "ciem-issue-evidence-recalc-jobs-ciem-result-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    ciem_issue_evidence_recalc_jobs_other = {
      name = "ciem-issue-evidence-recalc-jobs-other-${var.lcaas}"
      labels = {
        name = "ciem-issue-evidence-recalc-jobs-other"
        app  = "cortex-platform"
      }
      sub = {
        ciem_issue_evidence_recalc_jobs_other_sub = {
          name                 = "ciem-issue-evidence-recalc-jobs-other-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "ciem-issue-evidence-recalc-jobs-other-sub"
            app  = "cortex-platform"
          }
        }
      }
    }

    ciem_issue_evidence_recalc_jobs_other_result = {
      name = "ciem-issue-evidence-recalc-jobs-other-result-${var.lcaas}"
      labels = {
        name = "ciem-issue-evidence-recalc-jobs-other-result"
        app  = "cortex-platform"
      }
      sub = {
        ciem_issue_evidence_recalc_jobs_other_result_sub = {
          name                 = "ciem-issue-evidence-recalc-jobs-other-result-sub-${var.lcaas}"
          ack_deadline_seconds = 600
          labels = {
            name = "ciem-issue-evidence-recalc-jobs-other-result-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    email_security_alerts_topic = {
      enabled = local.enable_email_sku
      name    = "email-security-alerts-${var.lcaas}"
      labels  = {
        name  = "email-security-alerts-${var.lcaas}"
        app   = "cortex-platform"
      }
      sub = {
        email_security_alerts_sub = {
          name = "email-security-alerts-${var.lcaas}-sub"
          labels = {
            name = "email-security-alerts-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    email_security_policy_actions_topic = {
      enabled = local.enable_email_sku
      name    = "email-security-policy-actions-${var.lcaas}"
      labels  = {
        name  = "email-security-policy-actions-${var.lcaas}"
        app   = "cortex-platform"
      }
      sub = {
        email_security_policy_actions_sub = {
          name = "email-security-policy-actions-${var.lcaas}-sub"
          labels = {
            name = "email-security-policy-actions-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    email_security_actions_commands_topic = {
      enabled = local.enable_email_sku
      name    = "email-security-actions-commands-${var.lcaas}"
      labels  = {
        name  = "email-security-actions-commands-${var.lcaas}"
        app   = "cortex-platform"
      }
      sub = {
        email_security_actions_commands_sub = {
          name = "email-security-actions-commands-${var.lcaas}-sub"
          labels = {
            name = "email-security-actions-commands-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    email_security_retry_commands_topic = {
      enabled = local.enable_email_sku
      name    = "email-security-retry-commands-${var.lcaas}"
      labels  = {
        name  = "email-security-retry-commands-${var.lcaas}"
        app   = "cortex-platform"
      }
      sub = {
        email_security_retry_commands_sub = {
          name = "email-security-retry-commands-${var.lcaas}-sub"
          labels = {
            name = "email-security-retry-commands-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    email_security_quick_commands_topic = {
      enabled = local.enable_email_sku
      name    = "email-security-quick-commands-${var.lcaas}"
      labels  = {
        name  = "email-security-quick-commands-${var.lcaas}"
        app   = "cortex-platform"
      }
      sub = {
        email_security_quick_commands_sub = {
          name = "email-security-quick-commands-${var.lcaas}-sub"
          labels = {
            name = "email-security-quick-commands-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    email_security_commands_responses_topic = {
      enabled = local.enable_email_sku
      name    = "email-security-commands-responses-${var.lcaas}"
      labels  = {
        name  = "email-security-commands-responses-${var.lcaas}"
        app   = "cortex-platform"
      }
      sub = {
        email_security_commands_responses_sub = {
          name = "email-security-commands-responses-${var.lcaas}-sub"
          labels = {
            name = "email-security-commands-responses-${var.lcaas}-sub"
            app  = "cortex-platform"
          }
        }
      }
    }
    itdr_cap_responder_agent_tokens_topic = {
      enabled = var.enable_itdr
      name    = "itdr-cap-responder-agent-tokens-${var.lcaas}"
      labels  = {
        name  = "itdr-cap-responder-agent-tokens-${var.lcaas}"
        app   = "itdr"
      }
      sub = {}
    }
    itdr_cap_responder_tokens_result_topic = {
      enabled = var.enable_itdr
      name    = "itdr-cap-responder-tokens-result-${var.lcaas}"
      labels  = {
        name  = "itdr-cap-responder-tokens-result-${var.lcaas}"
        app   = "itdr"
      }
      sub = {}
    }

    itdr_mfa_tokens_topic = {
      enabled = var.enable_itdr
      name    = "itdr-mfa-tokens-${var.lcaas}"
      labels  = {
        name  = "itdr-mfa-tokens-${var.lcaas}"
        app   = "itdr"
      }
      sub = {
        itdr_cap_responder_tokens_sub = {
          name = "itdr-cap-responder-tokens-${var.lcaas}-sub"
          labels = {
            name = "itdr-cap-responder-tokens-${var.lcaas}-sub"
            app  = "itdr"
          }
        }
      }
    }
    itdr_asset_updates_topic = {
      enabled = var.enable_itdr
      name    = "itdr-asset-updates-${var.lcaas}"
      labels  = {
        name  = "itdr-asset-updates-${var.lcaas}"
        app   = "itdr"
      }
      sub = {
        itdr_data_pipeline_asset_updates_sub = {
          name = "itdr-data-pipeline-asset-updates-${var.lcaas}-sub"
          labels = {
            name = "itdr-data-pipeline-asset-updates-${var.lcaas}-sub"
            app  = "itdr"
          }
        }
      }
    }
  }
  lf_syslog_data_topic = {
    name    = "lf-data-${var.lcaas}"
    project = "xdr-log-forwarding-${var.viso_env}-01"
    labels = {
      name = "lf-data-${var.lcaas}"
      app  = "xdr-shared"
    }
    sub = {
      lf_syslog_data_sub = {
        name                 = "lf-data-${var.lcaas}-sub"
        project              = "xdr-log-forwarding-${var.viso_env}-01"
        ack_deadline_seconds = 180
        labels = {
          name = "lf-data-${var.lcaas}-sub"
          app  = "xdr-shared"
        }
      }
    }
   }
 }
