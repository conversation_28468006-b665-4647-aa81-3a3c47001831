module "create_config_map" {
  source                     = "../../modules/config_map"
  config_maps                = local.config_maps
  feature_flags_cm           = local.feature_flags_cm
  dml_cm                     = local.dml_cm
  xcloud_cm                  = local.xcloud_cm
  demisto_cm                 = local.demisto_cm
  scortex_deployment_cm      = local.scortex_deployment_cm
  prometheus_config_map      = local.prometheus_config_map
  prometheus_config_map_data = local.prometheus_config_map_data
}

locals {
  config_maps = [
    {
      enabled   = true
      name      = "common-confimaps-external-ips"
      namespace = module.create_namespace.ns[local.st_namespace]

      data = {
        APPLICATIONHUB_ENGINE_PROJECT_EXTERNAL_IPS = join(",", local.shared_engines_public_ips)
        XSOARMIGRATIONCONF_IP_TO_WHITELIST_1       = data.google_compute_addresses.engine0_external.addresses.0.address
        XSOARMIGRATIONCONF_IP_TO_WHITELIST_2       = data.google_compute_addresses.engine0_external.addresses.1.address

        "values.yaml" = yamlencode(
          {
            "config" = {
              APPLICATIONHUB_ENGINE_PROJECT_EXTERNAL_IPS = join(",", local.shared_engines_public_ips)
              XSOARMIGRATIONCONF_IP_TO_WHITELIST_1       = data.google_compute_addresses.engine0_external.addresses.0.address
              XSOARMIGRATIONCONF_IP_TO_WHITELIST_2       = data.google_compute_addresses.engine0_external.addresses.1.address
            }
          }
        )
      }
    },
    {
      enabled   = true
      name      = "cts-configmaps-terraform-exports"
      namespace = module.create_namespace.ns[local.cortex_cts_namespace]

      data = {
        "values.yaml" = yamlencode(
          {
            "config" = {
              CTSCONF_CALLERS_LIST = join(",", local.prisma_service_accounts)
            }
          }
        )
      }
    },
    {
      enabled   = true
      name      = "cwp-configmaps-terraform-exports"
      namespace = module.create_namespace.ns[local.st_namespace]
      data = {
         CWP_REGISTRY_SCANNER_VERSION = local.registry_scanner_version
         SP_AUTH_KEY_NAME               = var.sp_jwt_key_id
        # https://fluxcd.io/flux/components/helm/helmreleases/#values-references
        "values.yaml" = yamlencode(
          {
            "config" = {
              CWP_REGISTRY_SCANNER_VERSION = local.registry_scanner_version # registry_scanner_version is a local variable defined in 03_upload_binary.tf file
              SP_AUTH_KEY_NAME       = var.sp_jwt_key_id
            }
          }
        )
      }
    },
    {
      name      = "${var.lcaas}-configmap"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        GCPCONF_CORTEX_REGION_ABBR                                            = var.viso_env == "dev" ? "us" : var.is_fedramp ? "gov" : local.region_short_code
        UNIFIEDASSETINVENTORYCONF_NEO4J_URI                                   = local.enable_cortex_platform ? "neo4j://dp-neo4j-cluster-lb-neo4j:7687" : null
        UNIFIEDASSETINVENTORYCONF_NEO4J_USER                                  = local.enable_cortex_platform ? "neo4j" : null
        UNIFIEDASSETINVENTORYCONF_NEO4J_ENABLED                               = local.enable_cortex_platform ? "True" : null
        UNIFIEDASSETINVENTORYCONF_NEO4J_DATABASE                              = local.enable_cortex_platform ? "PlatformGraph" : null
        ISSUESCONF_INGESTION_FEEDBACK_TOPIC                                   = lookup(var.topics_output, "ap_issue_ingest_feedback", "")
        ISSUESCONF_INGESTION_FEEDBACK_VXP_SUBSCRIPTION                        = lookup(var.subscriptions_output, "ap_issue_ingest_feedback_sub_vxp")
        CTSCONF_SECRET_KMS_KEY_NAME                                           = var.lcaas
        CTSCONF_SECRET_KMS_KEY_RING                                           = "cts-security"
        CTSCONF_SECRET_KMS_LOCATION                                           = var.kms_keyring_region
        CTSCONF_SECRET_KMS_PROJECT_ID                                         = "xdr-kms-project-${var.multi_project_postfix}-01"
        CTSCONF_URL_TARGET_HOST                                               = "xdr-st-${var.lcaas}-cts.cortex-cts.svc.cluster.local:3435"
        ALERTSCONF_APPLY_RETENTION_SIMULATION                                 = var.is_xsoar ? "False" : null
        XPANSECONF_ENABLE_XPANSE_CONTENT_SEARCH                               = var.is_xpanse
        CLOUDTAGSCONF_ALWAYS_RETRIEVE_CLOUD_TAGS_FOR_ASSETS                   = var.is_xpanse
        UNIFIEDINVENTORYCONF_ENABLE_ASN_DETAILS                               = "True"
        UNIFIEDINVENTORYCONF_ENABLE_CERTIFICATE_PEM_REPLICATION               = var.is_xpanse
        XPANSEEFFECTIVEIPRANGECONF_STAT_COLLECTION_ENABLED                    = var.is_xpanse
        SUPPORTCASECONF_STATS_DATASET                                         = "metrics_center_${replace(var.viso_env, "-", "_")}"
        DSSCONF_XQL_AD_OBJECTS_REDIS_USE_DEFAULT                              = lookup(var.overrides, "enable_dss_redis", false) ? "False" : "True"
        HPL_DSS_REDIS_CONNECTION_STRING                                       = local.dss_redis_connection_string
        DSSCONF_XQL_AD_OBJECTS_REDIS_HOST                                     = "xdr-st-${var.lcaas}-dss-redis"
        HPL_DSS_REDIS_USE_DEFAULT                                             = lookup(var.overrides, "enable_dss_redis", false) ? "False" : "True"
        ANALYTICSCONF_DETECTION_ENGINE_PUBSUB_MULTI_EVENTS_DESTINATION_BUCKET = lookup(var.buckets_output, "ipl-edr-data")
        AnalyticsConf_pickle_vectorized_content_load_optimization             = "false"
        ANALYTICSCONF_CONTENT_LOADER_DATA_BUCKET                              = lookup(var.buckets_output, "feature-data_bucket")
        ANALYTICSTASKPROCESSOR_PS_SUBSCRIPTION                                = lookup(var.subscriptions_output, "analytics_task_processor_sub")
        ANALYTICSTASKPROCESSOR_PS_TOPIC                                       = lookup(var.topics_output, "analytics-task-processor")
        AnalyticsEmailBiConf_email_bi_project_id                              = "xdr-bi-email-data-${var.viso_env}-01"
        AnalyticsEmailBiConf_email_bi_kms_key_location                        = var.region
        AnalyticsEmailBiConf_email_bi_topic_name                              = "bi-email-data"
        AnalyticsEmailBiConf_email_bi_kms_key_ring                            = "bi-email-data"
        AnalyticsEmailBiConf_email_bi_kms_key_name                            = "bi_email_data_key"
        ASSOCIATIONREPLICATION_ASSOCIATION_REPLICATION_ENABLED                = contains(["small_epp", "epp"], var.product_tier) ? false : null
        AGENTAPICONF_REDIS_HOST                                               = local.enable_agent_api_redis ? "xdr-st-${var.lcaas}-agent-api-redis" : null
        AGENTAPICONF_REDIS_PORT                                               = local.enable_agent_api_redis ? "6379" : null
        AGENTINTEGRATION_CWP_COMPLIANCE_BASE_URL                              = "http://xdr-st-${var.lcaas}-cwp-compliance-agent-rules-api-svc:8080"
        ANALYTICSCONF_DEDUP_COUNTERS_VISIBILITY                               = var.small_epp ? "False" : null
        XQLSYNCSERVICECONF_UPDATE_SYSTEM_AUDIT                                = var.small_epp ? "False" : null
        XQLSYNCSERVICECONF_UPDATE_INCIDENTS_XQL                               = var.small_epp ? "False" : null
        STORYBUILDERDWPCONF_ENABLED                                           = var.small_epp || var.is_metro_tenant ? "False" : null
        SUPPORTCASECONF_CSP_GENERAL_LINK                                      = var.viso_env != "dev" ? "https://supportcases.paloaltonetworks.com/s/" : null
        APPLICATIONHUB_READINESS_CHECK_JOB_MAX_TIME_SEC                       = "3600"
        ALPHAFEATURES_FORENSICS_TENANT                                        = var.forensics
        ALPHAFEATURES_FORENSICS_MODULE                                        = var.forensics
        ALPHAFEATURES_FORENSICS_DISSOLVABLE_AGENT                             = var.forensics
        ALPHAFEATURES_API_POLLING_ENABLED                                     = var.is_xpanse || var.is_xsiam ? true : null
        ALPHAFEATURES_UNIT42_ENABLED                                          = var.is_fedramp ? false : null
        AGENT_REPORTS_BUCKET                                                  = lookup(var.buckets_output, "agent-reports")
        AGENTANALYTICSCONF_PROJECT_ID                                         = "xdr-agent-analytics-${var.viso_env}-01"
        AGENTANALYTICSCONF_PUBSUB_TOPIC_NAME                                  = var.agent_analytics_topic_name
        AGENTCONF_CC_URL                                                      = "https://${var.cc_fqdn}.traps.paloaltonetworks.com"
        AGENTCONF_CDC_URL                                                     = "https://${var.dc_fqdn}.traps.paloaltonetworks.com"
        AGENTCONF_CH_URL                                                      = "https://${var.ch_fqdn}.traps.paloaltonetworks.com"
        AGENTCONF_ENCRYPTION_KEY                                              = var.agentconfig_encryption_key
        ALERTSCONF_QUEUE_BUCKET_NAME                                          = lookup(var.buckets_output, "alerts-queue")
        AgentFeatureFlagsConf_ff_fedramp                                      = var.viso_env == "prod-fr" ? 1 : var.viso_env == "prod-gv" ? 2 : 0
        ANALYTICSCONF_EXTERNAL_DATASET_BUCKET_NAME                            = var.is_fedramp ? "global-bioc-rules-${var.viso_env}" : "global-bioc-rules-prod"
        ALERTSCONF_FETCHER_TOPIC_NAME                                         = lookup(var.topics_output, "alerts_fetcher")
        ALERTSCONF_FETCHER_SUBSCRIPTION_NAME                                  = lookup(var.subscriptions_output, "alerts_fetcher_sub")
        # CRTX-93789
        RETENTIONCONF_EGRESS_RAW_BUCKET                                                 = lookup(var.buckets_output, "egress_raw_bucket")
        ALERTSFETCHERCONF_GLOBAL_STORAGE_BUCKET_NAME                                    = var.pan_content_rules_bucket
        ALERTSFETCHERCONF_GLOBAL_STORAGE_PROJECT_ID                                     = var.pan_content_rules_project
        ALERTSFETCHERCONF_PUBSUB_EXT_LOGS_SUBSCRIPTION_NAME                             = lookup(var.subscriptions_output, "inr-ext-logs-sub")
        ALERTSFETCHERCONF_PUBSUB_EXT_LOGS_TOPIC                                         = lookup(var.topics_output, "external_logs_topic")
        ALERTSFETCHERCONF_REDIS_ENABLED                                                 = local.enable_fetcher_redis ? "True" : null
        ALERTSFETCHERCONF_REDIS_HOST                                                    = local.enable_fetcher_redis ? "xdr-st-${var.lcaas}-fetcher-redis" : null
        ALERTSFETCHERCONF_REDIS_PORT                                                    = local.enable_fetcher_redis ? "6379" : null
        AnalyticsDDE_metaline_bucket                                                    = lookup(var.buckets_output, "analytics_data")
        AnalyticsConf_multi_tenant_raw_bq_connector_bq_dataset_name                     = "analytics_source_tables"
        AnalyticsConf_multi_tenant_raw_bq_connector_project_id                          = var.viso_env == "dev" ? "xdr-res-analytics-dev-01" : var.viso_env == "prod-fr" ? "xdr-res-analytics-prod-fr-01" : var.viso_env == "prod-gv" ? "xdr-res-analytics-prod-gv-01" : "xdr-res-analytics-prod-us-01"
        ANALYTICSCONF_MULTI_TENANT_RAW_BQ_CONNECTOR_GLOBAL_BUCKET_NAME                  = var.viso_env == "dev" ? "analytics-source-tables-dev" : var.viso_env == "prod-fr" ? "analytics-source-tables-prod-fr" : var.viso_env == "prod-gv" ? "analytics-source-tables-prod-gv" : "analytics-source-tables-prod-us"
        ANALYTICS_OAKENSHIELDDB_CPU                                                     = local.rocksdb_cpu
        ANALYTICSCONF_REDIS_ENABLED                                                     = var.redis_split ? "True" : "False"
        ANALYTICSCONF_REDIS_HOST                                                        = "xdr-st-${var.lcaas}-analytics-redis"
        ANALYTICSCONF_REDIS_PORT                                                        = "6379"
        ANALYTICSGLOBALPROFILES_PROJECT_ID                                              = "xdr-global-profiles-${local.global_profiles_project_env}-01"
        AnalyticsGlobalProfiles_firestore_project                                       = "xdr-global-profiles-${local.global_profiles_project_env}-01"
        AnalyticsGlobalProfiles_provisioning_bq_location                                = upper(local.global_profiles_source_region)
        ANALYTICSGLOBALPROFILES_REGIONAL_BUCKET_EXPORT_PREFIX                           = var.viso_env == "dev" ? "dev-xdr-analytics-global-profiles" : var.viso_env == "prod-fr" ? "federal-xdr-analytics-global-profiles" : var.viso_env == "prod-gv" ? "federal-gv-xdr-analytics-global-profiles" : "xdr-analytics-global-profiles"
        ANALYTICSGLOBALPROFILES_IS_ENABLED                                              = "True"
        ANALYTICSGLOBALPROFILES_CAN_CONTRIBUTE                                          = var.viso_env != "dev" && !var.is_fedramp && (var.tenant_type == "internal" || contains(local.global_profiles_small_regions_list, var.viso_env)) ? "False" : "True"
        AnalyticsGlobalProfiles_profiles_source_region                                  = local.global_profiles_source_region
        ANALYTICS_OAKENSHIELDDB_MEMORY                                                  = replace(local.rocksdb_memory, "Gi", "")
        ANALYTICSCONF_ASSOCIATION_MIGRATION_WINDOW_DAYS                                 = 8
        ANALYTICSCONF_ASSOCIATION_MIGRATION_WINDOW_DAYS                                 = 8
        ANALYTICSCONF_BIG_QUERY_EXPORT_DATA_BUCKET_NAME                                 = lookup(var.buckets_output, "analytics_bq_export")
        ANALYTICSCONF_BIG_QUERY_SRC_DATASET                                             = lookup(var.bq_output, "ds")
        ANALYTICSCONF_BIG_QUERY_SRC_PROJECT                                             = var.project_id
        ANALYTICSCONF_BQ_DATASET                                                        = lookup(var.bq_output, "analytics")
        ANALYTICSCONF_DETECTION_ENGINE_READ_SUBSCRIPTION                                = local.analytics2_enabled || local.xpanse_analytics_enabled ? "edr-matching-service" : "A2_NOT_ENABLED"
        ANALYTICSCONF_DETECTION_HITS_TOPIC                                              = local.analytics2_enabled || local.xpanse_analytics_enabled ? lookup(var.topics_output, "analytics_detection_hits", "A2_NOT_ENABLED") : "A2_NOT_ENABLED"
        ANALYTICSCONF_DISABLE_ANALYTICS_MIGRATIONS                                      = "false"
        ANALYTICSCONF_EMITTER_SUBSCRIPTION_NAME                                         = local.analytics2_enabled || local.xpanse_analytics_enabled ? lookup(var.subscriptions_output, "alerts_emitter", "A2_NOT_ENABLED") : "A2_NOT_ENABLED"
        ANALYTICSCONF_IOC_BLOOM_FILTER_CACHE_BUCKET                                     = lookup(var.buckets_output, "feature-data_bucket")
        ANALYTICSCONF_TIMEZONE_OFFSET                                                   = var.timezone_offset
        ANALYTICSCONF_ENABLE_ROCKS_SST_CREATION_ON_REMOTE                               = local.rocksdb_blue_green_mode || local.rocksdb_cluster_mode ? "False" : local.enable_rocksdb_writer ? "True" : "False"
        ANALYTICSCONF_ROCKSDB_CLUSTER_MODE                                              = local.rocksdb_cluster_mode ? true : null
        ANALYTICSCONF_ROCKSDB_GRPC_CHANNEL_LOCK                                         = local.rocksdb_blue_green_mode ? "True" : null
        ANALYTICSCONF_DISABLE_WAIT_ON_ROCKSDB_COMPACTION                                = local.rocksdb_blue_green_mode || local.rocksdb_cluster_mode ? "False" : null
        ANALYTICSNOTIFICATIONAPICONF_SUBSCRIPTION_NAME                                  = lookup(var.subscriptions_output, "external_notifications_sub")
        ANALYTICSNOTIFICATIONAPICONF_TOPIC_NAME                                         = lookup(var.topics_output, "external_notifications_topic")
        ANALYTICSONDEMANDCONF_CONTENT_BUCKET_NAME                                       = var.viso_env == "dev" ? "analytics-on-demand-content-dev" : var.is_fedramp ? "" : "analytics-on-demand-content-prod"
        ANALYTICSONDEMANDCONF_IS_ENABLED                                                = var.is_fedramp ? "False" : "True"
        ANALYTICSONDEMANDCONF_JOBS_PROJECT_ID                                           = var.is_fedramp ? "" : "xdr-bq-mt-stats-${var.viso_env}-01"
        ANALYTICSONDEMANDCONF_INTERIM_CALC_RESULTS_BQ_DATASET                           = "analytics_on_demand_${var.lcaas}"
        ANALYTICSCONF_PROFILES_VIEWS_DATASET                                            = "analytics_profiles_views_${var.lcaas}"
        ANALYTICSCONF_PROFILES_ROCKSDB_HOST                                             = "analytics-rocksdb"
        ASMPublicApiServiceConf_asm_asset_limit                                         = var.is_xpanse ? "5000" : "500"
        ASMPublicApiServiceConf_external_ip_range_limit                                 = var.is_xpanse ? "5000" : "1000"
        BQSTATSCONF_BQ_STATS_PROJECT                                                    = "xdr-bq-mt-stats-${var.viso_env}-01"
        BQSTATSCONF_ST_SUBSCRIPTION_NAME                                                = "bq-stats-${var.lcaas}-sub"
        BQSTATSCONF_ST_TOPIC_NAME                                                       = "bq-stats-${var.lcaas}"
        BQSTATSCONF_BQ_STATS_METRICS_CENTER_DATASET                                     = replace("metrics_center_${var.viso_env}", "-", "_")
        BROKERCONF_BROKER_BUCKET_NAME                                                   = "xdr-ova-installers-${var.multi_project_postfix}"
        BROKERCONF_FILE_COLLECTOR_BUCKET                                                = lookup(var.buckets_output, "ext_files_bucket")
        BROKERCONF_LOG_REQUEST_BUCKET_NAME                                              = lookup(var.buckets_output, "broker_bucket")
        CA_COLLECTION_CLOUD_PROJECT_ID                                                  = var.project_id
        CA_COLLECTION_LCAAS_ID                                                          = var.lcaas
        CA_COLLECTION_REDIS_CONNECTION_STRING                                           = local.ca_collection_connection_string
        CA_COLLECTION_MYSQL_DB_NAME                                                     = "${var.lcaas}_main"
        CA_COLLECTION_MYSQL_DDL_ENDPOINT                                                = ""
        CA_COLLECTION_MYSQL_DML_ENDPOINT                                                = local.tenant_endpoint_mysql
        CLOUDONBOARDING_AZURE_STORAGE_ALLOWED_IPS                                       = lookup(local.st_proxydome_ip_list, var.viso_env, "")
        THIRDPARTYSAAS_COLLECTOR_SA_UNIQUE_ID                                           = module.harvester_workload_identity.google_service_unique_id
        THIRDPARTYSAAS_COLLECTION_STATIC_IPS                                            = local.enable_cortex_platform ? lookup(local.harvester_ip_list, var.viso_env, "") : null
        CLOUDONBOARDING_MT_KMS_ACCOUNT                                                  = lookup(local.cwp_environment_variables.CWP_AWS_SHARED_RESOURCES_ACCOUNT_ID, var.viso_env, "")
        CLOUDONBOARDING_TEMPLATES_BUCKET                                                = lookup(var.buckets_output, "cloud_onboarding_templates")
        CLOUDONBOARDING_ACCOUNTS_NOTIFICATION_TOPIC                                     = lookup(var.topics_output, "cloud_accounts")
        CLOUDONBOARDING_TASKS_TOPIC                                                     = lookup(var.topics_output, "cloud_onboarding_tasks")
        CLOUDONBOARDING_TASK_SUB                                                        = lookup(var.subscriptions_output, "cloud_onboarding_tasks_sub")
        COLLECTIONOAUTH2_REDIRECT_URL                                                   = "https://agent-gateway-public-api-${var.viso_env}.traps.paloaltonetworks.com"
        COMMUNICATION_SLACK_CHANNEL_NAME                                                = "xdr-app-errors-prod"
        COMMUNICATION_SLACK_CHANNEL_NAME_NEXUS                                          = "cortexdr-prod-errors"
        COMMUNICATION_SLACK_TOKEN                                                       = local.gonzo_slack_token
        COMMUNICATION_USE_SLACK                                                         = local.xdr_use_slack_channel
        CONTENTCONF_STORAGE_BUCKET_NAME                                                 = var.global_content_bucket
        CONTENTCONF_STORAGE_PROJECT_ID                                                  = var.global_content_storage_project_id
        CORRELATIONSRULESCONF_BQ_TEMP_TABLES_DATASET                                    = lookup(var.bq_output, "correlations")
        COPILOT_CHAT_API_URL                                                            = "xdr-st-${var.lcaas}-chat-api-svc:2020"
        COPILOT_MT_PROJECT_ID                                                           = "xdr-cortex-copilot-${var.viso_env}-01"
        COPILOT_CHAT_SERVICE_PROXY                                                      = "https://************:11115"
        COPILOT_CHAT_SERVICE_URL                                                        = "${var.viso_env}.copilot.crtx.paloaltonetworks.com"
        COPILOT_CHAT_SERVICE_CLOUD_RUN_AUDIENCE                                         = "cortex-copilot-${var.viso_env}"
        COPILOT_COPILOT_STATS_PROJECT                                                   = "xdr-bq-mt-stats-${var.viso_env}-01"
        COPILOT_COPILOT_STATS_REGION                                                    = var.bq_location
        COPILOT_MODEL_GARDEN_LOCATION                                                   = var.region
        CORTEX_PLATFORM_URL                                                             = local.enable_cortex_platform ? "xdr-st-${var.lcaas}-platform:8000" : null
        CORTEX_PLATFORM_PUBLIC_DS                                                       = "public_platform_${var.lcaas}"
        CORTEX_PLATFORM_DYNAMIC_CONFIG_DIR                                              = "/etc/config"
        CORTEXASMMONITORING_MT_BUCKET_NAME                                              = local.enable_cortex_platform ? var.viso_env == "dev" ? "cortex-asm-monitoring-dev" : "cortex-asm-monitoring-prod" : null
        CORTEXASMMONITORING_MT_PROJECT                                                  = local.enable_cortex_platform ? var.viso_env == "dev" ? "xp-gcp-h-dev-s-dev" : "xp-gcp-h-prod-s-prod" : null
        CwpOnCortex_bucket_name                                                         = local.scan_results_bucket
        CwpOnCortex_scan_results_topic                                                  = local.scan_results_pubsub_topic
        CwpOnCortex_environment_enabled                                                 = local.enable_cortex_platform ? true : false
        CwpOnCortex_scan_spec_manager_url                                               = "http://xdr-st-${var.lcaas}-cwp-scan-spec-manager-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local:8080"
        CWPREDISCONF_DB_NUMBER                                                          = local.enable_cortex_platform ? 0 : null
        CWPREDISCONF_HOST                                                               = local.enable_cortex_platform ? "xdr-st-${var.lcaas}-cwp-redis" : null
        CWPREDISCONF_PORT                                                               = local.enable_cortex_platform ? 6379 : null
        PLATFORM_IS_ACTIVE                                                              = local.enable_cortex_platform ? true : null
        DASHBOARDENGINECONF_UNUSED_WIDGETS_EXPIRY_DAYS                                  = "5"
        DataIngestionHealth_subscription                                                = "health-alerts-${var.lcaas}_sub"
        DataIngestionHealth_topic                                                       = "health-alerts-${var.lcaas}"
        DATAVISIBILITY_INGEST_RATE_LIMIT_SUBSCRIPTION                                   = "stitched-${var.lcaas}"
        DATA_PLATFORM_FINDINGS_TARGET_HOST                                              = "xdr-st-${var.lcaas}-dp-finding-pipeline.xdr-st.svc.cluster.local:7973"
        DATA_PLATFORM_ASSETS_TARGET_HOST                                                = "xdr-st-${var.lcaas}-dp-asset-ingester.xdr-st.svc.cluster.local:7973"
        DISTRIBUTIONSERVICECONF_CLOUD_RUN_PROJECT                                       = "xdr-cloudrun-${var.multi_project_postfix}-01"
        DISTRIBUTIONSERVICECONF_DISTRIBUTIONS_BUCKET_NAME                               = lookup(var.buckets_output, "distributions_bucket")
        DISTRIBUTIONSERVICECONF_INSTALLERS_BUCKET_NAME                                  = "panw-xdr-installers-${var.multi_project_postfix}"
        DISTRIBUTIONSERVICECONF_REGISTER_URL                                            = "https://distributions${local.distributions_postfix}.traps.paloaltonetworks.com"
        DISTRIBUTIONSERVICECONF_KMS_KEY_PROJECT                                         = "xdr-kms-project-${var.multi_project_postfix}-01"
        DISTRIBUTION_KEY_PROJECT                                                        = "xdr-kms-project-${var.multi_project_postfix}-01"
        DMLCONFIG_network_meta_hours_to_keep                                            = "24"
        DMLCONFIG_network_raw_hours_to_keep                                             = "30"
        DockerImageConf_artifactory_url                                                 = "${var.region}-docker.pkg.dev"
        DockerImageConf_repository_name                                                 = "agent-docker"
        DSSCONF_CERT_KEY                                                                = "/etc/cert/dss.key"
        DSSCONF_CERT_PATH                                                               = "/etc/cert/dss.pem"
        DSSCONF_DSS_URL                                                                 = "${lookup(local.dssconf_dss_url_map, var.viso_env, local.dssconf_dss_url_map["default"])}${local.dssconf_29_suffix}"
        DSSCONF_SERVER_CERTIFICATE_FILE                                                 = "/etc/cert/dss_server.crt"
        DSSCONF_SYNC_WITH_AD                                                            = var.enable_dirsync
        DUMPSTERCONF_ACTIVATE_GCS_MODE                                                  = var.dumpsterconf_activate_gcs_mode
        DUMPSTERCONF_ANALYSIS_REPORTS_BUCKET                                            = "${var.dumpsterconf_analysis_reports_bucket}-${var.viso_env}"
        DUMPSTERCONF_MT_PROJECT_ID                                                      = "xdr-dumpster-${var.viso_env}-01"
        EMAILARTIFACTSRELAY_DECRYPTED_ATTACHMENTS_BUCKET                                = lookup(var.buckets_output, "unknown_mail_attachments")
        ExternalNotificationsConf_pubsub_subscription                                   = lookup(var.subscriptions_output, "notifications_notifier_sub")
        EXPORTCONF_EXPORT_AGENTS_GCS_BUCKET_NAME                                        = lookup(var.buckets_output, "user_exports_bucket")
        FIRESTOREACCESSSERVICE_FIRESTORE_SERVICE_ENDPOINT                               = var.fas_url
        FIRESTOREACCESSSERVICE_KEY_LOCATION                                             = var.encrypt_fas_keyring_location
        FIRESTOREACCESSSERVICE_KEY_RING                                                 = var.encrypt_fas_keyring_name
        FIRESTOREACCESSSERVICE_KEY_VERSION                                              = 1
        FIRESTOREACCESSSERVICE_KMS_PROJECT_ID                                           = "xdr-kms-project-${var.multi_project_postfix}-01"
        FIRESTORECONF_PROJECT_ID                                                        = var.firestore_public_project_id
        FLIPPERCONF_BUCKET_NAME                                                         = var.changed_verdicts_bucket
        FLIPPERCONF_CHANGED_VERDICTS_API_URL                                            = "${lookup(local.gvs_url_map, var.viso_env, local.gvs_url_map["default"])}/service/wildfire/changed/verdicts"
        FLIPPERCONF_MISSED_FLIPS_BUCKET_NAME                                            = local.missed_flips_bucket_name
        FORENSICSCONF_DATASET_ID                                                        = lookup(var.bq_output, "forensics")
        FORENSICSCONF_EXPORT_BUCKET                                                     = lookup(var.buckets_output, "forensics_bucket")
        IosConf_use_sandbox                                                             = var.viso_env == "dev" ? "True" : "False"
        IosConf_auth_key_path                                                           = "/etc/ssh/ApnKey.p8"
        GCPCONF_BACKUP_EXTERNAL_DATASET                                                 = var.viso_env == "dev" ? "False" : "True"
        GCPCONF_BQ_DATASET                                                              = lookup(var.bq_output, "ds")
        GCPCONF_BQ_ENUMS_DATASET                                                        = lookup(var.bq_output, "enums")
        GCPCONF_BQ_EXTERNAL_DATASET                                                     = lookup(var.bq_output, "external_data")
        GCPCONF_BQ_TEMP_TABLE_DATASET                                                   = lookup(var.bq_output, "temp_tables")
        GCPCONF_BQ_REWARMED_DS_NAME                                                     = lookup(var.bq_output, "rewarmed_tables")
        GCPCONF_INGESTION_RATE_METRIC_NAME                                              = "xdr:xql_ingestion_raw_size_bytes:rate1h"
        GCPCONF_PROJECT_ID                                                              = var.project_id
        GCPCONF_REAL_REGION                                                             = var.region
        GCPCONF_REGION                                                                  = var.bq_location
        GCPCONF_REGION_ABBR                                                             = lookup(local.region_abbr, var.viso_env, local.region_abbr["default"])
        GCPCONF_ENV_REGION                                                              = var.viso_env
        GCPGEOLOCATIONCONF_DATASET                                                      = var.viso_env == "dev" ? "geolite_dev" : local.geo_location_dataset
        GCPGEOLOCATIONCONF_PROJECT                                                      = var.viso_env == "dev" ? "xdr-bq-mt-stats-dev-01" : local.geo_location_project
        GCPGEOLOCATIONCONF_TABLE                                                        = var.viso_env == "dev" ? "maxmind" : local.geo_location_table
        GCPPUBSUB_EDR_TOPIC_NAME                                                        = lookup(var.topics_output, "edr_topic")
        GCPPUBSUB_EXT_LOGS_TOPIC_NAME                                                   = lookup(var.topics_output, "external_logs_topic")
        GCPPUBSUB_LCAAS_TOPIC_NAME                                                      = lookup(var.topics_output, "lcaas_topic")
        GCPPUBSUB_XDR_ANALYTICS_PROJECT_ID                                              = var.is_fedramp ? "" : var.xdr_analytics_project_name
        GCPPUBSUB_XDR_ANALYTICS_TOPIC_NAME                                              = var.is_fedramp ? "" : "analytics-ingress"
        GCPPUBSUB_XDR_ANALYTICS_PROJECT_ID_ENABLED                                      = (var.viso_env == "dev" || var.is_fedramp) ? "False" : "True"
        GENERIC_ALLOW_EXTERNAL_CONFIG_SETTING                                           = "False"
        GENERIC_CSP_ACCOUNT_NAME                                                        = var.support_account_name
        GENERIC_CSP_ID                                                                  = var.csp_id
        GENERIC_DEFAULT_URL_TARGET_HOST                                                 = "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        POLICYCONF_POLICY_RECALC_TASK_TARGET_HOST                                       = var.api_split ? "${local.core_apps.api-agent-be.app_name}:${local.core_apps.api-agent-be.port}" : null
        PUBLICAPI_TARGET_HOST                                                           = var.api_split ? "${local.core_apps.api-papi.app_name}:${local.core_apps.api-papi.port}" : "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        WLM_TARGET_HOST                                                                 = var.api_split ? "${local.core_apps.api-be.app_name}:${local.core_apps.api-be.port}" : "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        SCHEDULERCONF_TARGET_HOST                                                       = var.api_split ? "${local.core_apps.api-be.app_name}:${local.core_apps.api-be.port}" : "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        XSOARCONF_TARGET_HOST                                                           = var.api_split && var.is_xsiam ? "${local.core_apps.api-xsoar.app_name}:${local.core_apps.api-xsoar.port}" : "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        XQLGLOBALCONFDEFAULT_TARGET_HOST                                                = var.api_split ? "${local.core_apps.api-xql.app_name}:${local.core_apps.api-xql.port}" : "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        GENERIC_DML_ACTIVATED                                                           = var.enable_pipeline ? "True" : "False"
        GENERIC_ENABLE_ANALYTICS                                                        = var.enable_analytics
        GENERIC_EXTERNAL_FQDN                                                           = var.external_fqdn
        GENERIC_IS_EPP                                                                  = !var.enable_pipeline && !var.is_xpanse ? "True" : "False"
        GENERIC_IS_GCP_SETUP                                                            = "True"
        GENERIC_TENANT_TYPE                                                             = var.is_xpanse ? "xpanse" : null
        GENERIC_XDR_ENVIRONMENT                                                         = var.xdr_env
        GENERIC_XDR_ID                                                                  = var.xdr_id
        GENERIC_CORTEX_ID                                                               = var.xdr_id
        GENERIC_PRODUCT_TYPE                                                            = var.product_type
        GENERIC_PRODUCT_CODE                                                            = var.product_code
        GENERIC_XDR_TENANT_NAME                                                         = var.instance_name
        GENERIC_XDR_TENANT_TYPE                                                         = var.tenant_type
        GENERIC_IS_METRO                                                                = var.is_metro_tenant ? "True" : "False"
        GLOBALCONF_GCS_BUCKET                                                           = local.global_config_bucket
        GLOBALCONF_GCS_BUCKET_REGION_FOLDER                                             = var.viso_env
        GLOBALVERDICTSERVICECONF_CRT_FILE_PATH                                          = "/cert/wf-verdict-service.crt.pem"
        GLOBALVERDICTSERVICECONF_FF_USE_V1                                              = var.is_fedramp ? "True" : "False"
        GLOBALVERDICTSERVICECONF_KEY_FILE_PATH                                          = "/cert/wf-verdict-service.key.pem"
        GLOBALVERDICTSERVICECONF_VERDICT_SERVICE_HOST                                   = lookup(local.gvs_url_map, var.viso_env, local.gvs_url_map["default"])
        GONZO_ENV                                                                       = var.viso_env
        GONZO_LOGGER_TRANSPORT                                                          = var.viso_env == "dev" && !var.is_metro_tenant ? "console:-1" : "console:0,slack:2"
        GONZO_PROJECT_ID                                                                = var.project_id
        GONZO_REDIS_CONNECTION_STRING                                                   = local.gonzo_redis_connection_string
        GONZO_REGION                                                                    = var.region
        GONZO_SLACK_CHANNEL                                                             = "xdr-pipeline-${var.viso_env}"
        GONZO_SLACK_TOKEN                                                               = local.gonzo_slack_token
        HIGHAVAILABILITY_HIGH_AVAILABILITY_PUBSUB_TOPIC                                 = local.enable_playbook_mirroring ? "high-availability-tenants-sync" : null
        HIGHAVAILABILITY_HIGH_AVAILABILITY_PUBSUB_SUBSCRIPTION                          = local.enable_primary_playbook_mirroring ? "high-availability-tenants-sync-sub" : null
        HIGHAVAILABILITY_SECONDARY_PROJECT_ID                                           = local.enable_playbook_mirroring ? local.secondary_project_id : null
        HIGHAVAILABILITY_PRIMARY_PROJECT_ID                                             = local.enable_playbook_mirroring ? local.primary_project_id :  null
        HIGHAVAILABILITY_IS_HIGH_AVAILABILITY_TENANT                                    = local.enable_playbook_mirroring ? "True" :  null
        HPL_BIGQUERY_INGESTER_DATASET                                                   = "ds_${var.lcaas}"
        HPL_BIGQUERY_INGESTER_NUM_PIPELINE_ROUTINES                                     = "32"
        HPL_BIGQUERY_INGESTER_TABLE                                                     = "edr_data"
        HPL_BIGQUERY_INGESTOR_ERROR_TOPIC                                               = lookup(var.topics_output, "bq_errors_topic")
        HPL_COLD_STORAGE_DATASET_AGGREGATOR_PUB_SUB_TOPIC                               = lookup(var.topics_output, "cold_storage_aggregation", "")
        HPL_COLD_STORAGE_GCS_TARGET_BUCKET                                              = lookup(var.buckets_output, "cold-storage-quantums-raw")
        HPL_COLD_STORAGE_AGGREGATOR_RAW_DATA_BUCKET                                     = lookup(var.buckets_output, "cold-storage-quantums-raw")
        HPL_DEDUP_REDIS_CONNECTION_STRING                                               = lookup(var.overrides, "enable_gonzo_dragonfly", false) ? "xdr-st-${var.lcaas}-gonzo-dragonfly:6379" : var.redis_split ? "xdr-st-${var.lcaas}-gonzo-redis:6379" : local.gonzo_redis_connection_string
        HPL_DEDUP_REDIS_USE_DEFAULT                                                     = var.redis_split || lookup(var.overrides, "enable_gonzo_dragonfly", false) ? "False" : "True"
        HPL_DMS_AGED_STITCHED_TIME                                                      = "24h"
        HPL_DMS_NETWORK_RAW_TIME_RANGE                                                  = "24h"
        HPL_DMS_SUBSCRIPTION                                                            = "stitched-${var.lcaas}"
        HPL_EDR_SOURCE_MAX_OUTSTANDING_MESSAGES                                         = "40"
        HPL_EDR_SOURCE_PROJECT_ID                                                       = var.project_id
        HPL_EDR_SOURCE_SUBSCRIPTION                                                     = lookup(var.subscriptions_output, "edr_raw_sub")
        HPL_ENABLE_DML_INGESTER                                                         = "True"
        HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_HOSTS                                         = local.scylla_endpoint
        HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_USER                                          = local.scylla_username
        HPL_GCP_PROJECT_ID                                                              = var.project_id
        HPL_GONZO_REDIS_CONNECTION_STRING                                               = local.hpl_gonzo_redis_connection_string
        HPL_GONZO_REDIS_USE_DEFAULT                                                     = local.hpl_gonzo_redis_use_default
        HPL_LEGACY_EDR_INGESTER_TOPIC                                                   = "edr-${var.lcaas}"
        HPL_MYSQL_DML_ENDPOINT                                                          = local.tenant_endpoint_mysql
        HPL_MYSQL_DDL_ENDPOINT                                                          = ""
        HPL_MYSQL_DB_NAME                                                               = "${var.lcaas}_main"
        HPL_PANIC_RECOVERY_ENABLE_RECOVERY                                              = "True"
        HPL_PIPELINE_ERRORS_BUCKET                                                      = lookup(var.buckets_output, "pipeline_errors_bucket")
        HPL_PZ_SCRIPT_RESULTS_SUBSCRIPTION                                              = lookup(var.subscriptions_output, "dml_script_results_sub", "")
        HPL_PZ_PUBSUB_SUBSCRIPTION                                                      = lookup(var.subscriptions_output, "pz_ext_logs_sub", "")
        HPL_PZ_LCAAS_SUBSCRIPTION                                                       = "lavawall-${var.lcaas}"
        HPL_PZ_WEF_SUBSCRIPTION                                                         = lookup(var.subscriptions_output, "dml_broker_wef_sub")
        HPL_REDIS_CONNECTION_STRING                                                     = local.gonzo_redis_connection_string
        HPL_STORY_BUILDER_PUBLISH_TOPIC                                                 = lookup(var.topics_output, "stitched_topic")
        HPL_STITCHER_PUBLISHER_TOPIC                                                    = ""
        HPL_STITCHER_QUERY_MAGNIFIER_DELAY                                              = "240s"
        HPL_STITCHER_QUERY_XDR_DELAY                                                    = "240s"
        HPL_STITCHER_STITCHING_OLDEST_TIME                                              = "24h"
        HPL_XQL_BIGQUERY_ERROR_TOPIC                                                    = lookup(var.topics_output, "xql-ingester-errors")
        HPL_XQL_BIGQUERY_EXTERNAL_LOGS_DATASET                                          = lookup(var.bq_output, "external_data")
        HPL_XQL_EXT_DL_SUBSCRIPTION                                                     = "xql-dl-sub-${var.lcaas}"
        HPL_XQL_EXT_SUBSCRIPTION                                                        = "xql-ext-logs-${var.lcaas}-sub"
        HPL_XQL_PZ_SCHEMA_MANAGER_HOST                                                  = "xdr-st-${var.lcaas}-pz-schema-manager:4981"
        HPL_XQL_ENABLE_METABLOB_INGESTER                                                = var.xsiam_licenses > 0 ? "True" : "False"
        HPL_XQL_MB_INGESTER_TRANSPORT_ANALYTICS_BUCKET                                  = local.analytics_detection_engine_external_data ? lookup(var.buckets_output, "external-data") : null
        HPL_XQL_MB_INGESTER_TRANSPORT_ANALYTICS_TOPIC                                   = local.analytics_detection_engine_external_data ? lookup(var.topics_output, "external_topic") : null
        HPL_MAXMIND_ASN_BUCKET                                                          = "maxmind-${var.viso_env}"
        HPL_MAXMIND_CITY_BUCKET                                                         = "maxmind-${var.viso_env}"
        HPL_MAXMIND_CITY_OBJECT                                                         = "maxmind/GeoLite2-City.mmdb"
        HPL_MAXMIND_ASN_OBJECT                                                          = "maxmind/GeoLite2-ASN.mmdb"
        HPL_MAXMIND_BUCKET                                                              = "maxmind-${var.viso_env}"
        HPL_MAXMIND_TAR                                                                 = "maxmind/maxmind_db.tar"
        HPL_MAXMIND_ASN_FILE_NAME                                                       = "GeoLite2-ASN.mmdb"
        HPL_MAXMIND_CITY_FILE_NAME                                                      = "GeoLite2-City.mmdb"
        http_proxy                                                                      = "http://${var.tenant_proxy}:13128"
        https_proxy                                                                     = "http://${var.tenant_proxy}:13128"
        HYDRACONF_KEY_LOCATION                                                          = var.encrypt_fas_keyring_location
        HYDRACONF_KEY_RING                                                              = "hydra-tenant-ring"
        HYDRACONF_KEY_VERSION                                                           = 1
        HYDRACONF_KMS_PROJECT_ID                                                        = "xdr-kms-project-${var.multi_project_postfix}-01"
        HYDRACONF_MT_PUBSUB_PROJECT_ID                                                  = "xdr-agent-gateway-${var.viso_env}-01"
        HYDRACONF_MT_PUBSUB_TOPIC                                                       = "hydra-incoming-topic"
        INGESTER_CLOUD_STORAGE_SOURCE_BUCKET                                            = lookup(var.buckets_output, "ingestion_bucket")
        INGESTER_INTERVAL_SEC                                                           = 180
        INGESTER_PUBSUB_ERRORS_TOPIC                                                    = lookup(var.topics_output, "ingestion_errors_topic")
        LOOKUPSCONF_BACKUP_LOOKUP                                                       = var.viso_env == "dev" ? "False" : "True"
        RETENTIONCONF_XSOAR_RETENTION_DEFAULT_MONTHS                                    = var.is_xsoar_onprem_migration && !var.xsoar_mssp_child && !var.xsoar_mssp_master && !var.xsoar_mssp_dev ? 12 : null
        EXTERNALINTEGRATIONFORWARDERCONF_EXTERNAL_INTEGRATION_FORWARDER_PS_SUBSCRIPTION = "external-integration-data-forwarder-${var.lcaas}-sub"
        EXTERNALINTEGRATIONFORWARDERCONF_EXTERNAL_INTEGRATION_FORWARDER_PS_TOPIC        = "external-integration-data-forwarder-${var.lcaas}"
        # Email data CRTX-112920
        EMAILARTIFACTSRELAY_EMAIL_ARTIFACTS_RELAY_PS_SUBSCRIPTION = lookup(var.subscriptions_output, "email_data_sub")

        # DLQ Topic XDR-34598-Merge-fix
        HPL_DEDUP_DEAD_LETTER_QUEUE_ENABLED                                = "False"
        HPL_DEDUP_DEAD_LETTER_QUEUE_TOPIC_PREFIX                           = "dlq"
        HPL_EXT_NAMESPACE                                                  = "ext"
        HPL_DMS_NAMESPACE                                                  = "dms"
        HPL_PZ_NAMESPACE                                                   = "pz"
        HPL_XQL_NAMESPACE                                                  = "xql"
        INGESTER_USE_STREAM                                                = "False"
        INGESTER_VALIDATE_LCAAS_ID_ON_INCOMING_STREAM                      = "True"
        KMSCONF_BROKER_VM_KEY_LOCATION                                     = var.encrypt_fas_keyring_location
        KMSCONF_BROKER_VM_KEY_NAME                                         = var.lcaas
        KMSCONF_BROKER_VM_KEY_RING                                         = "broker-vm"
        KMSCONF_BROKER_VM_KEY_VERSION                                      = 1
        KMSCONF_BROKER_VM_KMS_PROJECT_ID                                   = "xdr-kms-project-${var.multi_project_postfix}-01"
        KMSCONF_MT_KEY_LOCATION                                            = var.kms_keyring_region
        KMSCONF_MT_KEY_NAME                                                = "master"
        KMSCONF_MT_KEY_RING                                                = var.kms_keyring_name
        KMSCONF_MT_KEY_VERSION                                             = "1"
        KMSCONF_MT_PROJECT_ID                                              = "xdr-distributions-${var.multi_project_postfix}-01"
        KMSCONF_ST_KEY_LOCATION                                            = var.kms_keyring_region
        KMSCONF_ST_KEY_NAME                                                = "slave"
        KMSCONF_ST_KEY_RING                                                = var.kms_keyring_name
        KMSCONF_ST_KEY_VERSION                                             = "1"
        KMSCONF_TSF_ENC_DECRYPT_KEY_NAME                                   = "enc-dec"
        KMSCONF_TSF_ENC_KEY_LOCATION                                       = var.region
        KMSCONF_TSF_ENC_KEY_RING                                           = "tsf-encrypt-decrypt"
        KMSCONF_TSF_ENC_KEY_VERSION                                        = "1"
        LICENSEMGR_LICENSE_REGION                                          = var.license_region
        LICENSINGCONF_APOLLO2_QUOTA_COLLECTION_NAME                        = var.viso_env == "dev" ? "quotaConfigs-qa-uat" : "quotaConfigs-prod"
        LICENSINGCONF_APOLLO2_QUOTA_PROJECT_NAME                           = var.viso_env == "dev" ? "pan-qa-logging-svc-portal" : "pan-gov-logging-svc-portal"
        LICENSINGCONF_DISABLE_BACKEND_LICENSE_CHECK                        = local.LicensingConf_disable_backend_license_check
        LICENSINGCONF_DISABLE_FRONTEND_LICENSE                             = local.LicensingConf_disable_frontend_license
        LOGCONF_LOG_BQ_PROJECT_ID                                          = "xdr-bq-mt-stats-${var.viso_env}-01"
        LOGCONF_LOG_CONSOLE_MIN_SEVERITY                                   = var.viso_env == "dev" && !var.is_metro_tenant ? "DEBUG" : "INFO"
        LOGCONF_LOG_FILE_ENABLED                                           = "False"
        LOGCONF_LOG_SLACK_CHANNEL_NAME_BACKEND                             = var.viso_env == "dev" ? "cortexdr-dev-errors" : "cortexdr-prod-errors"
        LOGCONF_LOG_SLACK_CHANNEL_NAME_FRONTEND                            = var.viso_env == "dev" ? "xdr-app-errors-devfe" : "xdr-app-errors-prodfe"
        LOGCONF_FE_LOG_SLACK_ENABLED                                       = var.viso_env == "dev" ? "False" : "True"
        LOGGINGSERVICE_LCAAS_TENANT                                        = var.lcaas
        LOGGINGSERVICE_LCAAS_TOPIC_NAME                                    = "${var.lcaas}_lcaas"
        LOGPROCESSORCONF_LOG_PROCESSOR_TOPIC_NAME                          = lookup(var.topics_output, "log_processor_topic")
        LOGPROCESSORCONF_PUBSUB_ERRORS_TOPIC_NAME                          = lookup(var.topics_output, "log_processor_errors_topic")
        LOGPROCESSORCONF_DML_SCRIPT_RESULTS_TOPIC                          = lookup(var.topics_output, "dml_script_results_topic")
        LOGPROCESSORCONF_REPORTED_ISSUES_SUBSCRIPTION                      = local.enable_cortex_platform ? "agent-management-reported-issues-sub-${var.lcaas}" : null
        FORENSICSPROCESSORCONF_ENABLED                                     = var.is_metro_tenant ? "False" : null
        FORENSICSPROCESSORCONF_TOPIC_NAME                                  = "forensics-processor-${var.lcaas}"
        KafkaService_metablob_max_size                                     = "9500000"
        LOOKUPSCONF_BQ_DATASET                                             = lookup(var.bq_output, "lookup")
        LOOKUPSCONF_BUCKET_NAME                                            = lookup(var.buckets_output, "lookups_bucket")
        LOOKUPSCONF_LOOKUPS_CLONE_DATASET                                  = "lookups_clones_${var.lcaas}"
        MODIFICATIONSUBSCRIBERCONF_MODIFICATION_TOPIC_NAME                 = lookup(var.topics_output, "modification_topic")
        MAILINGCONF_SMTP_SERVER_ADDR                                       = "************"
        MAILINGCONF_SMTP_SERVER_PORT                                       = var.is_fedramp ? "11129" : "11127"
        MAILINGCONF_USERNAME                                               = "apikey"
        MAILINGCONF_USE_SSL                                                = var.is_fedramp ? "True" : "False"
        ManagementAuditConf_pubsub_subscription                            = lookup(var.subscriptions_output, "mgmt_audit_notifier_sub")
        MSSPCONF_PERMISSIONS_API_URL                                       = var.msspconf_permissions_api_url
        MSSPCONF_PERMISSIONS_BY_CSP_URL                                    = var.msspconf_permissions_by_csp_url
        MSSPCONF_IS_MSSP_CHILD_SHARED_LICENSE                              = var.is_mssp_child_xdr_xsiam
        MTHCONF_MTH_BCC_MAIL_ADDRESS                                       = var.viso_env == "dev" ? "" : "<EMAIL>"
        MYSQLCONF_BACKUP_BUCKET_NAME                                       = lookup(var.buckets_output, "mysql-backup")
        MYSQLCONF_BACKUP_METRICS_JOB_INTERVAL_MINUTES                      = var.mysql_backup_metrics_job_interval_minutes
        MYSQLCONF_BACKUP_SNAPSHOT_METRICS_ENABLED                          = lookup(var.overrides, "enable_mysql_backup_metrics", local.env_type == "dev" ? "False" : "True")
        MYSQLCONF_BACKUP_SNAPSHOT_METRICS_VALID_DAILY_RETENTION_IN_MINUTES = var.mysql_backup_snapshot_metrics_valid_retention_in_minutes
        MYSQLCONF_HOST                                                     = local.tenant_endpoint_mysql
        MysqlConf_backup_snapshot_metrics_job_interval_minutes             = "20"
        no_proxy                                                           = "xdr-st-${var.lcaas}-frontend,xdr-st-${var.lcaas}-platform,.internal,.googleapis.com,xdr-st-${var.lcaas}-xql-engine,${local.core_apps.api-agent-be.app_name},${local.core_apps.api.app_name},${local.core_apps.api-be.app_name},${local.core_apps.api-papi.app_name},${local.core_apps.api-xql.app_name},${local.core_apps.api-xsoar.app_name},localhost,${var.tenant_proxy},xdr-st-${var.lcaas}-reports-chrome,xdr-st-${var.lcaas}-xsoar,xdr-st-${var.lcaas}-pz-schema-manager,xdr-st-${var.lcaas}-xsoar-migration,xdr-st-${var.lcaas}-chat-api-svc,127.0.0.1,${var.region}-docker.pkg.dev,xdr-st-${var.lcaas}-xsoar-content,xdr-st-${var.lcaas}-engine-hub,***********,************,${local.tenant_endpoint_scortex},xdr-st-${var.lcaas}-cts.cortex-cts.svc.cluster.local,xdr-st-${var.lcaas}-cwp-ads-api-svc,xdr-st-${var.lcaas}-cwp-rules-management-svc,***********,xdr-st-${var.lcaas}-xsoar-api,xdr-st-${var.lcaas}-cwp-api-svc,xdr-st-${var.lcaas}-cwp-malware-analyzer-svc.xdr-st.svc.cluster.local,xdr-st-${var.lcaas}-cwp-secret-analyzer-svc.xdr-st.svc.cluster.local,xdr-st-${var.lcaas}-cwp-vulnerability-analyzer-svc.xdr-st.svc.cluster.local,xdr-st-${var.lcaas}-ciem-api-svc,xdr-st-${var.lcaas}-dspm-fda-api-svc,xdr-st-${var.lcaas}-apisec-bff-service-svc,xdr-st-${var.lcaas}-cwp-ci-analyzer-svc,xdr-st-${var.lcaas}-dspm-data-classification-settings,xdr-st-${var.lcaas}-uvem-vxp-api,xdr-st-${var.lcaas}-uvem-vip-api,xdr-st-${var.lcaas}-dp-finding-pipeline.xdr-st.svc.cluster.local:7973,xdr-st-${var.lcaas}-dp-asset-ingester.xdr-st.svc.cluster.local:7973,cloudsec-${var.lcaas}-secops-dash-api.cloudsec.svc.cluster.local:8080,source-control.cas.svc.cluster.local:80,cloudsec-${var.lcaas}-dashboard-api.cloudsec.svc.cluster.local:8080,xdr-st-${var.lcaas}-cwp-compliance-agent-rules-api-svc,xdr-st-${var.lcaas}-cwp-scan-spec-manager-svc,opentelemetry-collector.monitoring.svc.cluster.local,xdr-st-${var.lcaas}-cwp-scan-spec-manager-svc.xdr-st.svc.cluster.local:8080,alyx.alyx.svc.cluster.local"
        GCPPUBSUB_PLAYBOOK_EXEC_TOPIC_NAME                                 = lookup(var.topics_output, "playbook_execution_topic")
        PARTYZAURUSCONF_XQLE_REDIS_ADDRESS                                 = lookup(var.overrides, "enable_gonzo_dragonfly", false) ? "xdr-st-${var.lcaas}-gonzo-dragonfly" : var.redis_split ? "xdr-st-${var.lcaas}-gonzo-redis" : null
        PROMETHEUS_MULTIPROC_DIR                                           = "."
        PLATFORMCOMPLIANCEMANAGEMENTCONF_STORAGE_PROJECT_ID                = var.is_fedramp ? "xdr-mag-shared-${var.viso_env}-01" : "xdr-zeus"
        PLATFORMCOMPLIANCEMANAGEMENTCONF_STORAGE_BUCKET_NAME               = var.viso_env == "dev" ? "platform-compliance-content-dev" : var.viso_env == "prod-fr" ? "platform-compliance-content-prod-fr" : var.viso_env == "prod-gv" ? "platform-compliance-content-prod-gv" : "platform-compliance-content"
        PUBLICAPI_PAPI_GCS_BUCKET_NAME                                     = "${var.project_prefix}-${var.lcaas}-papi"
        PVMMIGRATIONCONF_API_PATH                                          = "spog/jango/api/v1/migration/"
        RBACCONF_BASE_URI                                                  = local.rbacconf_base_uri
        REDISCONF_ADDR                                                     = var.redis_ip_future
        REDISCONF_BACKUP_INTERVAL                                          = "86400"
        REDISCONF_BACKUP_SNAPSHOT_METRICS_ENABLED                          = lookup(var.overrides, "enable_redis_backup_metrics", local.env_type == "dev" ? "False" : "True")
        REDISCONF_E_VERSION_UPGRADE_DO_REDIS_MIGRATION                     = "True"
        REDISCONF_REDIS_BACKUP_BUCKET_NAME                                 = lookup(var.buckets_output, "redis-backup")
        RBACCONF_REDIS_TTL_USERS_FOR_XSIAM                                 = "600"
        REMOTETERMINAL_PAYLOAD_BUCKET_NAME                                 = "panw-xdr-payloads-${var.multi_project_postfix}"
        REMOTETERMINAL_PAYLOAD_SIGNED_URL_EXPIRATION                       = "600"
        REMOTETERMINAL_RTS_ADDR                                            = var.lrc_addr
        REMOTETERMINAL_RTS_BUCKET_NAME                                     = "${var.project_prefix}-rts-{}"
        REMOTETERMINAL_RTS_WS_SERVER_ADDRESS                               = "https://${var.lrc_addr}"
        REMOTETERMINAL_RTS_WS_SERVER_PORT                                  = "443"
        REPORTSCONF_FF_CHROME_APP                                          = "True"
        REPORTSCONF_REPORTS_BUCKET_NAME                                    = lookup(var.buckets_output, "reports_bucket")
        REPORTSCONF_REPORTS_CHROME_URL                                     = "http://xdr-st-${var.lcaas}-reports-chrome:9222"
        REPORTSCONF_REPORTS_FRONTEND_URL                                   = "http://xdr-st-${var.lcaas}-frontend"
        REPORTSCONF_DASHBOARD_DISABLE_BG_ANIMATION                         = var.is_xpanse ? "True" : null
        RETENTIONCONF_BACKUP_DATASET                                       = var.edr_retention_ds
        RETENTIONCONF_EVENT_FORWARDING_USER_SERVICE_ACCOUNT                = var.egress_enabled ? "event-forwarding-viewer@${var.project_id}.iam.gserviceaccount.com" : ""
        RETENTIONCONF_MANAGEMENT_AUDIT_RETENTION_DAYS                      = var.is_xsoar ? 3650 : 365
        REPORTSCONF_USE_CHROME_JOB                                         = "True"
        REPORTSCONF_KUBE_NAMESPACE                                         = var.is_metro_tenant ? "xdr-st-${var.lcaas}" : "xdr-st"
        REPORTSCONF_CRONJOB_NAME                                           = "xdr-st-${var.lcaas}-chrome-app-cron-job"
        RetentionConf_cold_bucket                                          = lookup(var.buckets_output, "cold_storage_aggregated_bucket")
        RetentionConf_cold_raw_bucket                                      = lookup(var.buckets_output, "cold_storage_raw_bucket")
        RetentionConf_egress_bucket                                        = lookup(var.buckets_output, "event_forwarding_bucket")
        RetentionConf_egress_subscription_name                             = var.egress_enabled ? "event-forwarding-${var.lcaas}-sub" : null
        RetentionConf_enforce_retention                                    = "False"
        RISKSCORECIEPROCESSOR_RISK_SCORE_PS_TOPIC                          = lookup(var.topics_output, "identity_risk_score_updates", "")
        RISKSCORECIEPROCESSOR_RISK_SCORE_PS_SUBSCRIPTION                   = lookup(var.subscriptions_output, "risk_score_cie_updates", "")
        RULESCONF_GLOBAL_EXPORT_FILE_NAME                                  = local.rulesconf_global_export_file_name
        RULESCONF_global_storage_bucket_name                               = var.global_bioc_bucket
        RULESCONF_global_storage_project_id                                = var.global_bioc_project
        MitreTagsConf_global_storage_bucket_name                           = var.global_bioc_bucket
        RulesConf_asymmetric_signing_key_project_id                        = var.is_fedramp ? "xdr-kms-project-${var.multi_project_postfix}-01" : "xdr-kms-project-prod-us-01"
        ScanningConf_evr_bucket_name                                       = "panw-xdr-evr-${var.viso_env}"
        SCORTEXCONF_CATBOOST_MODEL_IP                                      = local.tenant_endpoint_scortex
        SCRIPTSCONF_GLOBAL_SCRIPT_BUCKET_NAME                              = var.global_script_bucket
        SCRIPTSCONF_GLOBAL_SCRIPT_PROJECT_NAME                             = "xdr-restricted-gcs-${var.multi_project_postfix}-01"
        SCRIPTSCONF_SCRIPTS_DEV_PUBLIC_KEY_PATH                            = "/etc/cert/script_public.key"
        SCRIPTSCONF_SCRIPTS_PROD_PUBLIC_KEY_PATH                           = "/etc/cert/script_public_prod.key"
        SCRIPTSCONF_SCRIPTS_PUBLIC_KEY_MODE                                = var.viso_env == "dev" ? "dev" : "prod"
        SCYLLA_NODE_COUNT                                                  = var.scylla_nodes_count
        SCYLLACONF_KEYSPACE                                                = local.scylla_keyspace
        SCYLLACONF_INIT_ON_CORTEX_INIT                                     = var.is_metro_tenant ? "false" : null
        SCYLLACONFXCLOUD_INIT_ON_CORTEX_INIT                               = var.is_metro_tenant ? "false" : null
        SCYLLACONFENRICHMENT_INIT_ON_CORTEX_INIT                           = var.is_metro_tenant ? "false" : null
        SCYLLACONF_USER                                                    = local.scylla_username
        SCYLLACONFXCLOUD_USER                                              = local.scylla_xcloud_username
        SCYLLACONFXCLOUD_SCYLLA_ENDPOINT                                   = local.scylla_xcloud_endpoint
        SCYLLACONFXCLOUD_KEYSPACE                                          = local.scylla_xcloud_keyspace
        SCYLLACONFENRICHMENT_USER                                          = local.scylla_enrichment_username
        SCYLLACONFENRICHMENT_SCYLLA_ENDPOINT                               = local.scylla_enrichment_endpoint
        SCYLLACONFENRICHMENT_KEYSPACE                                      = local.scylla_enrichment_keyspace
        SLACKCONF_HYDRA_REDIRECT                                           = var.slackconf_hydra_redirect
        SLACKCONF_NOTIFICATION_SUBSCRIPTION                                = lookup(var.subscriptions_output, "slack_notification_sub")
        SLACKCONF_NOTIFICATION_TOPIC_NAME                                  = lookup(var.topics_output, "slack_notification_topic")
        XSOARCONF_BACKUP_SNAPSHOT_METRICS_ENABLED                          = lookup(var.overrides, "enable_xsoar_backup_metrics", local.env_type == "dev" ? "False" : "True")
        XSOARCONF_XSOAR_NG_ENABLED                                         = var.enable_xsoar_shared_components
        XSOARCONF_IS_MSSP_CHILD                                            = var.xsoar_mssp_child
        XSOARCONF_FRONTEND_HOST                                            = "***********:80"
        XSOARCONF_HOST                                                     = "***********:5566"
        XSOARCONF_HOST_XSOAR_API                                           = "***********:5566"
        XSOARCONF_USE_XSOAR_API_PODS                                       = local.enable_xsoar_api_pod ? "True" : "False"
        XSOARCONF_AUTH_KEY                                                 = var.xdr_auth_token
        XSOARCONF_HTTP_COLLECTION                                          = var.xdr_http_collection_token
        XSOARCONF_AUTH_KEY_HEADER                                          = "X-XDR-TOKEN"
        xsoarconf_alerts_artifacts_bucket_name                             = lookup(var.buckets_output, "xsoar_alerts_artifacts", "")
        xsoarconf_marketplace_bucket_name                                  = lookup(var.overrides, "marketplace_bootstrap_bypass_url", local.enable_cortex_platform ? "marketplace-cortex-content-${var.multi_project_postfix}/april-content" : "marketplace-${var.product_type}-${var.multi_project_postfix}")
        XSOARCONF_UI_TERM_INCIDENT                                         = local.enable_cortex_platform ? "6" : null
        TIMINDICATORSCONF_ALERTS_TO_XSOAR_SUBSCRIPTION                     = lookup(var.subscriptions_output, "alerts_to_xsoar_sub")
        SPOGCONF_STORAGE_BUCKET_NAME                                       = lookup(var.buckets_output, "shared_bucket")
        STREAMMATCHINGSERVICECONF_pubsub_edr_subscription_name             = var.matching_service_edr_subscription
        STREAMMATCHINGSERVICECONF_PUBSUB_EDR_TOPIC                         = var.matching_service_edr_subscription
        STREAMMATCHINGSERVICECONF_pubsub_lcaas_subscription_name           = var.matching_service_lcaas_subscription
        STREAMMATCHINGSERVICECONF_PUBSUB_LCAAS_TOPIC                       = var.matching_service_lcaas_subscription
        SYSLOGDISPATCHERCONF_FIRESTORE_PROJECT                             = "xdr-log-forwarding-${var.viso_env}-01"
        SYSLOGDISPATCHERCONF_NOTIFICATION_SUBSCRIPTION                     = lookup(var.subscriptions_output, "log_forwarding_sub")
        SYSLOGDISPATCHERCONF_SYSLOG_DISPATCHER_PROJECT_ID                  = "xdr-log-forwarding-${var.viso_env}-01"
        SYSLOGDISPATCHERCONF_SYSLOG_DISPATCHER_TOPIC_NAME                  = local.lf_topic
        SYSLOGDISPATCHERCONF_SYSLOG_NOTIFICATION_TOPIC                     = lookup(var.topics_output, "log_forwarding_topic")
        SYSLOGDISPATCHERCONF_SYSLOG_TEST_TOPIC_NAME                        = var.is_perf_tenant ? "syslog-connection-test-perf-topic" : "syslog-connection-test-topic"
        SYSLOGDISPATCHERCONF_SYSLOG_UPDATE_TOPIC_NAME                      = var.is_perf_tenant ? "lf-syslog-update-perf-topic" : "lf-syslog-update-topic"
        TASKPROCESSOR_TASK_PROCESSOR_PS_SUBSCRIPTION                       = lookup(var.subscriptions_output, "task_processor_sub")
        TASKPROCESSOR_TASK_PROCESSOR_PS_TOPIC                              = lookup(var.topics_output, "task_processor_topic")
        TechSupportFileRetrievalEng_pubsub_subscription                    = var.is_fedramp ? "" : lookup(var.subscriptions_output, "tech_support_file_retrieval_sub")
        TechSupportFileRetrievalEng_notifications_global_project_id        = var.is_fedramp ? "" : "xdr-shared-services-prod-eu-01"
        # There is only one project in eu REGION for shared services
        TechSupportFileRetrievalEng_notifications_global_topic_name = var.viso_env == "dev" ? "tech-support-file-global-dev" : var.is_fedramp ? "" : "tech-support-file-global-prod"
        TIMINDICATORSCONF_TIM_INDICATORS_BUCKET                     = lookup(var.buckets_output, "tim_indicators_bucket")
        TIMINDICATORSCONF_ALERTS_TO_XSOAR_TOPIC                     = lookup(var.topics_output, "alerts_to_xsoar_topic")
        TIMINDICATORSCONF_ALERTS_TO_XSOAR_SUBSCRIPTION              = lookup(var.subscriptions_output, "alerts_to_xsoar_sub")
        TIMINDICATORSCONF_ARTIFACT_EXTRACTION_TOPIC                 = lookup(var.topics_output, "artifact_extraction_topic")
        TIMINDICATORSCONF_ARTIFACT_EXTRACTION_SUBSCRIPTION          = lookup(var.subscriptions_output, "artifact_extraction_sub")
        TIMINDICATORSCONF_ARTIFACT_ALERT_RELATION_SUBSCRIPTION      = lookup(var.subscriptions_output, "xsoar_artifacts_extraction_incident_notifications_sub")
        TIMINDICATORSCONF_ARTIFACT_ALERT_RELATION_TOPIC             = lookup(var.topics_output, "xsoar_artifacts_extraction_incident_notifications")
        THIRDPARTYSAAS_GW_URL                                       = var.saas_endpoint
        THIRDPARTYSAAS_PUBSUB_COLLECTION_RESPONSE_TOPIC             = lookup(var.topics_output, "collection_responses_topic")
        THIRDPARTYSAAS_RESPONSES_SUBSCRIPTION_NAME                  = lookup(var.subscriptions_output, "collection_responses_sub")
        THIRDPARTYSAAS_SAAS_PROXY_URL                               = var.saas_endpoint
        THIRDPARTYSAAS_ONBOARDING_NOTIFICATION_SUB                  = lookup(var.subscriptions_output, "cloud_accounts_log_collection_sub")
        THIRDPARTYSAAS_MSFT_LIFECYCLE_TOPIC                         = lookup(var.topics_output, "msft_lifecycle_notification_topic", "")
        THIRDPARTYSAAS_MSFT_TASK_TOPIC                              = lookup(var.topics_output, "msft_task_notification_topic", "")
        THIRDPARTYSAAS_GSUITE_TASK_TOPIC                            = lookup(var.topics_output, "google_task_notification_topic", "")
        TUSCONF_UPLOADS_BUCKET                                      = lookup(var.buckets_output, "agent-uploads")
        UIAnalyticsConf_analytic_stats_project                      = "xdr-ui-analytics-${var.multi_project_postfix}-01"
        UIAnalyticsConf_analytic_stats_dataset                      = var.is_xpanse && var.viso_env != "dev" ? "ui_analytic_xpanse" : "ui_analytic_mt"
        UIAnalyticsConf_analytic_stats_table                        = "ui_analytic"
        EGRESSPROXY_CA_PATH                                         = "/etc/cert/egress.crt"
        EGRESSPROXY_URL                                             = "************:${local.egress_proxy_port}"
        VIEWSCONF_PROJECT                                           = "xdr-zeus"
        # Boaz: Mainly for fedramp support in the future
        VIEWSCONF_BUCKET = "global-content-anonymization-prod"
        # Boaz: Mainly for fedramp support in the future
        VertexAiBigqueryConf_dataset_name                      = !var.is_fedramp ? "llm_models" : null
        VertexAiBigqueryConf_model_name                        = !var.is_fedramp ? "bq_llm_runtime" : null
        VertexAiBigqueryConf_project_name                      = !var.is_fedramp ? "xdr-cortex-copilot-${var.multi_project_postfix}-01" : null
        VULNERABILITYASSESSMENTCONF_NIST_BUCKET_NAME           = var.vulnerability_assessment_bucket
        VULNERABILITYASSESSMENTCONF_TENANT_REPORTS_BUCKET_NAME = var.viso_env == "dev" ? "global-va-reports-dev" : var.is_fedramp ? "global-va-reports-${var.viso_env}" : "global-va-reports"
        WEBAPPCONF_APP_SWITCHER_URL                            = local.is_uat ? var.uat_switcher_url : var.app_switcher_url
        WEBAPPCONF_APPSWITCHER_JS                              = var.appswitcher_js
        WEBAPPCONF_CERTIFICATE_PATH                            = "/etc/tokens/"
        WEBAPPCONF_DISABLE_FRONTEND_RBAC                       = "False"
        WEBAPPCONF_ENABLE_CSRF                                 = "True"
        WEBAPPCONF_LOGOUT_URL                                  = local.logout_url
        WEBAPPCONF_PIN_HOSTNAME                                = var.external_fqdn
        WEBAPPCONF_RESTRICT_HOSTNAME                           = "True"
        WEBAPPCONF_USE_SUPPORT_INFO                            = "True"
        WEBAPPCONF_VALIDATE_JWT                                = "True"
        WEBAPPCONF_VALIDATE_RBAC                               = "True"
        WEBAPPCONF_VERIFY_JWT                                  = "True"
        WEBAPPCONF_DYNAMIC_FEATURE_FLAGS_DIR                   = "/etc/frontend-config"
        WILDFIRE_APIKEY                                        = var.wildfire_apikey
        WILDFIRE_SERVER_HOSTNAME                               = var.wildfire_url
        WildFire_low_priority_enabled                          = var.is_fedramp ? "False" : "True"
        XCLOUDCONF_ENABLED                                     = var.enable_xcloud
        XCloudAwsConf_xcloud_account_id                        = var.xcloud_aws_account_id
        XCloudAwsConf_xcloud_account_region                    = var.is_fedramp ? "us-gov-east-1" : "us-east-2"
        XCloudAwsConf_default_region                           = var.is_fedramp ? "us-gov-east-1" : "us-east-2"
        # todo: prod-gv
        XCloudGcpConf_default_full_scan_interval_sec                              = var.is_xpanse ? 86400 : null
        XCLOUDCONF_AWS_MASTER_PATH                                                = local.xcloud_onboarding_master_template_url
        XCLOUDCONF_AWS_MEMBER_PATH                                                = local.xcloud_onboarding_member_template_url
        XCLOUDINVENTORYCONF_PB_TOPIC                                              = var.enable_xcloud ? lookup(var.topics_output, "inventory_topic") : "XCLOUD_DISABLED"
        XCLOUDINVENTORYCONF_PB_SUBSCRIPTION                                       = var.enable_xcloud ? lookup(var.subscriptions_output, "inventory_sub") : "XCLOUD_DISABLED"
        XCLOUDINVENTORYCONF_PB_BUCKET_NAME                                        = var.enable_xcloud ? lookup(var.buckets_output, "inventory_bucket") : "XCLOUD_DISABLED"
        XCLOUDREDISCONF_HOSTNAME                                                  = local.xcloud_redis_standalone_deployment ? "xdr-st-${var.lcaas}-xcloud-redis" : "xdr-st-${var.lcaas}-redis"
        XQLCSVEXPORTCONF_BUCKET_NAME                                              = lookup(var.buckets_output, "csv_exports_bucket")
        XQLENGINE_SERVICE_URL                                                     = "http://xdr-st-${var.lcaas}-xql-engine:7973"
        XQLINGESTIONCONF_INGESTER_ERRORS_SUB                                      = lookup(var.subscriptions_output, "xql-ingester-errors-sub")
        XqlSyncServiceConf_GLOBAL_CONTENT_BUCKET                                  = local.xql_global_xql_content_bucket
        XqlSyncServiceConf_per_tenant_content_bucket_format                       = "global-xql-content-${var.viso_env == "dev" ? "qa" : var.is_fedramp ? var.viso_env : "prod"}"
        XQLSYNCSERVICECONF_SIGNING_KEY_PROJECT_ID                                 = "xdr-kms-project-${var.multi_project_postfix}-01"
        XQLSYNCSERVICECONF_SIGNING_KEY_LOCATION                                   = var.kms_keyring_region
        XQLSYNCSERVICECONF_SIGNING_KEY_RING_NAME                                  = "content_signing"
        XQLSYNCSERVICECONF_SIGNING_KEY_NAME                                       = "content_signing_key"
        XSOARMIGRATIONCONF_bq_migration_project                                   = "xdr-bq-mt-stats-${var.viso_env}-01"
        XSOARMIGRATIONCONF_SOURCE_TENANT_ACCESS_SA                                = local.wif_sa
        XSOARMIGRATIONCONF_MIGRATION_BUCKET                                       = lookup(var.buckets_output, "xsoar-migration", "")
        XSOARMIGRATIONCONF_ARTIFACTS_BUCKET                                       = lookup(var.buckets_output, "xsoar_files")
        XSOARMIGRATIONCONF_ONPREM_RETENTION_DEFAULT                               = "12"
        XSOARMIGRATIONCONF_IS_ON_PREM                                             = var.is_xsoar_onprem_migration
        XSOARMIGRATIONCONF_SOURCE_TENANT_HOST                                     = var.is_xsoar_onprem_migration ? "on_prem_migration" : var.xsoar_6_host
        XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY                                  = var.xsoar_6_migration_token
        XSOARMIGRATIONCONF_IP_TO_WHITELIST_1                                      = data.google_compute_addresses.engine0_external.addresses.0.address
        XSOARMIGRATIONCONF_IP_TO_WHITELIST_2                                      = data.google_compute_addresses.engine0_external.addresses.1.address
        XSOARMIGRATIONCONF_TENANT_SERIAL                                          = var.xsoar_6_sn
        XSOARMIGRATIONCONF_BUCKET_ACCESS_SA_NAME                                  = "xsoar-mig-pod"
        XSOARMIGRATIONCONF_DEV_PROD_TYPE                                          = var.xsoar_6_standalone ? "standalone" : var.xsoar_6_env
        XSOARMIGRATIONCONF_BYOG_TARGET_BRANCH                                     = var.xsoar_6_byog_target_branch
        XSOARMIGRATIONCONF_BYOG_USE_MULTI_BRANCH                                  = var.xsoar_6_byog_use_multi_branch
        XSOARMIGRATIONCONF_EMAILS_CC                                              = var.xsoar_6_emails_cc
        XSOARMIGRATIONCONF_DEFAULT_CUTOFF_DATE                                    = var.xsoar_6_cutoff_date
        XSOARMIGRATIONCONF_DEFAULT_CUTOFF_START_HOUR                              = var.xsoar_6_cutoff_start_hour
        XSOARMIGRATIONCONF_DEFAULT_CUTOFF_END_HOUR                                = var.xsoar_6_cutoff_end_hour
        XDRGATEWAYCONF_GATEWAY_URL                                                = local.xdr_gateway_url
        CASESYSTEMSCORINGCONF_SYSTEM_SCORING_BUCKET                               = var.is_fedramp ? "global-bioc-rules-${var.viso_env}" : "global-bioc-rules-prod"
        XDRBQSLOTPRIORITY_ANALYTICS_ON_DEMAND_PRIORITY_PROJECT_ID                 = var.is_fedramp ? "" : "xdr-data-research-slots-aod-01"
        UnifiedAssetInventoryConf_singlestore_partitions_per_leaf                 = var.viso_env == "dev" ? 8 : 32
        UnifiedAssetInventoryConf_singlestore_host                                = local.enable_cortex_platform ? "svc-dp-singlestore-cluster-ddl" : null # should be removed.. now it breaks old platform code
        UnifiedAssetInventoryConf_singlestore_host_ddl                            = local.enable_cortex_platform ? "svc-dp-singlestore-cluster-ddl" : null
        UnifiedAssetInventoryConf_singlestore_host_dml                            = local.enable_cortex_platform ? "svc-dp-singlestore-cluster-dml" : null
        UnifiedAssetInventoryConf_singlestore_port                                = local.enable_cortex_platform ? "3306" : null
        UnifiedAssetInventoryConf_singlestore_user                                = local.enable_cortex_platform ? "admin" : null
        UnifiedAssetInventoryConf_singlestore_db                                  = local.enable_cortex_platform ? "main_${var.lcaas}" : null
        UNIFIEDAGENTCONF_VC_SCAN_PUBSUB_DEBUG_SUBSCRIPTION_NAME                   = lookup(var.subscriptions_output, "vulnerability_and_compliance_scans_sub_debug")
        UNIFIEDAGENTCONF_VC_SCAN_PUBSUB_SUBSCRIPTION_NAME                         = lookup(var.subscriptions_output, "vulnerability_and_compliance_scans_sub")
        UNIFIEDAGENTCONF_VC_SCAN_PUBSUB_TOPIC                                     = lookup(var.topics_output, "vulnerability_and_compliance_scans")
        UNIFIEDAGENTCONF_VC_SCAN_UPLOAD_BUCKET                                    = lookup(var.buckets_output, "vulnerability_and_compliance_scans")
        UNIFIEDINVENTORYCONF_ASSET_TAGS_ENABLED                                   = var.is_xpanse ? "True" : null
        UNIFIEDINVENTORYCONF_IS_USING_CACHED_DATA                                 = (var.pro_agents > 10000) || (var.tb_licenses > 50) || (var.enable_asm) ? "True" : "False"
        UNIFIEDASSETINVENTORYCONF_ASSET_EXPORT_BUCKET                             = lookup(var.buckets_output, "dp_asset_export")
        ScortexConf_content_project_id                                            = var.global_bioc_project
        ScortexConf_content_bucket_name                                           = var.global_bioc_bucket
        ALERTSCONF_ALERTS_DATA_BUCKET_NAME                                        = "${var.project_id}-alerts-data"
        XPANSECONF_API_KEY_ID                                                     = var.enable_asm ? "1000000" : null
        XpanseClientConf_api_url                                                  = var.enable_asm ? (var.viso_env == "dev" ? "https://expander-viso.dev.q-internal.tech" : "https://expander.expanse.co") : null
        XpanseClientConf_request_token_url                                        = var.enable_asm ? (var.viso_env == "dev" ? "https://apitest.paloaltonetworks.com/api/oauth2/RequestToken" : "https://api.paloaltonetworks.com/api/oauth2/RequestToken") : null
        XpanseConf_enable_tags_replication                                        = var.is_xpanse ? "True" : "False"
        XpanseConf_enable_websites_replication                                    = var.enable_asm ? "True" : "False"
        XpanseConf_enable_xpanse_dashboard_filtering                              = var.is_xpanse ? "True" : "False"
        XpanseAlertDatabaseSyncConf_is_enabled                                    = var.is_xpanse ? "True" : "False"
        XpanseIncidentDatabaseSyncConf_is_enabled                                 = var.is_xpanse ? "True" : "False"
        XpanseCaseAlertDataSyncConf_is_enabled                                    = var.is_xpanse ? "True" : "False"
        XpanseConf_in_fedramp_ev2_migration_period                                = "False"
        XpanseConf_enable_replication                                             = var.enable_asm
        XpanseConf_xpanse_security_rating_dashboard_type                          = var.is_xpanse ? var.is_fedramp ? "no-benchmark-scores" : "has-benchmark-scores" : null
        UnifiedInventoryConf_use_id_to_tags_cache_table_for_services_and_websites = var.is_xpanse
        NotificationBanner_global_banner_bucket_name                              = var.viso_env == "dev" ? "xdr-banners-dev" : var.is_fedramp ? "xdr-banners-${var.viso_env}" : "xdr-banners-prod"
        NotificationBanner_global_banner_project_id                               = var.is_fedramp ? "xdr-mag-shared-${var.viso_env}-01" : "xdr-zeus"
        XpanseThreatEventsConf_bq_sync_enabled                                    = var.is_xpanse || var.is_xsiam ? "True" : "False"
        XpanseThreatEventsConf_global_storage_project_id                          = var.certs_project
        XpanseThreatEventsConf_global_storage_bucket_name                         = local.xpanse_multi_tenants_bucket_name
        XpanseComplianceFrameworksConf_global_storage_project_id                  = var.certs_project
        XpanseComplianceFrameworksConf_global_storage_bucket_name                 = local.xpanse_multi_tenants_bucket_name
        XpanseTechnologyMetadataConf_global_storage_project_id                    = var.certs_project
        XpanseTechnologyMetadataConf_global_storage_bucket_name                   = local.xpanse_multi_tenants_bucket_name
        XpansePolicyManagerConf_api_url                                           = var.enable_asm ? (var.viso_env == "dev" ? "https://expander-viso.dev.q-internal.tech/api/v1" : "https://expander.expanse.co/api/v1") : null
        XpansePolicyManagerConf_request_token_url                                 = var.enable_asm ? (var.viso_env == "dev" ? "https://apitest.paloaltonetworks.com/api/oauth2/RequestToken" : "https://api.paloaltonetworks.com/api/oauth2/RequestToken") : null
        #CRTX-105752
        VULNERABILITYTESTINGCONF_IS_ENABLED                     = "False"
        VULNERABILITYTESTINGCONF_IS_DISABLED                    = "False"
        XPANSEVULNERABILITYTESTSCONF_GLOBAL_STORAGE_BUCKET_NAME = var.viso_env == "prod-fr" ? "xpanse-policies-prod-fr" : var.viso_env == "prod-gv" ? "xpanse-policies-prod-gv" : var.viso_env == "dev" ? "xpanse-policies-dev" : "xpanse-policies-prod"
        XPANSEVULNERABILITYTESTSYNCCONFIG_MT_BUCKET_NAME        = var.viso_env == "prod-gv" ? "vulnerability-tests-gv-prod" : var.viso_env == "prod-fr" ? "vulnerability-tests-prod" : var.viso_env == "dev" ? "vulnerability-tests-dev" : "vulnerability-tests-prod"
        XPANSECONF_ENABLE_XPANSE_USER_DEFINED_IP_RANGES         = var.is_xpanse ? "True" : "False"
        REPORTSCONF_MAX_WIDGET_OVER_TIME_RETENTION_DAYS         = var.is_xpanse ? 90 : 30
        ASMPublicApiServiceConf_internet_exposure_limit         = var.is_xpanse ? "5000" : "500"
        GCPConf_mth_queries_charged_project                     = var.viso_env == "dev" ? "" : var.is_fedramp ? "" : "xdr-mth-slots-prod-us-01"
        GCPConf_mdr_queries_charged_project                     = var.viso_env == "dev" ? "" : var.is_fedramp ? "" : "xdr-mth-slots-prod-us-01"
        DASHBOARDENGINECONF_OUTPUT_DATASET                      = "dashboard_engine_${var.lcaas}"
        ALPHAFEATURES_USE_PENDO                                 = var.is_fedramp ? "False" : "True"
        # Metrics Aggregator
        HPL_GCP_DATASET                       = lookup(var.bq_output, "ds")
        HPL_XQL_METRICS_REGISTRY_PUBSUB_TOPIC = lookup(var.topics_output, "metrics_aggregator_topic")
        #CRTX-80280 Xapsne feature flag
        APILOGSCONF_LOG_REQUEST_BODY          = var.is_xpanse ? (var.viso_env == "dev" || var.viso_env == "prod-us" ? "True" : "False") : "False"
        APILOGSCONF_LOG_USERNAME_WITHOUT_HASH = var.is_xpanse ? (var.viso_env == "dev" || var.viso_env == "prod-us" ? "True" : "False") : "False"
        #CRTX-80728 api monitoring BQ
        APILOGSCONF_BQ_PROJECT                                        = "xdr-bq-mt-stats-${var.viso_env}-01"
        APILOGSCONF_ENABLED                                           = (var.is_xpanse || var.is_xsiam || var.is_xsoar) ? "True" : "False"
        APPLICATIONHUB_BQ_PROJECT                                     = "xdr-app-hub-slots-${var.viso_env}-01"
        APPLICATIONHUB_BQ_VIEWS_DATASET                               = lookup(var.bq_output, "public_access_views", "")
        APPLICATIONHUB_BQ_USER_DATASET                                = lookup(var.bq_output, "public_access_user", "")
        APPLICATIONHUB_ENGINE_PROJECT_EXTERNAL_IPS                    = join(",", local.shared_engines_public_ips)
        XpanseIncidentsConf_reopen_incidents_with_open_alerts_enabled = var.is_xpanse
        XpanseConf_enable_xpanse_ipv6                                 = var.is_xpanse ? "True" : "False"
        # CRTX-94914 set true for all xpanse tenants
        #CRTX-94371 move rcs configs from env vars to CM
        XpanseConf_enable_xpanse_remediation_confirmation_scanning         = var.enable_asm && !var.is_fedramp ? "True" : "False"
        XpanseRemediationScanningConf_mt_pubsub_project_id                 = var.viso_env == "dev" ? "xp-gcp-h-dev-s-dev" : var.viso_env == "prod-gv" ? "xpanse-aw-prod-svc-asm" : "xp-gcp-h-prod-s-prod"
        XpanseRemediationScanningConf_topic_name                           = var.viso_env == "dev" ? "manual-scan-requests-dev" : var.viso_env == "prod-gv" ? "manual-scan-requests-gv-prod" : "manual-scan-requests-prod"
        XpanseRcsResultProcessorConf_GCS_SCAN_RESULTS_BUCKET_NAME          = lookup(var.buckets_output, "xpanse_manual_scan_results_bucket", "")
        XpanseRcsResultProcessorConf_PUBSUB_SCAN_RESULTS_SUBSCRIPTION_NAME = lookup(var.subscriptions_output, "xpanse_manual_scan_results_sub", "")
        ExportConf_async_exports_gcs_bucket_name                           = lookup(var.buckets_output, "async_export_files_bucket", "")
        #CRTX-98703
        XPANSEINCIDENTSCONTEXTCONFIG_SYNC_ENABLED  = var.is_xpanse ? "True" : "False"
        INCIDENTSCONF_PARALLEL_INCIDENT_MANAGEMENT = var.is_xpanse ? "True" : "False"
        # CRTX-96911 enable migration-banners buckets
        XSOARMIGRATIONCONF_GLOBAL_STORAGE_CUSTOM_NOTIFICATION_BUCKET_NAME = var.is_xsoar ? (var.viso_env == "dev" ? "xsoar-migration-banners-dev" : "xsoar-migration-banners-prod") : null
        AssetManagementConf_pubsub_assets_change_topic_name               = "ipl-asset-changes-${var.lcaas}"
        AssetManagementConf_pubsub_assets_association_subscription_name   = "ipl-asset-changes-${var.lcaas}-sub"
        AssetManagementConf_asset_management_v2_bucket_name               = "${var.project_id}-asset-inventory"
        AssetManagementConf_bq_asset_management_dataset                   = "asset_inventory_${var.lcaas}"
        AssetManagementConf_bq_asset_management_inventory_dataset         = "asset_inventory_${var.lcaas}"
        AssetManagementConf_redis_host                                    = "xdr-st-${var.lcaas}-ipl-asset-redis"
        AssetManagementConf_redlock_host                                  = "xdr-st-${var.lcaas}-ipl-asset-redis"
        SUPPORTCASECONF_CASE_URL                                          = local.commercial_env ? "https://sso.paloaltonetworks.com/app/panw-ciam_sfdcprodcasecommunities_1/exk3jjv9xQhNTsmhF0j6/sso/saml?RelayState=https%3A%2F%2Fsupportcases.paloaltonetworks.com/s/casedetail?Id={id}" : null
        RETENTIONCONF_EGRESS_FORWARDING_HTTP_PROXY                        = "************:${local.egress_proxy_port}"
        RETENTIONCONF_EGRESS_FORWARDING_SUBSCRIPTION_NAME                 = var.egress_enabled ? "event-forwarding-external-${var.lcaas}-sub" : null
        FIRESTOREACCESSSERVICE_EGRESS_FIRESTORE_PROJECT                   = var.viso_env == "dev" ? "xdr-egress-firestore-dev-01" : var.is_fedramp ? "xdr-egress-fas-${var.viso_env}-01" : "xdr-egress-fas-prod-us-01"
        MITREMAPPINGSCONF_ENABLE_ASM_ALERT_MITRE_MAPPINGS                 = var.is_xpanse ? "True" : "False"
        #CRTX-111870
        THIRDPARTYCOLLECTIONKMS_PROJECT_ID = "xdr-kms-project-${var.multi_project_postfix}-01"
        THIRDPARTYCOLLECTIONKMS_LOCATION   = var.encrypt_fas_keyring_location
        THIRDPARTYCOLLECTIONKMS_KEY_RING   = "collection-security"
        THIRDPARTYCOLLECTIONKMS_KEY_NAME   = var.lcaas
        XSOARCONF_IS_REDIS_XSOAR_ENABLED   = var.is_xsiam ? "true" : null
        XSOARCONF_REDIS_PORT               = var.is_xsiam ? "6379" : null
        XSOARCONF_REDIS_HOST               = var.is_xsiam ? "xdr-st-${var.lcaas}-xsoar-redis" : null
        #CRTX-124604 & CRTX-126837
        ISSUESFETCHERCONF_CREATION_TOPIC                = lookup(var.topics_output, "ap_issue_create")
        ISSUESINGESTERCONF_INGESTER_TOPIC               = lookup(var.topics_output, "ap_issue_upsert")
        ISSUESCONF_DEAD_LETTER_TOPIC                    = lookup(var.topics_output, "ap_issue_ingestion_errors")
        ISSUESCONF_DEAD_LETTER_SUBSCRIPTION             = lookup(var.subscriptions_output, "ap_issue_ingestion_errors_sub")
        ISSUESINGESTERCONF_INGESTER_SUBSCRIPTION        = lookup(var.subscriptions_output, "ap_issue_upsert_sub")
        ISSUESFETCHERCONF_CREATION_SUBSCRIPTION         = lookup(var.subscriptions_output, "ap_issue_create_sub")
        ISSUEUPDATECONF_ISSUE_UPDATES_TOPIC_NAME        = lookup(var.topics_output, "ap_issue_update")
        ISSUEUPDATECONF_ISSUE_UPDATES_SUBSCRIPTION_NAME = lookup(var.subscriptions_output, "ap_issue_update_sub")
        #CRTX-131355
        CUSTOMERASMMANAGEMENTCONF_MT_BUCKET_NAME = local.asm_management_mt_bucket_name
        #CRTX-132986
        VipCveConf_global_storage_bucket_name = local.vip_cve_conf_bucket_name
        GENERIC_TENANT_DEPLOYMENT_BATCH                = var.is_fedramp ? null : var.viso_env == "dev" ? "P1" : var.upgrade_phase
        #CRTX-163679
        VIPVULNERABILITYCONF_VIP_PROJECT_ID            = var.viso_env == "dev" ? "xdr-vulnerability-intel-dev-01" : var.viso_env == "prod-fr" ? "xdr-vuln-intel-prod-fr-01" : var.viso_env == "prod-gv" ? "xdr-vuln-intel-prod-gv-01" : "xdr-vuln-intel-prod-us-01"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        CLOUDSEC_COMMON_POSTGRES_SVC              = "xdr-st-${var.lcaas}-postgres.${module.create_namespace.ns[local.st_namespace]}"
        CLOUDSEC_COMMON_POSTGRES_RULE_MGMT_SCHEMA = "rule_management_service"
        CLOUDSEC_COMMON_POSTGRES_USER             = "root"
        CLOUDSEC_RO_POSTGRES_USER                 = "postgres_ro_user"
        CLOUDSEC_RW_POSTGRES_USER                 = "postgres_rw_user"
        CLOUDSEC_COMMON_POSTGRES_PORT             = "5432"
        CLOUDSEC_COMMON_POSTGRES_POLICY_SCHEMA    = "cloudsec_policy_management"
        CLOUDSEC_COMMON_POSTGRES_CLOUD_API_DB     = "cloud_api_service"
        GENERIC_LCAAS_ID                          = var.lcaas
        GCPCONF_PROJECT_ID                        = var.project_id
        GCPCONF_REAL_REGION                       = var.region
        GCPCONF_REGION                            = var.bq_location
        GCPCONF_ENV_REGION                        = var.viso_env
        CORTEX_PLATFORM_URL                       = local.enable_cortex_platform ? "xdr-st-${var.lcaas}-platform:8000" : null
        GENERIC_EXTERNAL_FQDN                     = var.external_fqdn
        GENERIC_CORTEX_ID                         = var.xdr_id
        MYSQLCONF_HOST                            = local.tenant_endpoint_mysql
        OTEL_EXPORTER_OTLP_ENDPOINT               = "http://opentelemetry-collector.monitoring.svc.cluster.local:4318"
        MANAGEMENT_OTLP_METRICS_EXPORT_URL        = "http://opentelemetry-collector.monitoring.svc.cluster.local:4318/v1/metrics"
        OTEL_EXPORTER_OTLP_PROTOCOL               = "http/protobuf"
        BQSTATSCONF_ST_TOPIC_NAME                 = lookup(var.topics_output, "bq_stats_topic", "")
        PLATFORM_API_PORT                         = "8000"
        XDR_REDIS_HOST                            = "xdr-st-${var.lcaas}-redis.xdr-st.svc.cluster.local"
        XDR_REDIS_PORT                            = "6379"
      }
    },
    {
      name      = "${var.lcaas}-configmap-archive-aggregator"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = var.cold_retention
      data = {
        HPL_GCP_PROJECT_ID          = var.project_id
        HPL_LCAAS_ID                = var.lcaas
        HPL_REDIS_CONNECTION_STRING = local.gonzo_redis_connection_string
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-action-plan-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        GCP_PROJECT_ID          = var.project_id
        GENERIC_LCAAS_ID        = var.lcaas
        PCS_CLOUDSEC_DATASET_ID = "cloudsec_${var.lcaas}"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-action-plan-recon-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        JAVA_OPTS_APPEND        = "-Xss20M"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-action-plan-cleanup-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        JAVA_OPTS_APPEND        = "-Xss20M"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-dashboard-api-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        GCP_PROJECT_ID          = var.project_id,
        GENERIC_LCAAS_ID        = var.lcaas,
        PCS_CLOUDSEC_DATASET_ID = "cloudsec_${var.lcaas}",
        CLOUDSEC_CVM_DATASET    = "cloudsec",
        CLOUDSEC_CVM_MV_NAME    = "vulnerable_asset_summary_view"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-inline-scanner-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        GCP_PROJECT_ID                 = var.project_id
        ASSET_CHANGE_TOPIC_NAME        = "dp-uai-asset-change-feed-${var.lcaas}"
        ASSET_CHANGE_SUBSCRIPTION_NAME = "cloudsec-asset-change-sub-${var.lcaas}"
        SCANNER_DLQ_NAME               = "cloudsec-inline-scanner-dlq-${var.lcaas}"
        FINDINGS_TOPIC_NAME            = "verdict-manager-${var.lcaas}"
        RULES_DB_JDBC_URL              = "jdbc:postgresql://xdr-st-${var.lcaas}-postgres.xdr-st:5432/rule_management_service"
        RULES_DB_SCHEMA                = "public"
        RULES_DB_USER                  = "root"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-batch-scanner-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init                              = "init"
        GCPCONF_PROJECT_ID                = var.project_id
        CONFIG_SCANNER_ASSET_TABLE        = "aggregated_assets_view"
        CONFIG_SCANNER_GCS_BUCKET         = lookup(var.buckets_output, "verdict_manager")
        CONFIG_SCANNER_GCP_PROJECT_NUMBER = var.lcaas
        SPRING_PROFILES_ACTIVE            = "prod"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-rule-management"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        DB_SCHEMA                                 = "rule_management_service"
        DB_URL                                    = "xdr-st-${var.lcaas}-postgres.${module.create_namespace.ns[local.st_namespace]}"
        DB_USER                                   = "root"
        RULE_MANAGEMENT_MT_BUCKET_NAME            = local.rule_management_mt_bucket_name
        RULES_MIGRATION_WORKFLOW_PENDING_TIMEOUT  = "300"
        RULES_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT = "1800"
        RULES_MIGRATION_WORKFLOW_INTERVAL         = "60"
        EGRESSPROXY_URL                           = "************:${local.egress_proxy_port}"
        EGRESSPROXY_CA_PATH                       = "/etc/cert/egress.crt"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-xspm-rules-cron-job"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        DB_SCHEMA                                 = "rule_management_service"
        DB_URL                                    = "xdr-st-${var.lcaas}-postgres.${module.create_namespace.ns[local.st_namespace]}"
        DB_USER                                   = "root"
        XSPM_RULES_BUCKET_NAME                    = local.rule_management_mt_bucket_name
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-cloud-api-service-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        GCP_PROJECT_ID                                          = var.project_id
        CLOUDSEC_REMEDIATION_SUPPORTED_RULE_TYPES               = lookup(var.overrides, "cloudse_remediation_supported_rule_types", "config,attack_path,identity,network_reachability")
        COMPLIANCE_STANDARD_MIGRATION_WORKFLOW_PENDING_TIMEOUT  = "2100"
        COMPLIANCE_STANDARD_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT = "5400"
        COMPLIANCE_STANDARD_MIGRATION_WORKFLOW_INTERVAL         = "60"
        POLICIES_MIGRATION_WORKFLOW_PENDING_TIMEOUT             = "7800"
        POLICIES_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT            = "5400"
        POLICIES_MIGRATION_WORKFLOW_INTERVAL                    = "60"
        NOTIFICATIONS_MIGRATION_WORKFLOW_INTERVAL               = "60"
        NOTIFICATIONS_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT       = "3600"
        NOTIFICATIONS_MIGRATION_WORKFLOW_PENDING_TIMEOUT        = "13500"
        EGRESSPROXY_URL                                         = "************:${local.egress_proxy_port}"
        EGRESSPROXY_CA_PATH                                     = "/etc/cert/egress.crt"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-xspm-scanner-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        GCP_PROJECT_ID                            = var.project_id,
      }
    },
    {
      name      = "prometheus-server-conf"
      namespace = local.monitoring_namespace
      enabled   = !var.is_metro_tenant
      data = {
        "prometheus.yml" = templatefile(
          "${path.module}/files/prometheus/prom-config-map.yaml.tpl",
          {
            module_path                      = path.module
            lcaas_id                         = var.lcaas
            tenant_type                      = var.tenant_type
            xdr_id                           = var.pool_tenant_creation ? "pool-tenant" : var.xdr_id
            product_type                     = var.product_type
            product_tier                     = var.product_tier
            use_gmp                          = local.enable_gmp
            is_platform                      = local.enable_cortex_platform
            region                           = var.viso_env
            location                         = var.region
            namespace                        = "monitoring"
            cluster                          = "cluster-${var.lcaas}"
            gmp_project_id                   = local.gmp_monarch_project
            enable_victoria_metrics          = false
            # "metrics_debug_all" and "metrics_debug_selected" overrides are there for the backward capability
            metrics_whitelist_debug_all      = tobool(lower(lookup(var.overrides, "metrics_whitelist_debug_all", lookup(var.overrides, "metrics_debug_all", "true"))))
            metrics_whitelist_debug_selected = lookup(var.overrides, "metrics_whitelist_debug_selected", lookup(var.overrides, "metrics_debug_selected", "")) # metric1;metric2;metric3
            metrics_blacklist_debug_selected = lookup(var.overrides, "metrics_blacklist_debug_selected", "") # metric1;metric2;metric3
          }
        )
        "prometheus.rules" = file("${path.module}/files/prometheus/prometheus.rules")
      }
    },
    {
      name      = "${var.lcaas}-configmap-rocksdb-common"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        ANALYTICS_OAKENSHIELDDB_UPDATE_VIEW_AFTER_COMPACTION = anytrue([local.rocksdb_blue_green_mode,
          local.rocksdb_cluster_mode,
        local.rocksdb_cpu >= 7]) ? "true" : null
        ANALYTICS_OAKENSHIELDDB_REDIS_CONNECTION_STRING = local.rocksdb_blue_green_mode || local.rocksdb_cluster_mode ? "xdr-st-${var.lcaas}-analytics-redis:6379" : null
        ANALYTICS_OAKENSHIELDDB_BLUE_GREEN_MODE         = local.rocksdb_blue_green_mode ? "true" : null
        ANALYTICS_OAKENSHIELDDB_CLUSTER_MODE            = local.rocksdb_cluster_mode ? true : null
        ANALYTICS_OAKENSHIELDDB_COMPACT_AFTER_LOAD_DATA = anytrue([local.rocksdb_cluster_mode, local.rocksdb_cpu >= 7]) ? true : null
        ANALYTICS_OAKENSHIELDDB_COMPACT_ON_STARTUP      = anytrue([local.rocksdb_cluster_mode, local.rocksdb_cpu >= 7]) ? true : null
        ANALYTICS_OAKENSHIELDDB_BACKUP_BUCKET           = "${var.project_prefix}-${var.lcaas}-rocksdb-backups"
      }
    },
    {
      name      = "${var.lcaas}-configmap-rocksdb"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        ANALYTICS_OAKENSHIELDDB_ENV_NAME = local.rocksdb_blue_green_mode ? "green" : null
      }
    },
    {
      name      = "${var.lcaas}-configmap-rocksdb-writer"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        GONZO_LOGGER_TRANSPORT                 = "console:0,slack:2"
        GONZO_REDIS_CONNECTION_STRING          = local.gonzo_redis_connection_string
        ANALYTICS_OAKENSHIELDDB_MAX_CONCURRENT = local.rocksdb_blue_green_mode || local.rocksdb_cluster_mode ? null : "8"
        ANALYTICS_OAKENSHIELDDB_ENV_NAME       = local.rocksdb_blue_green_mode ? "blue" : null
        ANALYTICS_OAKENSHIELDDB_CPU            = local.rocksdb_blue_green_mode || local.rocksdb_cluster_mode ? local.rocksdb_cpu : local.rocksdb_writer_cpu
        ANALYTICS_OAKENSHIELDDB_MEMORY         = local.rocksdb_blue_green_mode || local.rocksdb_cluster_mode ? replace(local.rocksdb_memory, "Gi", "") : replace(local.rocksdb_writer_memory, "Gi", "")
        ANALYTICS_OAKENSHIELDDB_IS_MASTER      = local.rocksdb_cluster_mode ? true : null
      }
    },
    {
      name      = "monitoring-${var.lcaas}-prom-adapter"
      namespace = local.monitoring_namespace
      enabled   = !var.is_metro_tenant
      data = {
        "config.yaml" = file("${path.module}/files/prometheus/prom-adapter.rules")
      }
    },
    local.config_map_postgres,
    {
      name      = "${var.lcaas}-configmap-mongodb"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        "mongodb.conf" = <<-EOT
        net:
          bindIpAll: true
        storage:
          dbPath: /var/lib/mongodb
          engine: wiredTiger
          wiredTiger:
            engineConfig:
              cacheSizeGB: 4
        replication:
          replSetName: "mongo_rs1"
        security:
          authorization: enabled
          keyFile: /home/<USER>
        EOT
        "initiate.js"  = <<-EOT
        const os = require('os');
        const hostname = os.hostname();
        print('startup script');
        if (hostname.includes('mongodb-0')) {
            print('connecting to db');
            const username = process.env.MONGO_INITDB_ROOT_USERNAME;
            const password = process.env.MONGO_INITDB_ROOT_PASSWORD;
            db = db.getSiblingDB('admin');
            db.auth(username, password);

            let status;
            try {
                status = rs.status();
            } catch (error) {
                if (error.message.includes('no replset config has been received')) {
                    print('No replica set configuration received. Proceeding to initiate.');
                    status = { ok: 0 }; // Set status to indicate that initialization is needed
                } else {
                    print('Unexpected error occurred:', error.message);
                    quit(1);
                }
            }

            if (status && status.ok === 1) {
                print('Replica set is already initialized.');
                while (true) {
                    sleep(1000); // Sleep for 1 second before repeating
                }
            } else {
                print('Trying to initiate');
                var i = rs.initiate({
                    _id: 'mongo_rs1',
                    members: [
                       { _id: 0, host : "xdr-st-${var.lcaas}-cwp-mongodb-0.xdr-st-${var.lcaas}-cwp-mongodb.xdr-st.svc.cluster.local:27017" },
                    ]
                });
            }
        } else {
            print('Hostname does not contain "mongodb-0".');
            while (true) {
                sleep(1000); // Sleep for 1 second before repeating
            }
        }
        EOT
      }
    },
    {
      name      = "${var.lcaas}-configmap-cts"
      namespace = module.create_namespace.ns[local.cortex_cts_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        CTSCONF_AWS_ACCOUNT_ID        = var.xcloud_aws_account_id
        CTSCONF_AWS_REGION            = var.is_fedramp ? "us-gov-east-1" : "us-east-2"
        AWS_STS_REGIONAL_ENDPOINTS    = "regional"
        CTSCONF_CALLERS_LIST          = join(",", local.prisma_service_accounts)
        CTSCONF_PROXY_CA_PATH         = "/etc/cert/egress.crt"
        CTSCONF_PROXY_URL             = "************:${local.egress_proxy_port}"
        CTSCONF_REDIS_HOST            = "xdr-st-${var.lcaas}-redis-cts"
        CTSCONF_REDIS_PORT            = "6379"
        CTSCONF_SECRET_KMS_KEY_NAME   = var.lcaas
        GCPCONF_PROJECT_ID            = "${var.project_id}"
        CTSCONF_SECRET_KMS_KEY_RING   = "cts-security"
        LOGCONF_LOG_FILE_ENABLED      = "False"
        CTSCONF_SECRET_KMS_LOCATION   = var.kms_keyring_region
        CTSCONF_SECRET_KMS_PROJECT_ID = "xdr-kms-project-${var.multi_project_postfix}-01"
        LOGGINGSERVICE_LCAAS_TENANT   = var.lcaas
        MYSQLCONF_HOST                = "xdr-st-${var.lcaas}-mysql.xdr-st.svc.cluster.local"
        REDISCONF_ADDR                = "${var.redis_ip_future}.xdr-st.svc.cluster.local"
      }
    },
    {
      name      = "monitoring-${var.lcaas}-configmap-opentelemetry-collector"
      namespace = module.create_namespace.ns[local.monitoring_namespace]
      enabled   = local.enable_otel_collector
      data = {
        "relay.yaml" = <<-EOT
    exporters:
      debug:
        sampling_initial: 2
        sampling_thereafter: 10
        verbosity: basic
      prometheus:
        enable_open_metrics: true
        endpoint: 0.0.0.0:8889
        resource_to_telemetry_conversion:
          enabled: false
        send_timestamps: true
    extensions:
      health_check:
        endpoint: $${env:MY_POD_IP}:13133
    processors:
      batch: {}
      k8sattributes:
        extract:
          metadata:
          - k8s.namespace.name
          - k8s.deployment.name
          - k8s.statefulset.name
          - k8s.daemonset.name
          - k8s.cronjob.name
          - k8s.job.name
          - k8s.node.name
          - k8s.pod.name
          - k8s.pod.ip
          - k8s.pod.uid
          - k8s.container.name
        passthrough: false
        pod_association:
        - sources:
          - from: resource_attribute
            name: k8s.pod.ip
        - sources:
          - from: resource_attribute
            name: k8s.pod.uid
        - sources:
          - from: resource_attribute
            name: net.host.name
        - sources:
          - from: resource_attribute
            name: server.address
        - sources:
          - from: connection
      memory_limiter:
        check_interval: 5s
        limit_percentage: 80
        spike_limit_percentage: 25
      transform:
        metric_statements:
        # Define standardized metric attributes/labels
        - context: datapoint
          statements:
          - set(attributes["kubernetes_namespace"], resource.attributes["k8s.namespace.name"])
          - set(attributes["kubernetes_cronjob"], resource.attributes["k8s.cronjob.name"])
          - set(attributes["kubernetes_daemonset"], resource.attributes["k8s.daemonset.name"])
          - set(attributes["kubernetes_statefulset"], resource.attributes["k8s.statefulset.name"])
          - set(attributes["kubernetes_deployment"], resource.attributes["k8s.deployment.name"])
          - set(attributes["kubernetes_pod_name"], resource.attributes["k8s.pod.name"])
          - set(attributes["kubernetes_container"], resource.attributes["k8s.container.name"])
          - set(attributes["telemetry_sdk_name"], resource.attributes["telemetry.sdk.name"])
          - set(attributes["service_name"], resource.attributes["service.name"])
          # All OTEL metrics will have a "service_name" label
          - set(attributes["service_name"], "unknown") where attributes["service_name"] == nil
      filter/metrics:
        # Metric Allow list using regular expressions
        error_mode: ignore
        metrics:
          include:
            match_type: regexp
            metric_names:
              # OTEL Collector metrics
              - otelcol_.*
              # Prometheus scrape meta metrics
              - scrape_.*|up|target_info
              # temporary testing, allow all metrics
              - .+
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: $${env:MY_POD_IP}:4317
          http:
            endpoint: $${env:MY_POD_IP}:4318
      prometheus/self:
        config:
          scrape_configs:
          - job_name: opentelemetry-collector
            metric_relabel_configs:
            - action: labeldrop
              regex: service_instance_id
            relabel_configs:
            - action: replace
              replacement: 'monitoring'
              target_label: namespace
            - action: replace
              replacement: $${env:HOSTNAME}
              target_label: pod
            scrape_interval: 10s
            static_configs:
            - targets:
              - $${env:MY_POD_IP}:8888
    service:
      extensions:
      - health_check
      pipelines:
        logs:
          exporters:
          - debug
          processors:
          - memory_limiter
          - k8sattributes
          - batch
          receivers:
          - otlp
        metrics:
          exporters:
          - debug
          - prometheus
          processors:
          - memory_limiter
          - k8sattributes
          - transform
          - filter/metrics
          - batch
          receivers:
          - otlp
          - prometheus/self
        traces:
          exporters:
          - debug
          processors:
          - memory_limiter
          - k8sattributes
          - batch
          receivers:
          - otlp
      telemetry:
        # collector's logs must be in JSON format
        logs:
          encoding: json
        metrics:
          address: $${env:MY_POD_IP}:8888
        EOT
      }
    },
    {
      name      = "${var.lcaas}-configmap-cwp"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        ADS_CLEANING_CLEAN_IDLE_DURATION                        = "5s"
        ADS_CLEANING_CLEAN_SNAPSHOT_LOCK_DURATION               = "5m"
        ADS_CLEANING_CLEAN_UP_INTERVAL                          = "10s"
        ADS_CLEANING_CORTEX_PLATFORM_URL                        = "xdr-st-${var.lcaas}-platform:8000"
        ADS_CLEANING_CPU_PROFILE_DURATION                       = "1m"
        ADS_CLEANING_DISABLE_ROUTES_INSTRUMENTATION             = "FALSE"
        ADS_CLEANING_HOST                                       = "0.0.0.0"
        ADS_CLEANING_METRICS_PORT                               = "9091"
        ADS_CLEANING_PORT                                       = "8080"
        ADS_CLEANING_POSTGRES_DB_CONN_MAX_IDLE_TIME             = "15s"
        ADS_CLEANING_POSTGRES_DB_CONN_MAX_LIFETIME              = "30m"
        ADS_CLEANING_POSTGRES_DB_CONNECTION_STRING_SUFFIX       = "sslmode=disable"
        ADS_CLEANING_POSTGRES_DB_MAX_IDLE_CONNS                 = "1"
        ADS_CLEANING_POSTGRES_DB_MAX_OPEN_CONNS                 = "0"
        ADS_CLEANING_POSTGRES_DB_OWNER                          = "" #TODO
        ADS_CLEANING_POSTGRES_DB_SCHEMA                         = "public"
        ADS_CLEANING_POSTGRES_DB_UPDATE_SCHEMA                  = "TRUE"
        ADS_CLEANING_POSTGRES_DB_USER                           = "root"
        ADS_CLEANING_SERVICE_NAME                               = "cwp-ais-scanned-account-cleaning"
        ADS_CLEANING_SNAPSHOT_TASKS_COUNT                       = "20"
        ADS_CLEANING_SNAPSHOT_WORKER_COUNT                      = "10"
        ADS_CLEANING_SNAPSHOTS_LIMIT                            = "50"
        ADS_CONTROLLER_ASSUME_ROLE_ARN                          = lookup(local.ads_environment_variables.ADS_CONTROLLER_ASSUME_ROLE_ARN, var.viso_env, "")
        ADS_CONTROLLER_CLEANUP_GRACE_PERIOD                     = "24h"
        ADS_CONTROLLER_CPU_PROFILE_DURATION                     = "1m"
        ADS_CONTROLLER_DB_CREATE_BATCH_SIZE                     = "1000"
        ADS_CONTROLLER_DB_QUERY_TIMEOUT                         = "1m"
        ADS_CONTROLLER_DISABLE_ROUTES_INSTRUMENTATION           = "FALSE"
        ADS_CONTROLLER_FETCHER_INTERVAL                         = "1m"
        ADS_CONTROLLER_HOST                                     = "0.0.0.0"
        ADS_CONTROLLER_KMS_KEY_ALIAS                            = "${var.viso_env}-pan-managed-encrypt-snapshots-customers-generic"
        ADS_CONTROLLER_MAX_CONCURRENT_WORK                      = "50"
        ADS_CONTROLLER_MAX_CONCURRENT_WORKERS                   = "10"
        ADS_CONTROLLER_METRICS_PORT                             = "9091"
        ADS_CONTROLLER_OTEL_SERVICE_NAME                        = "cwp-ads-account-controller"
        ADS_CONTROLLER_PORT                                     = "8080"
        ADS_CONTROLLER_POSTGRES_DB_CONN_MAX_IDLE_TIME           = "15s"
        ADS_CONTROLLER_POSTGRES_DB_CONN_MAX_LIFETIME            = "30m"
        ADS_CONTROLLER_POSTGRES_DB_CONNECTION_STRING_SUFFIX     = "sslmode=disable"
        ADS_CONTROLLER_POSTGRES_DB_MAX_IDLE_CONNS               = "1"
        ADS_CONTROLLER_POSTGRES_DB_MAX_OPEN_CONNS               = "0"
        ADS_CONTROLLER_POSTGRES_DB_OWNER                        = "" #TODO
        ADS_CONTROLLER_POSTGRES_DB_SCHEMA                       = "public"
        ADS_CONTROLLER_POSTGRES_DB_UPDATE_SCHEMA                = "TRUE"
        ADS_CONTROLLER_POSTGRES_DB_USER                         = "root"
        ADS_CONTROLLER_REGION_LOCK_DURATION                     = "30m"
        ADS_CONTROLLER_SERVICE_NAME                             = "cwp-ads-account-controller"
        ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT                   = lookup(local.ads_environment_variables.ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT, var.viso_env, "")
        ADS_DB_INIT_POSTGRES_DB_CONNECTION_STRING_SUFFIX        = "sslmode=disable"
        ADS_DB_INIT_POSTGRES_DB_OWNER                           = ""
        ADS_DB_INIT_POSTGRES_DB_SCHEMA                          = "public"
        ADS_DB_INIT_POSTGRES_DB_USER                            = "root"
        ADS_GROUPING_CPU_PROFILE_DURATION                       = "1m"
        ADS_GROUPING_DB_CONN_CONTEXT_TIMEOUT                    = "3m"
        ADS_GROUPING_DB_ERROR_INTERVAL                          = "5s"
        ADS_GROUPING_DB_QUERY_CONTEXT_TIMEOUT                   = "1m"
        ADS_GROUPING_DISABLE_ROUTES_INSTRUMENTATION             = "FALSE"
        ADS_GROUPING_GROUPS_THRESHOLD                           = "5000"
        ADS_GROUPING_HOST                                       = "0.0.0.0"
        ADS_GROUPING_INSTANCES_LIMIT                            = "10000"
        ADS_GROUPING_MAX_ATTACHED_SNAPSHOTS_AWS                 = "5"
        ADS_GROUPING_MAX_ATTACHED_SNAPSHOTS_AZURE               = "5"
        ADS_GROUPING_MAX_ATTACHED_SNAPSHOTS_GCP                 = "5"
        ADS_GROUPING_MAX_INSTANCE_AGE                           = "20m"
        ADS_GROUPING_METRICS_PORT                               = "9091"
        ADS_GROUPING_MIN_SNAPSHOT_COUNT                         = "5"
        ADS_GROUPING_PORT                                       = "8080"
        ADS_GROUPING_POSTGRES_DB_CONN_MAX_IDLE_TIME             = "15s"
        ADS_GROUPING_POSTGRES_DB_CONN_MAX_LIFETIME              = "30m"
        ADS_GROUPING_POSTGRES_DB_CONNECTION_STRING_SUFFIX       = "sslmode=disable"
        ADS_GROUPING_POSTGRES_DB_MAX_IDLE_CONNS                 = "1"
        ADS_GROUPING_POSTGRES_DB_MAX_OPEN_CONNS                 = "0"
        ADS_GROUPING_POSTGRES_DB_OWNER                          = "" #TODO
        ADS_GROUPING_POSTGRES_DB_SCHEMA                         = "public"
        ADS_GROUPING_POSTGRES_DB_UPDATE_SCHEMA                  = "TRUE"
        ADS_GROUPING_POSTGRES_DB_USER                           = "root"
        ADS_GROUPING_SERVICE_NAME                               = "cwp-ais-grouping"
        ADS_GROUPING_WORKER_INTERVAL                            = "15s"
        ADS_LAUNCH_DEAD_LINE_SECONDS                            = "10800"
        ADS_LAUNCH_DISABLE_ROUTES_INSTRUMENTATION               = "FALSE"
        ADS_LAUNCH_HOST                                         = "0.0.0.0"
        ADS_LAUNCH_IMAGE_IDS_PATH                               = ""
        ADS_LAUNCH_MAX_WORKLOAD_LIFE_TIME_SECONDS               = "21600"
        ADS_LAUNCH_METRICS_PORT                                 = "9091"
        ADS_LAUNCH_PORT                                         = "8080"
        ADS_LAUNCH_POSTGRES_DB_CONN_MAX_IDLE_TIME               = "15s"
        ADS_LAUNCH_POSTGRES_DB_CONN_MAX_LIFETIME                = "30m"
        ADS_LAUNCH_POSTGRES_DB_CONNECTION_STRING_SUFFIX         = "sslmode=disable"
        ADS_LAUNCH_POSTGRES_DB_MAX_IDLE_CONNS                   = "1"
        ADS_LAUNCH_POSTGRES_DB_MAX_OPEN_CONNS                   = "0"
        ADS_LAUNCH_POSTGRES_DB_OWNER                            = "" #TODO
        ADS_LAUNCH_POSTGRES_DB_SCHEMA                           = "public"
        ADS_LAUNCH_POSTGRES_DB_UPDATE_SCHEMA                    = "TRUE"
        ADS_LAUNCH_POSTGRES_DB_USER                             = "root"
        ADS_LAUNCH_SCANNER_BINARY_PAYLOAD_ID                    = "ads-scanner_amd64.deb"
        ADS_LAUNCH_WORKLOAD_ORCHESTRATOR_ADDRESS                = "xdr-st-${var.lcaas}-cwp-sp-workload-orchestration-svc"
        ADS_DEPENDENCY_COLLECTOR_INTERVAL_MINUTES               = "10"
        ADS_DEPENDENCY_COLLECTOR_RULES_PAGE_SIZE                = "50"
        ADS_OBSERVER_DISABLE_ROUTES_INSTRUMENTATION             = "FALSE"
        ADS_OBSERVER_HOST                                       = "0.0.0.0"
        ADS_OBSERVER_METRICS_PORT                               = "9091"
        ADS_OBSERVER_PORT                                       = "8080"
        ADS_OBSERVER_POSTGRES_DB_CONN_MAX_IDLE_TIME             = "15s"
        ADS_OBSERVER_POSTGRES_DB_CONN_MAX_LIFETIME              = "30m"
        ADS_OBSERVER_POSTGRES_DB_CONNECTION_STRING_SUFFIX       = "sslmode=disable"
        ADS_OBSERVER_POSTGRES_DB_MAX_IDLE_CONNS                 = "1"
        ADS_OBSERVER_POSTGRES_DB_MAX_OPEN_CONNS                 = "0"
        ADS_OBSERVER_POSTGRES_DB_OWNER                          = "" #TODO
        ADS_OBSERVER_POSTGRES_DB_SCHEMA                         = "public"
        ADS_OBSERVER_POSTGRES_DB_UPDATE_SCHEMA                  = "TRUE"
        ADS_OBSERVER_POSTGRES_DB_USER                           = "root"
        ADS_OBSERVER_PUBSUB_SUBSCRIPTION_ID                     = "cwp-sp-lifecycle-events-ads-result-sync-sub-${var.lcaas}"
        ADS_OBSERVER_SERVICE_NAME                               = "crtx-ads-scan-status-observer"
        ADS_POSTGRES_DB_HOST                                    = "xdr-st-${var.lcaas}-postgres"
        ADS_POSTGRES_DB_NAME                                    = "cwp_ads"
        ADS_POSTGRES_DB_PORT                                    = "5432"
        ADS_PRIORITIZATION_CPU_PROFILE_DURATION                 = "1m"
        ADS_PRIORITIZATION_DISABLE_ROUTES_INSTRUMENTATION       = "FALSE"
        ADS_PRIORITIZATION_HOST                                 = "0.0.0.0"
        ADS_PRIORITIZATION_METRICS_PORT                         = "9091"
        ADS_PRIORITIZATION_PORT                                 = "8080"
        ADS_PRIORITIZATION_POSTGRES_DB_CONN_MAX_IDLE_TIME       = "15s"
        ADS_PRIORITIZATION_POSTGRES_DB_CONN_MAX_LIFETIME        = "30m"
        ADS_PRIORITIZATION_POSTGRES_DB_CONNECTION_STRING_SUFFIX = "sslmode=disable"
        ADS_PRIORITIZATION_POSTGRES_DB_MAX_IDLE_CONNS           = "1"
        ADS_PRIORITIZATION_POSTGRES_DB_MAX_OPEN_CONNS           = "0"
        ADS_PRIORITIZATION_POSTGRES_DB_OWNER                    = "" #TODO
        ADS_PRIORITIZATION_POSTGRES_DB_SCHEMA                   = "public"
        ADS_PRIORITIZATION_POSTGRES_DB_UPDATE_SCHEMA            = "TRUE"
        ADS_PRIORITIZATION_POSTGRES_DB_USER                     = "root"
        ADS_API_URL                                             = "http://xdr-st-${var.lcaas}-cwp-ads-api-svc:8080"
        ADS_API_POSTGRES_DB_OWNER                               = "" #TODO
        ADS_API_POSTGRES_DB_USER                                = "root"
        ADS_SNAPSHOT_COMPLETION_BACKOFF                         = "2m"
        ADS_SNAPSHOT_COMPLETION_TIMOUT                          = "30m"
        ADS_SNAPSHOT_COPY_BACKOFF                               = "30m"
        ADS_SNAPSHOT_CORTEX_ACCOUNT_ID                          = "" #TODO
        ADS_SNAPSHOT_CORTEX_KMS_KEY_ALIAS                       = "${var.viso_env}-pan-managed-encrypt-snapshots-customers-generic"
        ADS_SNAPSHOT_CORTEX_PLATFORM_URL                        = "xdr-st-${var.lcaas}-platform:8000"
        ADS_SNAPSHOT_CPU_PROFILE_DURATION                       = "1m"
        ADS_SNAPSHOT_CREATE_TIMEOUT                             = "30m"
        ADS_SNAPSHOT_DB_CREATE_BATCH_SIZE                       = "1000"
        ADS_SNAPSHOT_DB_QUERY_TIMEOUT                           = "1m"
        ADS_SNAPSHOT_DB_TIMEOUT                                 = "10s"
        ADS_SNAPSHOT_DISABLE_ROUTES_INSTRUMENTATION             = "FALSE"
        ADS_SNAPSHOT_HOST                                       = "0.0.0.0"
        ADS_SNAPSHOT_INSTANCE_LOCK_DURATION                     = "30m"
        ADS_SNAPSHOT_MAX_CONCURRENT_WORK                        = "100"
        ADS_SNAPSHOT_MAX_CONCURRENT_WORKERS                     = "20"
        ADS_SNAPSHOT_METRICS_PORT                               = "9091"
        ADS_SNAPSHOT_PORT                                       = "8080"
        ADS_SNAPSHOT_POSTGRES_DB_CONN_MAX_IDLE_TIME             = "15s"
        ADS_SNAPSHOT_POSTGRES_DB_CONN_MAX_LIFETIME              = "30m"
        ADS_SNAPSHOT_POSTGRES_DB_CONNECTION_STRING_SUFFIX       = "sslmode=disable"
        ADS_SNAPSHOT_POSTGRES_DB_MAX_IDLE_CONNS                 = "1"
        ADS_SNAPSHOT_POSTGRES_DB_MAX_OPEN_CONNS                 = "0"
        ADS_SNAPSHOT_POSTGRES_DB_OWNER                          = "" #TODO
        ADS_SNAPSHOT_POSTGRES_DB_SCHEMA                         = "public"
        ADS_SNAPSHOT_POSTGRES_DB_UPDATE_SCHEMA                  = "TRUE"
        ADS_SNAPSHOT_POSTGRES_DB_USER                           = "root"
        ADS_SNAPSHOT_PRODUCER_BACKOFF                           = "5s"
        ADS_SNAPSHOT_SERVICE_NAME                               = "cwp-ais-snapshot"
        ADS_SNAPSHOT_SNAPSHOT_COPY_WAIT_TIMEOUT                 = "2h"
        ADS_SNAPSHOT_SNAPSHOT_WAIT_TIMEOUT                      = "2h"
        ADS_SYNC_ACCOUNTS_PAGE_LIMIT                            = "100"
        ADS_SYNC_DISABLE_ROUTES_INSTRUMENTATION                 = "FALSE"
        ADS_SYNC_HOST                                           = "0.0.0.0"
        ADS_SYNC_ASSETS_INTERVAL                                = "10m"
        ADS_SYNC_CONNECTORS_INTERVAL                            = "1m"
        ADS_SYNC_SNAPSHOTS_INTERVAL                             = "2h"
        ADS_SYNC_MEMORY_BATCH_SIZE                              = "100"
        ADS_SYNC_METRICS_PORT                                   = "9091"
        ADS_SYNC_PORT                                           = "8080"
        ADS_SYNC_POSTGRES_DB_CONN_MAX_IDLE_TIME                 = "15s"
        ADS_SYNC_POSTGRES_DB_CONN_MAX_LIFETIME                  = "30m"
        ADS_SYNC_POSTGRES_DB_CONNECTION_STRING_SUFFIX           = "sslmode=disable"
        ADS_SYNC_POSTGRES_DB_CREATE_BATCH_SIZE                  = 1000
        ADS_SYNC_POSTGRES_DB_MAX_IDLE_CONNS                     = "1"
        ADS_SYNC_POSTGRES_DB_MAX_OPEN_CONNS                     = "0"
        ADS_SYNC_POSTGRES_DB_OWNER                              = "" #TODO
        ADS_SYNC_POSTGRES_DB_SCHEMA                             = "public"
        ADS_SYNC_POSTGRES_DB_UPDATE_SCHEMA                      = "TRUE"
        ADS_SYNC_POSTGRES_DB_USER                               = "root"
        ADS_SYNC_SERVICE_NAME                                   = "crtx-ads-platform-sync"
        ADS_SYNC_OLD_SNAPSHOTS_MIN_AGE                          = "10h"
        ADS_SYNC_DANGLING_SNAPSHOTS_MIN_AGE                     = "1h"
        AGENT_ANALYTICS_PROJECT_ID                              = "xdr-agent-analytics-${var.viso_env}-01"
        APISEC_ANALYTICS_DATASET                                = "apisec_analytics_${var.lcaas}"
        APISEC_ASSET_INGESTION_ERRORS_SUB                       = "apisec-dp-uai-asset-ingestion-errors-sub-${var.lcaas}"
        APISEC_ASSET_UPDATES_ASSET_MANAGER_SUB                  = lookup(var.subscriptions_output, "apisec_asset_updates_sub", "")
        APISEC_DP_BUS_ENRICHER_SUB                              = "apisec-dp-bus-enricher-sub-${var.lcaas}"
        APISEC_GROUPED_ASSET_MANAGER_SUB                        = "apisec-grouped-asset-manager-sub-${var.lcaas}"
        APISEC_GROUPED_INSEPCTION_SUB                           = "apisec-grouped-inspection-sub-${var.lcaas}"
        APISEC_GROUPED_RISK_SUB                                 = "apisec-grouped-risk-sub-${var.lcaas}"
        APISEC_GROUPED_TRANSACTIONS_BQ_TOPIC                    = "apisec-grouped-transactions-analytics-bq-${var.lcaas}"
        APISEC_GROUPED_TRANSACTIONS_TOPIC                       = "apisec-grouped-transactions-${var.lcaas}"
        APISEC_RISK_FINDINGS_ERRORS_SUBSCRIPTION                = "dp-finding-ingestion-errors-risk-sub-${var.lcaas}"
        APISEC_RISK_HEALTH_PORT                                 = "8080"
        APISEC_RISK_ISSUES_ERRORS_SUBSCRIPTION                  = "ap-issue-ingestion-errors-risk-sub-${var.lcaas}"
        APISEC_RISK_METRICS_PORT                                = "9091"
        APISEC_RISK_MIN_LOG_LEVEL                               = "INFO"
        APISEC_TRANSACTION_GROUPING_SUB                         = "apisec-transactions-grouping-sub-${var.lcaas}"
        APISEC_TRANSACTION_TOPIC                                = "apisec-transactions-${var.lcaas}"
        APISEC_SPEC_SERVICE_URL                                 = "http://xdr-st-${var.lcaas}-apisec-spec-service-svc:8080"
        APISEC_SPEC_TOPIC                                       = "apisec-spec-${var.lcaas}"
        APISEC_SPEC_ASSET_MANAGER_SUB                           = "apisec-spec-asset-manager-sub-${var.lcaas}"
        APISEC_SPEC_RISK_ENGINE_SUB                             = "apisec-spec-risk-engine-sub-${var.lcaas}"
        APISEC_SPEC_SERVICE_MIN_LOG_LEVEL                       = "INFO"
        APISEC_SPEC_JOB_MIN_LOG_LEVEL                           = "INFO"
        APISEC_SPEC_GATE_MIN_LOG_LEVEL                          = "INFO"
        APISEC_TESTING_SCAN_RESULTS_BUCKET_NAME                 = lookup(var.buckets_output, "apisec_scan_results", null)
        APISEC_BFF_MIN_LOG_LEVEL                                = "INFO"
        BQSTATSCONF_ST_TOPIC_NAME                               = "bq-stats-${var.lcaas}"
        CAS_BILLING_API                                         = "billing-api.cas.svc.cluster.local"
        CONTENT_BUCKET                                          = var.viso_env == "dev" ? "global-apisec-content-dev" : "global-apisec-content"
        CONTENT_EXCEPTION_BUCKET                                = var.viso_env == "dev" ? "global-apisec-content-dev" : "global-apisec-content"
        CONTENT_EXCEPTION_LOCATION                              = "exception/${var.lcaas}"
        CONTENT_LOCATION                                        = "global"
        CONTEXT_COLLECTION                                      = "TRUE"
        CORE_ASSET_ANALYZER_REDIS_DB_NUMBER                     = 0
        CORE_ASSET_ANALYZER_REDIS_HOST                          = "xdr-st-${var.lcaas}-cwp-redis"
        CORE_ASSET_ANALYZER_REDIS_PORT                          = 6379
        CORTEX_PLATFORM_URL                                     = "xdr-st-${var.lcaas}-platform:8000"
        CORTEXCLI_BUCKET_NAME                                   = "panw-cortex-cli-${var.multi_project_postfix}"
        CWP_AWS_SHARED_RESOURCES_ACCOUNT_ID                     = lookup(local.cwp_environment_variables.CWP_AWS_SHARED_RESOURCES_ACCOUNT_ID, var.viso_env, "")
        CWP_RELEASES_CORTEXCLI_BUCKET_NAME                      = var.viso_env == "dev" ? "global-cli-scanner-binaries-dev" : "global-cli-scanner-binaries"
        CWP_REGISTRY_SCAN_POSTGRES_DB_HOST                      = "xdr-st-${var.lcaas}-postgres"
        CWP_REGISTRY_DISCOVERY_POSTGRES_DB_HOST                 = "xdr-st-${var.lcaas}-postgres"
        CWP_SERVERLESS_SCAN_POSTGRES_DB_HOST                    = "xdr-st-${var.lcaas}-postgres"
        CWP_REGISTRY_SCANNER_VERSION                            = local.registry_scanner_version # registry_scanner_version is a local variable defined in 03_upload_binary.tf file
        CWP_REGISTRY_SCAN_SPEC_MANAGER_URL                      = "http://xdr-st-${var.lcaas}-cwp-scan-spec-manager-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local:8080"
        CWP_SERVERLESS_SCAN_SPEC_MANAGER_URL                    = "http://xdr-st-${var.lcaas}-cwp-scan-spec-manager-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local:8080"
        CWP_SCAN_SPEC_MANAGER_POSTGRES_DB_HOST                  = "xdr-st-${var.lcaas}-postgres"
        CWP_SCAN_SPEC_MANAGER_CLOUD_ACCOUNTS_SUBSCRIPTION_NAME  = "cwp-scan-spec-manager-cloud-accounts-${var.lcaas}-sub"
        CWP_REDIS_HOST                                          = "xdr-st-${var.lcaas}-cwp-redis"
        CWP_REDIS_PORT                                          = 6379
        EDR_BUCKET                                              = lookup(var.buckets_output, "ipl-edr-data")
        EGRESSPROXY_CA_PATH                                     = "/etc/cert/egress.crt"
        EGRESSPROXY_URL                                         = "************:${local.egress_proxy_port}"
        FINDINGS_EMIT_TOPIC                                     = "dp-finding-emits-${var.lcaas}"
        FINDINGS_VIEW                                           = "public_platform_${var.lcaas}.findings_view"
        GCP_PROJECT_ID                                          = "${var.project_id}"
        GCPCONF_PROJECT_ID                                      = "${var.project_id}"
        GENERIC_PRODUCT_TYPE                                    = var.product_type
        GENERIC_PRODUCT_CODE                                    = var.product_code
        GENERIC_XDR_ENVIRONMENT                                 = var.xdr_env
        GEO_IP_BQ_TABLE_NAME                                    = "xdr-bq-mt-stats-${var.viso_env}-01.${local.geo_location_dataset}.maxmind"
        GONZO_PROJECT_ID                                        = var.project_id
        GROUPING_PUBSUB_INPUT_MAX_OUTSTANDING_BYTES             = "100000000"
        GROUPING_PUBSUB_INPUT_NUM_GOROUTINES                    = "200"
        IMAGE_ANALYZER_POSTGRES_DB_HOST                         = "xdr-st-${var.lcaas}-postgres"
        IMAGE_ANALYZER_POSTGRES_DB_PORT                         = 5432
        IMAGE_ANALYZER_POSTGRES_DB_USER                         = "root"
        IMAGE_ANALYZER_SERVICE_URL                              = "http://xdr-st-${var.lcaas}-cwp-image-analyzer-svc.xdr-st.svc.cluster.local:8080"
        IMAGE_ANALYZER_REDIS_PORT                               = 6379
        IMAGE_ANALYZER_REDIS_HOST                               = "xdr-st-${var.lcaas}-cwp-redis"
        IMAGE_ANALYZER_REDIS_DB_NUMBER                          = 0
        INPUT_TYPE                                              = "pubsub"
        ISSUE_UPSERT_TOPIC                                      = "ap-issue-upsert-${var.lcaas}"
        ISSUES_VIEW                                             = "public_platform_${var.lcaas}.issues_view"
        K8S_PUBSUB_INVENTORY_SUBSCRIPTION_NAME                  = "cwp-k8s-data-inventory-sub-${var.lcaas}"
        K8S_PUBSUB_INVENTORY_TOPIC_NAME                         = "cwp-k8s-data-${var.lcaas}"
        K8SCONNECTORCONF_API_URL                                = "http://xdr-st-${var.lcaas}-cwp-k8s-api:8080"
        K8SCONNECTORCONF_GCS_INVENTORY_BUCKET_NAME              = "${var.project_id}-cwp-k8s-data"
        K8SCONNECTORCONF_LOG_INGESTOR_TOPIC_NAME                = "cwp-k8s-connector-logs-${var.lcaas}"
        K8SCONNECTORCONF_MYSQL_DB_NAME                          = "${var.lcaas}_main"
        K8SCONNECTORCONF_MYSQL_URL                              = local.tenant_endpoint_mysql
        K8SCONNECTORCONF_POSTGRES_HOST                          = "xdr-st-${var.lcaas}-postgres"
        K8SCONNECTORCONF_PUBSUB_INVENTORY_TOPIC_NAME            = "cwp-k8s-data-${var.lcaas}"
        K8SCONNECTORCONF_RULES_NOTIFICATION_SUBSCRIPTION        = "cwp-policy-rules-notification-sub-${var.lcaas}"
        K8SCONNECTORCONF_GAR_PROJECT_ID                         = "${var.project_id}"
        K8SCONNECTORCONF_GAR_LOCATION                           = "${var.region}"
        K8SCONNECTORCONF_GAR_REPOSITORY                         = "agent-docker"
        COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_LOCATION          = var.kms_keyring_region
        COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_NAME              = "master"
        COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_RING              = var.kms_keyring_name
        COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_VERSION           = "1"
        COMPLIANCE_AGENT_RULES_API_KMS_MT_PROJECT_ID            = "xdr-distributions-${var.multi_project_postfix}-01"
        COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_LOCATION          = var.kms_keyring_region
        COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_NAME              = "slave"
        COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_RING              = var.kms_keyring_name
        COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_VERSION           = "1"
        AGENT_DOWNLOADS_BUCKET_NAME                             = "${var.project_id}-cwp-agent-downloads"
        CORTEX_AGENT_CH_BASE_URL                                = "https://${var.ch_fqdn}.traps.paloaltonetworks.com"
        KMSCONF_BROKER_VM_KEY_NAME                              = var.lcaas
        LOGGINGSERVICE_LCAAS_TENANT                             = var.lcaas
        MALWARE_ANALYZER_SERVICE_URL                            = "http://xdr-st-${var.lcaas}-cwp-malware-analyzer-svc.xdr-st.svc.cluster.local:8080"
        MALWARE_DETECTION_REDIS_DB_NUMBER                       = 0
        MALWARE_DETECTION_REDIS_HOST                            = "xdr-st-${var.lcaas}-cwp-redis"
        MALWARE_DETECTION_REDIS_PORT                            = 6379
        MESSAGE_BUS_MAX_OUTSTANDING_BYTES                       = "-1"
        MESSAGE_BUS_PREFETCH_COUNT                              = "1"
        MESSAGE_BUS_PROJECT_ID                                  = "${var.project_id}"
        MESSAGE_BUS_WORKER_POOL_SIZE                            = "1"
        MIN_LOG_LEVEL                                           = "info"
        MONGO_LOCAL_MODE                                        = "false"
        MONGODB_REPLICASSET_NODES                               = "mongodb://mongo-headless.cas.svc.cluster.local:27017"
        MYSQL_CWP_CRUD_USER_NAME                                = "cwp-crud-user"
        OUTPUT_TYPE                                             = "pubsub"
        OTEL_EXPORTER_OTLP_ENDPOINT                             = "opentelemetry-collector.monitoring.svc.cluster.local:4318"
        PLATFORM_BUS_BASE_URL                                   = "http://xdr-st-${var.lcaas}-api:4999"
        PLATFORM_BUS_COMPONENT_NAME                             = "apisec_enricher"
        PLATFORM_BUS_SOURCES                                    = "panw_apisec_raw,aws_apigw_raw"
        PLATFORM_HEALTHMONITORING_TOPIC                         = "cloud-health-monitoring-${var.lcaas}"
        PLATFORM_HEALTHMONITORING_STATUS_TOPIC                  = "cloud-health-monitoring-statuses-${var.lcaas}"
        PLATFORM_HEALTHMONITORING_OUTPOST_STATUS_TOPIC          = "cloud-onboarding-tasks-${var.lcaas}"
        SCAN_LOGS_ENABLED                                       = "true"
        SCAN_LOGS_PUB_SUB_SEND_TOPIC_NAME                       = "dp-scan-logs-${var.lcaas}"
        SCAN_LOGS_BUCKET_NAME                                   = "${var.lcaas}-dp-scan-logs"
        POLICY_RULES_DELETION_ISSUE_CLOSING_PUBSUB_SUB          = lookup(var.subscriptions_output, "cwp_issue_closing_sub", "")
        POLICY_RULES_DELETION_ISSUE_CLOSING_PUBSUB_TOPIC        = lookup(var.topics_output, "cwp_issue_closing_topic", "")
        PROJECT_ID                                              = "${var.project_id}"
        PUBSUB_INPUT_MAX_OUTSTANDING_BYTES                      = "500000000"
        PUBSUB_INPUT_NUM_GOROUTINES                             = "200"
        PUBSUB_MODE                                             = "true"
        RULES_MANAGEMENT_URL                                    = "http://xdr-st-${var.lcaas}-cwp-rules-management-svc:8080"
        SBOM_ANALYZER_POSTGRES_DB_HOST                          = "xdr-st-${var.lcaas}-postgres"
        SBOM_ANALYZER_POSTGRES_DB_PORT                          = "5432"
        SBOM_ANALYZER_POSTGRES_DB_USER                          = "root"
        SCAN_RESULTS_BUCKET                                     = local.scan_results_bucket
        SCAN_RESULTS_ENRICHER_REDIS_DB_NUMBER                   = 0
        SCAN_RESULTS_ENRICHER_REDIS_HOST                        = "xdr-st-${var.lcaas}-cwp-redis"
        SCAN_RESULTS_ENRICHER_REDIS_PORT                        = 6379
        SCAN_RESULTS_ENRICHER_URL                               = "http://xdr-st-${var.lcaas}-cwp-scan-results-enricher-svc.xdr-st.svc.cluster.local:8080"
        SCAN_RESULTS_PUBSUB_TOPIC                               = local.scan_results_pubsub_topic
        SECRET_ANALYZER_SERVICE_URL                             = "http://xdr-st-${var.lcaas}-cwp-secret-analyzer-svc.xdr-st.svc.cluster.local:8080"
        SERVERLESS_REPORTED_ISSUES_TOPIC                        = "agent-management-reported-issues-${var.lcaas}"
        SERVERLESS_AGENT_ENCRYPTION_KEY                         = var.agentconfig_encryption_key
        SERVERLESS_REDIS_DB_NUMBER                              = 0
        SERVERLESS_REDIS_HOST                                   = "xdr-st-${var.lcaas}-cwp-redis"
        SERVERLESS_REDIS_PORT                                   = 6379
        SERVERLESS_LAST_SEEN_UPDATE_TOPIC                       = "log-processor-${var.lcaas}"
        SP_BC_BUCKET                                            = "${var.project_id}-cwp-sp-bc-data"
        SP_BC_CONTROL_SUB                                       = "cwp-sp-bc-control-sub-${var.lcaas}"
        SP_BC_OBJECTS_SUB                                       = "cwp-sp-bc-objects-sub-${var.lcaas}"
        SP_BC_OBJECTS_TOPIC                                     = "cwp-sp-bc-objects-${var.lcaas}"
        SP_ACCOUNT_CONTROLLER_ASSUME_ROLE_ARN                   = lookup(local.ads_environment_variables.ADS_CONTROLLER_ASSUME_ROLE_ARN, var.viso_env, "")
        SP_ACCOUNT_CONTROLLER_ROUTES_INSTRUMENTATION            = "FALSE"
        SP_ACCOUNT_CONTROLLER_FETCH_INTERVAL                    = "5m"
        SP_ACCOUNT_CONTROLLER_HOST                              = "0.0.0.0"
        SP_ACCOUNT_CONTROLLER_KMS_KEY_ALIAS                     = "${var.viso_env}-pan-managed-encrypt-snapshots-customers-generic"
        SP_ACCOUNT_CONTROLLER_MAX_CONCURRENT_WORK               = "50"
        SP_ACCOUNT_CONTROLLER_MAX_CONCURRENT_WORKERS            = "10"
        SP_ACCOUNT_CONTROLLER_METRICS_PORT                      = "9091"
        SP_ACCOUNT_CONTROLLER_PORT                              = "8080"
        SP_ACCOUNT_CONTROLLER_SERVICE_NAME                      = "sp-account-controller"
        SP_ACCOUNT_CONTROLLER_TARGET_SERVICE_ACCOUNT            = lookup(local.ads_environment_variables.ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT, var.viso_env, "")
        SP_ACCOUNT_CONTROLLER_ACCOUNTS_PAGE_LIMIT               = "100"
        SERVICE_LCAAS_TENANT                                    = var.lcaas
        SP_WO_BC_RESULTS_TOPIC                                  = local.scan_results_pubsub_topic
        SP_WO_DISABLE_ROUTES_INSTRUMENTATION                    = "FALSE"
        SP_WO_HOST                                              = "0.0.0.0"
        SP_WO_LAUNCH_TASK_WORKERS                               = "20"
        SP_WO_LIFECYCLE_TOPIC                                   = "cwp-sp-lifecycle-events-${var.lcaas}"
        SP_WO_METRICS_PORT                                      = "9091"
        SP_WO_PAYLOAD_DISTRIBUTOR_API_ADDRESS                   = "http://xdr-st-${var.lcaas}-cwp-sp-bc-distributor-svc:8080"
        SP_WO_PORT                                              = "8080"
        SP_WO_POSTGRES_DB_CONN_MAX_IDLE_TIME                    = "15s"
        SP_WO_POSTGRES_DB_CONN_MAX_LIFETIME                     = "30m"
        SP_WO_POSTGRES_DB_CONNECTION_STRING_SUFFIX              = "sslmode=disable"
        SP_WO_POSTGRES_DB_HOST                                  = "xdr-st-${var.lcaas}-postgres"
        SP_WO_POSTGRES_DB_MAX_IDLE_CONNS                        = "1"
        SP_WO_POSTGRES_DB_MAX_OPEN_CONNS                        = "0"
        SP_WO_POSTGRES_DB_NAME                                  = "cwp_sp_wo"
        SP_WO_POSTGRES_DB_OWNER                                 = "" #TODO
        SP_WO_POSTGRES_DB_PORT                                  = "5432"
        SP_WO_POSTGRES_DB_SCHEMA                                = "public"
        SP_WO_POSTGRES_DB_UPDATE_SCHEMA                         = "TRUE"
        SP_WO_POSTGRES_DB_USER                                  = "root"
        SP_WO_RESULTS_BUCKET_NAME                               = "${var.project_id}-cwp-sp-bc-data"
        SP_WO_RESULTS_CONSUMERS_NUM                             = "1"
        SP_WO_RESULTS_SUBSCRIPTION_ID                           = "cwp-sp-bc-control-workload-orchestration-${var.lcaas}"
        SP_WO_RUNNER_PAYLOAD_ID                                 = "sp-scan-runner_amd64.deb"
        SP_BC_METRICS_INGESTOR_HOST                             = "0.0.0.0"
        SP_BC_METRICS_INGESTOR_PORT                             = "8080"
        SP_BC_METRICS_INGESTOR_METRICS_PORT                     = "9091"
        SP_BC_METRICS_INGESTOR_SUB                              = "cwp-sp-bc-control-metrics-${var.lcaas}-sub"
        SP_BC_METRICS_INGESTOR_TOPIC                            = "cwp-sp-bc-metrics-${var.lcaas}"
        SP_BC_METRICS_INGESTOR_NUMBER_OF_CONSUMERS              = "2"
        SP_BC_METRICS_INGESTOR_MAX_CONSUMED_MSG                 = "10"
        SP_BC_CONTROL_TOPIC                                     = "cwp-sp-bc-control-${var.lcaas}"
        SP_OUTPOST_ERROR_THRESHOLD                              = "24h"
        SP_SNAPSHOT_DB_HOST                                     = "xdr-st-${var.lcaas}-postgres"
        SP_SNAPSHOT_API_ADDRESS                                 = "http://xdr-st-${var.lcaas}-cwp-sp-snapshot-svc:8080"
        SP_SNAPSHOT_KMS_KEY_ALIAS                               = "${var.viso_env}-pan-managed-encrypt-snapshots-customers-generic"
        TELEMETRY_HANDLER_TYPE                                  = "pubsub"
        TELEMETRY_OUTPUT_TOPIC                                  = var.agent_analytics_topic_name
        TENANT_ID                                               = var.lcaas
        UAI_AGGREGATED_ASSETS_VIEW                              = "public_platform_${var.lcaas}.aggregated_assets_view"
        UAI_OBSERVATION_TOPIC                                   = "dp-uai-asset-observations-${var.lcaas}"
        VE_CVE_FEED_PATH                                        = var.viso_env == "dev" ? "gs://intelligence-builder-dev/feeds/33.02.116/CVEs.zip" : "gs://intelligence-builder-prod/feeds/33.01.141/CVEs.zip"
        VE_WINDOWS_FEED_PATH                                    = var.viso_env == "dev" ? "gs://intelligence-builder-dev/feeds/33.02.116/Windows.zip" : "gs://intelligence-builder-prod/feeds/33.01.141/Windows.zip"
        VULNERABILITY_ANALYZER_SERVICE_URL                      = "http://xdr-st-${var.lcaas}-cwp-vulnerability-analyzer-svc.xdr-st.svc.cluster.local:8080"
        VULNERABILITY_RE_ANALYZER_SERVICE_URL                   = "http://xdr-st-${var.lcaas}-cwp-vulnerability-re-analyzer-svc.xdr-st.svc.cluster.local:8080"
        VULNERABILITY_EVALUATOR_BASE_URL                        = "http://xdr-st-${var.lcaas}-uvem-vip-api:8080"
        UVM_EVALUATOR_URL                                       = "http://xdr-st-${var.lcaas}-uvem-vxp-api:8080"
        UVM_NETSCAN_PROCESSOR_URL                               = "http://xdr-st-${var.lcaas}-uvem-netscan-processor:8080"
        SBOM_ANALYZER_SERVICE_URL                               = "http://xdr-st-${var.lcaas}-cwp-sbom-analyzer-svc.xdr-st.svc.cluster.local:8080"
      }
    },
    {
      name      = "${var.lcaas}-configmap-ciem"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        LCAAS_ID                                  = var.lcaas
        CORTEX_PLATFORM_SERVICE_AUDIENCE          = "cortex.platform"
        CORTEX_PLATFORM_URI                       = "http://xdr-st-${var.lcaas}-platform:8000"
        BIGQUERY_TENANT                           = var.lcaas
        BUCKET_NAME                               = "${var.lcaas}-ext-logs"
        CIEM_API_URL                              = "http://xdr-st-${var.lcaas}-ciem-api-svc"
        CORTEX_PLATFORM_URL                       = "xdr-st-${var.lcaas}-platform:8000"
        CORTEX_PLATFORM_REST_HOST                 = "http://xdr-st-"
        CORTEX_PLATFORM_REST_CONNECT_TIMEOUT_SECS = "60"
        CORTEX_PLATFORM_REST_READ_TIMEOUT_SECS    = "60"
        CLOUDSEC_REST_HOST                        = "http://cloudsec-"
        CLOUDSEC_NAMESPACE                        = "cloudsec"
        CLOUDSEC_RULE_MANAGEMENT_SERVICE          = "rule-management"
        CLOUDSEC_CLOUD_API_SERVICE                = "cloud-api-service"
        ENVIRONMENT                               = var.viso_env
        VERSION                                   = local.enable_cortex_platform ? element(split(":", lookup(var.app_images, "ciem", "")), 1) : null
        NAMESPACE                                 = "xdr-st"
        GENERIC_PRODUCT_TYPE                      = var.product_type
        GENERIC_PRODUCT_CODE                      = var.product_code
        GENERIC_XDR_ENVIRONMENT                   = var.xdr_env
        ISSUES_IN_MESSAGE_BATCH_SIZE              = "20"
        ISSUE_TOPIC_BATCH_SIZE                    = "10"
        LOGGINGSERVICE_LCAAS_TENANT               = var.lcaas
        MAXIMUM_DB_CONNECTION_LIFETIME_MS         = "300000"
        MAXIMUM_DB_CONNECTION_POOL_SIZE           = "10"
        MIN_LOG_LEVEL                             = var.viso_env == "dev" && !var.is_metro_tenant ? "DEBUG" : "INFO"
        POSTGRES_HOST                             = "xdr-st-${var.lcaas}-postgres"
        POSTGRES_USER                             = "root"
        POSTGRES_PORT                             = "5432"
        POSTGRES_DB                               = "ciem"
        PROJECT_ID                                = "${var.project_id}"
        POLLING_TIME_OFFSET_MS                    = "600000"
        RULE_SCAN_ORCHESTRATOR_THREAD_POOL_SIZE   = "1"
        RULE_SCANNER_WORKER_TIMEOUT_MS            = "3600000"
        SCAN_ISSUE_TOPIC_PREFIX                   = "ap-issue-upsert"
        STATS_PROJECT_ID                          = "xdr-bq-mt-stats-${var.viso_env}-01"
        TENANT_ID                                 = var.lcaas
        init                                      = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        DIG_INFRA_PRODUCT                = "CORTEX"
        DIG_INFRA_CLOUD                  = "GCP"
        LCAAS_ID                         = var.lcaas
        CORTEX_PLATFORM_SERVICE_AUDIENCE = "cortex.platform"
        CORTEX_PLATFORM_URI              = "http://xdr-st-${var.lcaas}-platform:8000"
        CORTEX_PROJECT_ID                = var.project_id
        DSPM_DT_DEBUG                    = tobool(lower(lookup(var.overrides, "dspm_dt_debug", "false")))
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-verdict-manager-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        CLOUDSEC_VERDICT_MANAGER_BATCH_GCS_ENABLED          = "true"
        CLOUDSEC_VERDICT_MANAGER_BATCH_GCS_NOTIFICATION_SUB = "cloudsec-batch-verdicts-notification-${var.lcaas}-sub"
        CLOUDSEC_VERDICT_MANAGER_XSPM_GCS_NOTIFICATION_SUB = "cloudsec-xspm-verdicts-notification-${var.lcaas}-sub"
        GCP_PROJECT_ID                                      = var.project_id
        PCS_VERDICT_MANAGER_BOOKMARK_PERSISTENCE_BUCKET     = lookup(var.buckets_output, "verdict_manager")
        PCS_PLATFORM_FINDING_TOPIC                          = "projects/${var.project_id}/topics/dp-finding-emits-${var.lcaas}"
        PCS_PLATFORM_ISSUE_TOPIC                            = "projects/${var.project_id}/topics/ap-issue-upsert-${var.lcaas}"
        PCS_VERDICT_MANAGER_DATASET_ID                      = "cloudsec_${var.lcaas}"
        PCS_VERDICT_MANAGER_RECONCILER_JOB_INTERVAL         = "300000"
        RULES_POSTGRES_HOST                                 = "xdr-st-${var.lcaas}-postgres.${module.create_namespace.ns[local.st_namespace]}"
        JAVA_OPTS                                           = "-Xmx3072m -Xms3072m"
        PCS_PLATFORM_FINDING_BUCKET                         = "${var.project_prefix}-${var.lcaas}-dp-finding-emits"
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-attack-path-scanner-configmap"
      namespace = module.create_namespace.ns[local.cloudsec_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        GCP_PROJECT_ID                 = var.project_id
        PCS_COMMON__VERDICT_TOPIC_NAME = "projects/${var.project_id}/topics/verdict-manager-${var.lcaas}"
        PCS_ATTACK_PATH_DATASET_ID     = "public_platform_${var.lcaas}"
        PCS_VERDICT_MANAGER_DATASET_ID = "cloudsec_${var.lcaas}"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-x-files"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        REDIS_PORT                        = local.dspm_redis_port
        REDIS_HOST                        = local.dspm_redis_host
        X_FILES_BUCKET                    = lookup(var.buckets_output, "dspm_listing_data_bucket", null)
        DB_NAME                           = "dspm-x-files"
        TEMPORAL_QUEUE_NAME               = "x_files_queue"
        TEMPORAL_TLS                      = "false"
        TEMPORAL_NAMESPACE                = "dspm-x-files"
        TEMPORAL_URL                      = local.temporal_address
        OUTPOST_ORCHESTRATOR_INTERNAL_URL = "http://xdr-st-${var.lcaas}-dspm-oo-ready-job-evaluator-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-fda"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        FDA_INTERNAL_API_USERNAME                                     = "user"
        FDA_INTERNAL_API_PASSWORD                                     = "temp"
        FDA_ASSET_INVENTORY_LISTENER_QUEUE                            = lookup(var.subscriptions_output, "dspm_fda_asset_change_feed_sub", null)
        FDA_ASSET_ANALYSIS_LISTENER_QUEUE                             = lookup(var.subscriptions_output, "dspm_fda_asset_analysis_sub", null)
        FDA_FILES_LISTENER_QUEUE                                      = lookup(var.subscriptions_output, "dspm_fda_file_analysis_sub", null)
        FDA_ASSET_POST_PROCESSING_LISTENER_QUEUE                      = lookup(var.subscriptions_output, "dspm_fda_asset_post_processing_request_sub", null)
        FDA_SCHEDULER_ASSET_POST_PROCESSING_REQUEST_TOPIC             = lookup(var.topics_output, "dspm_asset_post_processing_request", null)
        FDA_REPLICATION_BIG_QUERY_DATASET_NAME                        = lookup(var.bq_output, "dspm")
        FDA_COLUMN_ANALYSIS_LISTENER_QUEUE                            = lookup(var.subscriptions_output, "dspm_fda_column_analysis_sub", null)
        SPRING_DATA_REDIS_HOST                                        = local.dspm_redis_host
        SPRING_DATA_REDIS_PORT                                        = local.dspm_redis_port
        SPRING_DATA_MONGODB_HOST                                      = "xdr-st-${var.lcaas}-cwp-mongodb"
        SPRING_DATA_MONGODB_PORT                                      = "27017"
        SPRING_DATA_MONGODB_DATABASE                                  = "dspm-fda"
        SPRING_DATA_MONGODB_USERNAME                                  = "root"
        SPRING_DATA_MONGODB_AUTHENTICATION_DATABASE                   = "admin"
        SPRING_TEMPORAL_NAMESPACE                                     = "dspm-fda"
        SPRING_TEMPORAL_CONNECTION_TARGET                             = local.temporal_address
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-cb"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        TEMPORAL_NAMESPACE           = "dspm-cb"
        TEMPORAL_URL                 = local.temporal_address
        QUEUE_ID                     = "projects/${var.project_id}/subscriptions/${lookup(var.subscriptions_output, "dspm_cb_listing_data_sub", "")}"
        COLUMN_LISTING_DATA_QUEUE_ID = "projects/${var.project_id}/subscriptions/${lookup(var.subscriptions_output, "dspm_cb_column_listing_data_sub", "")}"
        MALWARE_DETECTION_URL        = "http://xdr-st-${var.lcaas}-cwp-malware-detection-service-svc.cwp.svc.cluster.local:8080"
        ECHO_BRIDGE_URL              = "http://dummy:8888"
        ASSET_OUTPUT_TOPIC           = "projects/${var.project_id}/topics/${lookup(var.topics_output, "dspm_asset_analysis_topic", "")}"
        COLUMN_ANALYSIS_OUTPUT_TOPIC = "projects/${var.project_id}/topics/${lookup(var.topics_output, "dspm_column_analysis_topic", "")}"
        FILE_OUTPUT_TOPIC            = "projects/${var.project_id}/topics/${lookup(var.topics_output, "dspm_file_analysis_topic", "")}"
        FDA_INTERNAL_URL             = "http://${local.dspm_fda_app_name_prefix}-api-svc:${local.dspm_fda_port}"
        FDA_USER                     = "user"
        FDA_PASSWORD                 = "temp"
        X_FILES_SKINNER_INTERNAL_URL = "http://xdr-st-${var.lcaas}-dspm-x-files-skinner-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-mac"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        SPRING_TEMPORAL_NAMESPACE         = "dspm-mac"
        SPRING_TEMPORAL_CONNECTION_TARGET = local.temporal_address
        FINDING_BUCKET                    = lookup(var.buckets_output, "dp_finding_emits", null)
        ASSET_TOPIC                       = lookup(var.topics_output, "dp_uai_asset_observations_topic", null)
        DISCOVERY_SUBSCRIPTION      = lookup(var.subscriptions_output, "dspm_mac_asset_discovery_sub", null)
        DISCOVERY_DLQ_SUBSCRIPTION  = lookup(var.subscriptions_output, "dspm_mac_asset_discovery_dlq_sub", null)
        DISCOVERY_BUCKET            = lookup(var.buckets_output, "dspm_asset_discovery_bucket", null)
        MAC_MAIN_DATAFLOW_QUEUES_discovery = lookup(var.subscriptions_output, "dspm_discovery_data_sub", null)
        X_FILES_SKINNER_INTERNAL_URL                        = "http://xdr-st-${var.lcaas}-dspm-x-files-skinner-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local"
        MAC_MAIN_DATAFLOW_QUEUES_aispm-asset-change-queue   = "dspm-mac-aispm-asset-change-feed-${var.lcaas}-sub"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-ai-data-connector"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        SPRING_TEMPORAL_NAMESPACE                 = "dspm-ai-data-connector"
        SPRING_TEMPORAL_CONNECTION_TARGET         = local.temporal_address
        ADC_PERSISTENT_BIG_QUERY_DATASET_NAME     = lookup(var.bq_output, "aispm")
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-dpc"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        APP__TEMPORAL__NAMESPACE         = "dspm-dpc"
        APP__TEMPORAL__TARGET            = local.temporal_address
        APP__DACS__BASE_URL              = "http://xdr-st-${var.lcaas}-dspm-dacs-dashboard-svc.${module.create_namespace.ns[local.st_namespace]}.svc.cluster.local"
        APP__DACS_LISTENER__PROFILE_CHANGE_QUEUE_ID = "projects/${var.project_id}/subscriptions/${lookup(var.subscriptions_output, "dspm_dpc_profile_update_sub", "")}"
        APP__ETL__DATASET                = lookup(var.bq_output, "dspm")
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-dpc-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "cas-configmap"
      namespace = module.create_namespace.ns[local.cas_namespace]
      enabled   = local.enable_cortex_platform
      labels = {
        "workflows.argoproj.io/configmap-type" = "Parameter"
      }
      data = {
        CRTX_INTEGRATION_URL                         = var.crtx_integration_url
        GENERIC_EXTERNAL_FQDN                        = var.external_fqdn
        LCAAS_ID                                     = var.lcaas
        GCPCONF_REGION                               = var.bq_location
        GCPCONF_PROJECT_ID                           = var.project_id
        MIN_LOG_LEVEL                                = var.viso_env == "dev" && !var.is_metro_tenant ? "DEBUG" : "INFO"
        EGRESSPROXY_URL                              = lookup(var.overrides, "cas_egress_proxy_url", "************:${local.egress_proxy_port}")
        SKIP_SSL                                     = lookup(var.overrides, "cas_skip_ssl", false)
        EGRESSPROXY_CA_PATH                          = "/etc/cert/egress.crt"
        CORTEX_PLATFORM_URL                          = "xdr-st-${var.lcaas}-platform.xdr-st.svc.cluster.local:8000"
        CORTEX_FINDINGS_BUCKET_NAME                  = lookup(var.buckets_output, "dp_finding_emits")
        CORTEX_SCAN_LOGS_BUCKET_NAME                 = lookup(var.buckets_output, "dp_scan_logs", "")
        CORTEX_ISSUES_PERSIST_PUB_SUB_TOPIC_NAME     = "projects/${var.project_id}/topics/ap-issue-upsert-${var.lcaas}"
        CORTEX_ASSETS_OBSERVATION_PUB_SUB_TOPIC_NAME = "projects/${var.project_id}/topics/dp-uai-asset-observations-${var.lcaas}"
        SBOM_REPORTS_BUCKET                          = lookup(var.buckets_output, "sbom_reports")
        WORKFLOWS_ARTIFACTS_BUCKET                   = lookup(var.buckets_output, "argo_worklows_artifacts")
        WORKFLOWS_LOGS_BUCKET                        = lookup(var.buckets_output, "argo_worklows_logs")
        WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET     = lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null)
        SOURCE_CONTROL_BUCKET                        = lookup(var.buckets_output, "source_control")
        CI_BUILD_LOGS_BUCKET                         = lookup(var.buckets_output, "ci_build_logs")
        CAS_REQUESTS_PAYLOAD_BUCKET                  = lookup(var.buckets_output, "cas_requests_payload")
        IS_PROD                                      = strcontains(var.viso_env, "prod")
        ISSUES_INGESTION_ERRORS_TOPIC                = "projects/${var.project_id}/topics/ap-issue-ingestion-errors-${var.lcaas}"
        ISSUES_INGESTION_ERRORS_SUBSCRIPTION         = "projects/${var.project_id}/subscriptions/ap-issue-ingestion-errors-cas-${var.lcaas}-sub"
        FINDING_INGESTION_ERRORS_TOPIC               = "projects/${var.project_id}/topics/dp-finding-ingestion-errors-${var.lcaas}"
        FINDING_INGESTION_ERRORS_SUBSCRIPTION        = "projects/${var.project_id}/subscriptions/dp-finding-ingestion-errors-cas-${var.lcaas}-sub"
        ISSUE_INGESTION_FEEDBACK_TOPIC               = "projects/${var.project_id}/topics/ap-issue-ingest-feedback-${var.lcaas}"
        ISSUE_INGESTION_FEEDBACK_SUBSCRIPTION        = "projects/${var.project_id}/subscriptions/ap-issue-ingest-feedback-cas-${var.lcaas}-sub"
        TRIGGER_ARGO_WF_TOPIC                        = "projects/${var.project_id}/topics/cas-trigger-argo-wf-${var.lcaas}"
        TRIGGER_ARGO_WF_SARIF_SUBSCRIPTION           = "projects/${var.project_id}/subscriptions/cas-trigger-argo-wf-sarif-sub-${var.lcaas}"
        TRIGGER_ARGO_WF_JENKINS_INTEGRATION_STATE_SUBSCRIPTION      = "projects/${var.project_id}/subscriptions/cas-jenkins-state-trigger-argo-wf-plugin-sub-${var.lcaas}"
        TRIGGER_ARGO_WF_JENKINS_INTEGRATION_PIPELINE_SUBSCRIPTION   = "projects/${var.project_id}/subscriptions/cas-jenkins-pipelines-trigger-argo-wf-plugin-sub-${var.lcaas}"
        TRIGGER_ARGO_WF_JENKINS_INTEGRATION_LOGS_SUBSCRIPTION       = "projects/${var.project_id}/subscriptions/cas-jenkins-logs-trigger-argo-wf-plugin-sub-${var.lcaas}"
        WEBHOOKS_TOPIC                               = "projects/${var.project_id}/topics/cas-webhooks-${var.lcaas}"
        WEBHOOKS_SUBSCRIPTION                        = "projects/${var.project_id}/subscriptions/cas-webhooks-sub-${var.lcaas}"
        VCS_ENRICHMENT_TOPIC                         = "projects/${var.project_id}/topics/cas-vcs-enrichment-${var.lcaas}"
        VCS_ENRICHMENT_CICD_SUBSCRIPTION             = "projects/${var.project_id}/subscriptions/cas-vcs-enrichment-cicd-sub-${var.lcaas}"
        PERIODIC_SCAN_TOPIC                          = "projects/${var.project_id}/topics/cas-periodic-scan-${var.lcaas}"
        PERIODIC_SCAN_SECRETS_S_SUBSCRIPTION         = "projects/${var.project_id}/subscriptions/cas-periodic-scan-secrets-s-sub-${var.lcaas}"
        CLI_GIT_USERS_PERSISTENCE_TOPIC              = "projects/${var.project_id}/topics/cas_cli_git_users_persistence-${var.lcaas}"
        CLI_GIT_USERS_PERSISTENCE_SUBSCRIPTION       = "projects/${var.project_id}/subscriptions/cas_cli_git_users_persistence_sub-${var.lcaas}"
        DELETION_API_TOPIC                           = "projects/${var.project_id}/topics/deletion-api-${var.lcaas}"
        DELETION_API_SUBSCRIPTION                    = "projects/${var.project_id}/subscriptions/deletion-api-sub-${var.lcaas}"
        METRICS_RECEIVER_URL                         = "http://opentelemetry-collector.monitoring.svc.cluster.local:4317"
        NEO4J_SERVER_URI                             = "neo4j://dp-neo4j-cluster.xdr-st.svc.cluster.local:7687"
        NEO4J_SERVER_USERNAME                        = "neo4j"
        JENKINS_PLUGIN_BUCKET                        = var.viso_env == "dev" ? "panw-jenkins-plugin-dev" : "panw-jenkins-plugin-prod-us"
        CORTEXCLI_BUCKET_NAME                        = "panw-cortex-cli-${var.multi_project_postfix}"
        CAS_MULTI_TENANT_PROJECT_ID                  = "xdr-cas-${var.multi_project_postfix}-01"
        /*  FEATURE FLAGS */
        CAS_ENABLE_NEW_PR_SCAN_FLOW                  = "true"
      }
    },
    {
      name      = "${var.lcaas}-configmap-cas-mongodb"
      namespace = "cas"
      enabled   = local.enable_cortex_platform
      data = {
        "mongodb.conf" = <<-EOT
        net:
          bindIpAll: true
        storage:
          dbPath: /var/lib/mongodb
          engine: wiredTiger
          wiredTiger:
            engineConfig:
              cacheSizeGB: 4
        replication:
          replSetName: "mongo_rs1"
        security:
          authorization: enabled
          keyFile: /home/<USER>
        EOT
        "initiate.js"  = <<-EOT
        const os = require('os');
        const hostname = os.hostname();
        print('startup script');
        if (hostname.includes('mongodb-0')) {
            print('connecting to db');
            const username = process.env.MONGO_INITDB_ROOT_USERNAME;
            const password = process.env.MONGO_INITDB_ROOT_PASSWORD;
            db = db.getSiblingDB('admin');
            db.auth(username, password);

            let status;
            try {
                status = rs.status();
            } catch (error) {
                if (error.message.includes('no replset config has been received')) {
                    print('No replica set configuration received. Proceeding to initiate.');
                    status = { ok: 0 }; // Set status to indicate that initialization is needed
                } else {
                    print('Unexpected error occurred:', error.message);
                    quit(1);
                }
            }

            if (status && status.ok === 1) {
                print('Replica set is already initialized.');
                while (true) {
                    sleep(1000); // Sleep for 1 second before repeating
                }
            } else {
                print('Trying to initiate');
                var i = rs.initiate({
                    _id: 'mongo_rs1',
                    members: [
                       { _id: 0, host : "xdr-st-${var.lcaas}-mongodb-0.mongo-headless.cas.svc.cluster.local:27017" }
                    ]
                });
            }
        } else {
            print('Hostname does not contain "mongodb-0".');
            while (true) {
                sleep(1000); // Sleep for 1 second before repeating
            }
        }
        EOT
      }
    },
    {
      name      = "${var.lcaas}-configmap-images-ids"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        "images.json"       = file("${path.module}/files/scan-launch-images/vm-image-identifiers.scanner.gen.json")
        "proxy-images.json" = var.viso_env == "prod-fr" || var.viso_env == "prod-gv" ? file("${path.module}/files/scan-launch-images/vm-image-identifiers.proxy-fips.gen.json") : file("${path.module}/files/scan-launch-images/vm-image-identifiers.proxy.gen.json")
      }
    },
    {
      name      = "${var.lcaas}-configmap-uvem"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        MYSQLCONF_USER      = "root"
        UVEM_MYSQLCONF_HOST = "xdr-st-${var.lcaas}-uvem-mysql"
        USE_UVEM_MYSQL      = lookup(var.overrides, "enable_mysql_uvem", false)
        MYSQLCONF_PORT      = "3306"
        CORTEX_PLATFORM_URL = "xdr-st-${var.lcaas}-platform:8000"
        ASSET_CHANGE_FEED_SUBSCRIPTION_NAME = "vxp-comp-controls-asset-change-feed-${var.lcaas}-sub"
        XDR_AGENT_RESEARCH_GCS_BUCKET_NAME  =  var.viso_env == "dev" ? "xdr-agent-research-staging" : "xdr-agent-research-prod"
        GCP_PROJECT_ID            = "${var.project_id}"
        MYSQLCONF_HOST            = "xdr-st-${var.lcaas}-mysql"
        MYSQLCONF_MAIN_DATABASE   = "${var.lcaas}_main"
        NETSCAN_MYSQL_DATABASE    = "${var.lcaas}_netscan"
        NETSCAN_MYSQL_MIGRATION_DATABASE_NAME = "${var.lcaas}_netscan"
        TENANT_ID           =  "${var.lcaas}"
        VXP_POLICIES_FINDINGS_FEED_SUBSCRIPTION_NAME = "dp-finding-revisions-sub-${var.lcaas}"
        VXP_POLICIES_FINDINGS_TOPIC_NAME = "dp-finding-revisions-${var.lcaas}"
      }
    },
    {
      name      = "${var.lcaas}-configmap-cortex-platform-no-restart"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
      }
    }
  ]
  dml_cm = [
    {
      name      = "${var.lcaas}-configmap-dml"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        HPL_NEO4J_URI                                       = local.enable_cortex_platform ? "neo4j://dp-neo4j-cluster-lb-neo4j:7687" : null
        HPL_NEO4J_USERNAME                                  = local.enable_cortex_platform ? "neo4j" : null
        HPL_ASSET_PIPELINE_ENABLE_NEO4J_INGESTER            = local.enable_cortex_platform ? "True" : null
        HPL_NEO4J_DATABASE                                  = local.enable_cortex_platform ? "PlatformGraph" : null
        HPL_XQL_METRICS_REMOTE_STORE                        = !local.deploy_metrus ? "pubsub" : "metrus"
        HPL_ASSET_ASSOCIATIONS_PIPELINE_ASSETS_PUB_SUB_SUBSCRIPTION = local.enable_cortex_platform ? "dp-uai-asset-association-observations-sub-${var.lcaas}" : null
        HPL_ASSET_ASSOCIATIONS_PIPELINE_ENABLED                     = local.enable_cortex_platform ? "true" : null
        HPL_ASSET_ASSOCIATIONS_PIPELINE_MB_TRANSPORT_ASSET_CHANGE_FEED_TOPIC = local.enable_cortex_platform ? "dp-uai-asset-observations-${var.lcaas}" : null
        HPL_ASSET_ASSOCIATIONS_PIPELINE_MB_TRANSPORT_ASSET_CHANGE_FEED_INGEST_BY_GCS = local.enable_cortex_platform ? "false" : null
        HPL_ASSET_ASSOCIATIONS_PIPELINE_METABLOB_ERRORS_TOPIC = local.enable_cortex_platform ? "dp-uai-asset-association-ingestion-errors-${var.lcaas}" : null
        HPL_ASSET_ASSOCIATIONS_PIPELINE_METABLOB_ERRORS_INGEST_BY_GCS = local.enable_cortex_platform ? "false" : null
        HPL_ASSET_ASSOCIATIONS_PIPELINE_VALIDATE_OBJECT_ID_FORMAT = local.enable_cortex_platform ? "false" : null
        HPL_ASSET_PIPELINE_ASSETS_PUB_SUB_SUBSCRIPTION      = local.enable_cortex_platform ? "dp-collector-assets-sub-${var.lcaas}" : null
        HPL_ASSET_PIPELINE_ENABLED                          = local.enable_cortex_platform ? "true" : null
        HPL_ASSET_PIPELINE_REDIS_CONNECTION_STRING          = local.enable_cortex_platform ? "xdr-st-${var.lcaas}-ipl-asset-redis:6379" : null
        HPL_ASSET_PIPELINE_SINGLE_STORE_DB_NAME             = local.enable_cortex_platform ? "main_${var.lcaas}" : null
        HPL_ASSET_PIPELINE_SINGLE_STORE_USER                = local.enable_cortex_platform ? "admin" : null
        HPL_ASSET_PIPELINE_SINGLE_STORE_PORT                = local.enable_cortex_platform ? "3306" : null
        HPL_ASSET_PIPELINE_SINGLE_STORE_DO_PING             = local.enable_cortex_platform ? "true" : null
        HPL_ASSET_PIPELINE_SINGLE_STORE_DML_ENDPOINT        = local.enable_cortex_platform ? "svc-dp-singlestore-cluster-dml:3306" : null
        HPL_ASSET_PIPELINE_SINGLE_STORE_DDL_ENDPOINT        = local.enable_cortex_platform ? "svc-dp-singlestore-cluster-ddl:3306" : null
        HPL_BIGQUERY_PROCESS_INGESTER_INGEST_OTHER_COLUMN   = "False"
        HPL_KEY_VALUE_STORE_DSS_STORE_TYPE                  = lookup(var.overrides, "enable_dss_redis", false) ? "redis" : null
        HPL_KEY_VALUE_STORE_IS_ENABLED                      = var.enable_scylla ? "True" : "False"
        HPL_KEY_VALUE_STORE_IS_BLOCKING                     = var.enable_scylla ? "True" : "False"
        HPL_KEY_VALUE_STORE_IS_EXCLUSIVE                    = var.enable_scylla ? "True" : "False"
        HPL_KEY_VALUE_STORE_UPGRADED_SCYLLA_CLIENT_KEYSPACE = local.scylla_keyspace
        HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_KEYSPACE          = local.scylla_keyspace
        HPL_XQL_ENRICHMENT_SCYLLA_CLIENT_KEYSPACE           = local.scylla_enrichment_keyspace
        HPL_XCLOUD_SCYLLA_CLIENT_KEYSPACE                   = local.scylla_xcloud_keyspace
        HPL_REDIS_FLOW_LOGS_CONNECTION_STRING               = local.xcloud_redis_standalone_deployment ? "xdr-st-${var.lcaas}-xcloud-redis:6379" : local.gonzo_redis_connection_string
        HPL_BIGQUERY_PROCESS_INGESTER_AGENT_INDEX_ENABLE    = "false"
        HPL_BIGQUERY_PROCESS_INGESTER_DATASET               = "ds_${var.lcaas}"
        HPL_BIGQUERY_PROCESS_INGESTER_ERROR_KEEPING_ENABLE  = "false"
        HPL_BIGQUERY_PROCESS_INGESTER_NUM_PIPELINE_ROUTINES = "4"
        HPL_BIGQUERY_PROCESS_INGESTER_TABLE                 = "card_edr_data"
        HPL_BIGQUERY_PROCESS_INGESTER_EVENT_TYPE_MASK       = "1,5,10"
        HPL_ENABLE_BQ_PROCESS_INGESTER                      = "true"
        HPL_MYSQL_MAX_IDLE_CONNECTIONS                      = "0"
        HPL_COLD_STORAGE_AGGREGATOR_AGGREGATED_DATA_BUCKET  = lookup(var.buckets_output, "cold_storage_aggregated_bucket")
        HPL_COLD_STORAGE_AGGREGATOR_RAW_DATA_BUCKET         = lookup(var.buckets_output, "cold-storage-quantums-raw")
        HPL_COLD_STORAGE_ENABLED                            = var.cold_retention ? "True" : "False"
        HPL_COLD_STORAGE_GCS_TARGET_BUCKET                  = lookup(var.buckets_output, "cold-storage-quantums-raw")
        HPL_CWF_INGESTER_TOPIC                              = lookup(var.topics_output, "external_logs_topic")
        HPL_EGRESS_AGGREGATOR_AGGREGATED_DATA_BUCKET        = lookup(var.buckets_output, "event_forwarding_bucket")
        HPL_EGRESS_AGGREGATOR_RAW_DATA_BUCKET               = lookup(var.buckets_output, "egress_raw_bucket")
        HPL_EGRESS_ENABLED                                  = var.egress_enabled ? "True" : "False"
        HPL_EGRESS_GCS_TARGET_BUCKET                        = lookup(var.buckets_output, "egress_raw_bucket")
        HPL_KEY_VALUE_STORE_NETWORK_STORE_TYPE              = var.enable_pipeline ? var.enable_cronus ? "cronus" : "scylla" : null
        HPL_PROMETHEUS_BQ_ADAPTER_ENABLED                   = "true"
        HPL_PROMETHEUS_BQ_ADAPTER_DATASET                   = "ds_${var.lcaas}"
        HPL_PROMETHEUS_BQ_ADAPTER_TABLE                     = "metrics_center"
        HPL_PROMETHEUS_BQ_ADAPTER_TARGETS                   = "ds_${var.lcaas}:metrics_center,xdr-bq-mt-stats-${var.viso_env}-01:metrics_center_${replace(var.viso_env, "-", "_")}:metrics_center:send_to_regional=yes"
        HPL_PZ_MATCHER_IGNORE_SOURCE_LOGS                   = ""
        HPL_LCAAS_ID                                        = var.lcaas
        HPL_XQL_INPUT_SAMPLER_USE_DEDUP_REDIS_IF_EXISTS     = "false"
        HPL_XQL_ENRICHMENT_SCYLLA_CLIENT_HOSTS              = local.scylla_enrichment_endpoint
        HPL_XQL_ENRICHMENT_SCYLLA_CLIENT_USER               = local.scylla_enrichment_username
        HPL_XQL_APP_HUB_OBSERVABILITY_SOURCE_SUBSCRIPTION   = lookup(var.topics_output, "app-hub-observability", null)
        HPL_XQL_APP_HUB_OBSERVABILITY_ENDPOINT              = "http://************/application-hub/observability/prometheus/api/v1/write"
        LOGPROCESSORCONF_DML_SCRIPT_RESULTS_SUBSCRIPTION    = lookup(var.subscriptions_output, "dml_script_results_sub")
        #CRTX-92226
        HPL_EGRESS_FORWARDING_PROXY_URL                   = "http://************:${local.egress_proxy_port}"
        HPL_EGRESS_FORWARDING_MESSAGE_STREAM_SUBSCRIPTION = var.egress_enabled ? "event-forwarding-external-${var.lcaas}-sub" : null

        # Email data CRTX-112920
        HPL_PZ_EMAIL_ATTACHMENTS_TOPIC = lookup(var.topics_output, "email_data")


        # Cronus network hotkeys
        CROP_CONTROLLER_HOTKEYS_ENABLED     = !var.is_metro_tenant
        CROP_HOTKEYS_ENABLED                = !var.is_metro_tenant
        CROP_HOTKEYS_SAMPLING_FREQ          = "2m"
        CROP_HOTKEYS_STREAMS                = "network=ENABLED:True,TOKENS_IN_WINDOW:400000"
        CRONUS_HOTKEYS_ENABLED              = !var.is_metro_tenant
        CRONUS_HOTKEYS_SAMPLE_RATE          = "2m"
        CRONUS_HOTKEYS_TRACKER              = "network=ENABLED:True,RATE_WINDOW:2m,TOKENS_IN_WINDOW:400000"
        HPL_CRONUS_CLIENT_HOTKEYS_ENABLED   = !var.is_metro_tenant
      }
    },
    {
      name      = "${var.lcaas}-configmap-metrics-aggregator"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        HPL_XQL_METRICS_AGGREGATOR_PUBSUB_SUBSCRIPTION = join("/", [
          "projects", var.project_id, "subscriptions", lookup(var.subscriptions_output, "metrics_aggregator_sub")
        ])
        GONZO_REDIS_CONNECTION_STRING      = local.gonzo_redis_connection_string
        HPL_GONZO_REDIS_CONNECTION_STRING  = local.hpl_gonzo_redis_connection_string
        HPL_GONZO_REDIS_USE_DEFAULT        = local.hpl_gonzo_redis_use_default
        HPL_REDIS_CONNECTION_STRING        = local.gonzo_redis_connection_string
        HPL_GCP_DATASET                    = lookup(var.bq_output, "ds")
        HPL_GCP_PROJECT_ID                 = var.project_id
        GONZO_LOGGER_TRANSPORT             = "console:0,slack:2"
        HPL_LCAAS_ID                       = var.lcaas
        HPL_XQL_METRICS_AGGREGATOR_TARGETS = "ds_${var.lcaas}:system_monitoring,xdr-bq-mt-stats-${var.viso_env}-01:metrics_center_${replace(var.viso_env, "-", "_")}:system_monitoring"
        HPL_XQL_METRICS_REMOTE_STORE       = !local.deploy_metrus ? "pubsub" : "metrus"
      }
    },
    {
      name      = "${var.lcaas}-configmap-cold-storage-aggregation"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        HPL_REDIS_CONNECTION_STRING                       = local.gonzo_redis_connection_string
        HPL_GCP_DATASET                                   = lookup(var.bq_output, "ds")
        HPL_GCP_PROJECT_ID                                = var.project_id
        HPL_LCAAS_ID                                      = var.lcaas
        HPL_COLD_STORAGE_DATASET_AGGREGATOR_PUB_SUB_TOPIC = lookup(var.topics_output, "cold_storage_aggregation", "")
      }
    },
    {
      name      = "${var.lcaas}-configmap-xql"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        HPL_EDR_PIPELINE_DISABLED                   = "true"
        HPL_EDR_SOURCE_SUBSCRIPTION                 = ""
        HPL_XQL_ENRICHMENT_HOSTS_ENRICHMENT_ENABLED = local.enable_xql_fdr_engine ? "True" : null
        HPL_XQL_ENRICHMENT_ENABLED                  = local.enable_xql_fdr_engine ? "True" : null
        HPL_XQL_INPUT_ROW_ID_ENABLED                = local.enable_playbook_mirroring ? "True" : null
      }
    },
    {
      name      = "${var.lcaas}-configmap-edr"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        HPL_PZ_DISABLED                                    = "true"
        HPL_XQL_EXT_SUBSCRIPTION                           = ""
        HPL_PZ_LOG_PROCESSOR_SUBSCRIPTION                  = ""
        HPL_PZ_SCRIPT_RESULTS_SUBSCRIPTION                 = ""
        HPL_PZ_LCAAS_SUBSCRIPTION                          = ""
        HPL_PZ_WEF_SUBSCRIPTION                            = ""
        HPL_XQL_APP_HUB_OBSERVABILITY_SOURCE_SUBSCRIPTION  = ""
        HPL_XQL_MB_INGESTER_TRANSPORT_PLATFORM_BUS_ENABLED = "false"
      }
    }
  ]
  demisto_cm = [
    {
      name      = "${var.lcaas}-configmap-xsoar-ng"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = var.enable_xsoar_shared_components
      data = {
        "demisto.conf" = templatefile(
          "${path.module}/files/xsoar-ng.conf.tpl",
          {
            xsoarconf_bucket_name                           = lookup(var.buckets_output, "xsoar_files")
            engine_id                                       = "${var.project_id}-engine-01"
            lcaas_id                                        = var.lcaas
            repo_branch                                     = var.customer_dev_tenant ? local.xsoar_ng_dev_branch : ""
            enable_vc                                       = local.enable_vc
            is_xsoar                                        = var.is_xsoar
            is_xsiam                                        = var.is_xsiam || var.is_xpanse
            is_xpanse                                       = var.is_xpanse
            is_xsoar_6_migration                            = var.is_xsoar_6_migration
            gcpconf_project_id                              = var.project_id
            backup_enable                                   = var.viso_env != "dev"
            backup_bucket                                   = lookup(var.buckets_output, "elasticsearch-backup", "")
            backup_schedule                                 = "0 0 ${contains(split("-", var.region), "europe") ? "1" : "9"} * * ?"
            git_url                                         = "https://source.developers.google.com/p/${local.parent_project_id}/r/content"
            gcp_git                                         = var.viso_env != "prod-gv"
            gcppubsub_playbook_exec_topic_name              = lookup(var.topics_output, "playbook_execution_topic")
            external_fqdn                                   = var.external_fqdn
            parent_external_fqdn                            = var.customer_dev_tenant ? var.parent_external_fqdn : var.external_fqdn
            workers_count                                   = var.viso_env == "dev" && !var.prod_spec ? "8" : "56"
            migration_token                                 = base64encode(var.xsoar_6_migration_token)
            marketplace_bootstrap_bypass_url                = lookup(var.overrides, "marketplace_bootstrap_bypass_url", local.enable_cortex_platform ? "marketplace-cortex-content-${var.multi_project_postfix}/april-content" : "marketplace-${var.product_type}-${var.multi_project_postfix}")
            marketplace_v2_url                              = lookup(var.overrides, "marketplace_bootstrap_bypass_url", local.enable_cortex_platform ? "marketplace-cortex-content-${var.multi_project_postfix}/april-content" : "marketplace-${var.product_type}-${var.multi_project_postfix}")
            marketplace_sso_asc                             = var.viso_env == "dev" ? "https://xsoar-authentication-proxy-dev.paloaltonetworks.com/api/v1/saml/response" : "https://xsoar-authentication-proxy.paloaltonetworks.com/api/v1/saml/response"
            marketplace_sso_endpoint                        = var.viso_env == "dev" ? "https://ssopreview.paloaltonetworks.com/app/panw-ciam_xsoarmarketplace_1/exk3ux5w6rGPmLt2P1d7/sso/saml" : "https://sso.paloaltonetworks.com/app/panw-ciam_xsoarmarketplace_1/exk2fire9DUUcnW7X0j6/sso/saml"
            marketplace_premium_content_gateway_service_url = var.viso_env == "dev" ? "https://xsoar-premium-content-gateway-dev.paloaltonetworks.com" : "https://xsoar-premium-content-gateway.paloaltonetworks.com"
            marketplace_subscription_service_url            = var.viso_env == "dev" ? "https://xsoar-marketplace-subscriptions-dev.paloaltonetworks.com" : "https://xsoar-marketplace-subscriptions.paloaltonetworks.com"
            hashed_xdr_http_token                           = var.hashed_xdr_http_token != null ? var.hashed_xdr_http_token : ""
            hashed_wildfire_key                             = var.hashed_wildfire_key != null ? var.hashed_wildfire_key : ""
            hashed_monitoring_key                           = var.hashed_monitoring_key != null ? var.hashed_monitoring_key : ""
            hashed_autofocus_key                            = var.hashed_autofocus_key != null ? var.hashed_autofocus_key : ""
            unit42intel_service_url                         = var.viso_env == "dev" ? "https://unit42intel-dev.xsoar.paloaltonetworks.com" : "https://unit42intel.xsoar.paloaltonetworks.com"
            customer_dev_tenant                             = var.customer_dev_tenant
            xsoar_domain_whitelist                          = join(",", var.xsoar_domain_whitelist)
            xsoar_port_whitelist                            = join(",", var.xsoar_port_whitelist)
          }
        )
        "conf_definitions.json"                                = file("${path.module}/files/conf_definitions.json")
        ARCHIVE_JOB_ENABLED                                    = var.is_xpanse || var.is_xsoar
        ARCHIVE_JOB_LIMIT                                      = var.is_xpanse ? 500 : null
        ARCHIVE_JOB_PERIOD                                     = var.is_xpanse ? 32400 : null
        GCPCONF_BQ_DATASET                                     = lookup(var.bq_output, "ds_xsoar", "ds_${var.lcaas}_xsoar")
        GCPCONF_PROJECT_ID                                     = var.project_id
        GCPPUBSUB_PLAYBOOK_EXEC_TOPIC_NAME                     = lookup(var.topics_output, "playbook_execution_topic")
        LOGGINGSERVICE_LCAAS_TENANT                            = var.lcaas
        PERSIST_INCIDENT_FIELDS_TO_REMOTE_CACHE                = true
        ALERTSCONF_FETCHER_TOPIC_NAME                          = lookup(var.topics_output, "alerts_fetcher")
        XSOARCONF_FRONTEND_HOST                                = "***********:80"
        GCPPUBSUB_UI_NOTIFICATIONS_PUBSUB_TOPIC_NAME           = "notifications-notifier-${var.lcaas}"
        GCPPUBSUB_AUDIT_PUBSUB_TOPIC_NAME                      = "mgmt-audit-notifier-${var.lcaas}"
        GENERIC_XDR_TENANT_NAME                                = var.instance_name
        ENTRY_ENRICHMENT_PUBSUB_SUBSCRIPTION_NAME              = lookup(var.subscriptions_output, "xsoar_indicator_enrichment_results_sub")
        ENTRY_ENRICHMENT_PUBSUB_TOPIC                          = lookup(var.topics_output, "xsoar_indicator_enrichment_results")
        INDICATOR_TIMELINE_PUBSUB_SUBSCRIPTION_NAME            = lookup(var.subscriptions_output, "xsoar_indicator_timeline_comments_sub")
        INDICATOR_TIMELINE_PUBSUB_TOPIC                        = lookup(var.topics_output, "xsoar_indicator_timeline_comments")
        INSIGHT_CACHE_SYNC_PUBSUB_SUBSCRIPTION_NAME            = lookup(var.subscriptions_output, "xsoar_indicator_score_updates_sub")
        INSIGHT_CACHE_SYNC_PUBSUB_TOPIC                        = lookup(var.topics_output, "xsoar_indicator_score_updates")
        RELATIONSHIPS_PUBSUB_SUBSCRIPTION_NAME                 = lookup(var.subscriptions_output, "xsoar_relationships_sub")
        RELATIONSHIPS_PUBSUB_TOPIC                             = lookup(var.topics_output, "xsoar_relationships")
        TAGS_FIELD_VALUES_PUBSUB_SUBSCRIPTION_NAME             = lookup(var.subscriptions_output, "xsoar_tags_field_values_sub")
        TAGS_FIELD_VALUES_PUBSUB_TOPIC                         = lookup(var.topics_output, "xsoar_tags_field_values")
        XSOARCONF_FRONTEND_HOST                                = "***********:80"
        GENERIC_XDR_TENANT_TYPE                                = var.tenant_type
        BQSTATSCONF_BQ_STATS_PROJECT                           = "xdr-bq-mt-stats-${var.viso_env}-01"
        GCPPUBSUB_UI_NOTIFICATIONS_PUBSUB_TOPIC_NAME           = "notifications-notifier-${var.lcaas}"
        GCPPUBSUB_AUDIT_PUBSUB_TOPIC_NAME                      = "mgmt-audit-notifier-${var.lcaas}"
        GENERIC_XDR_TENANT_NAME                                = var.instance_name
        GCPCONF_SHARED_FIRESTORE_PROJECT_ID                    = local.enable_playbook_mirroring ? local.primary_project_id : null
        HA_BUCKET_NAME                                         = local.enable_playbook_mirroring ? "${local.primary_project_id}-playbook-mirroring" : null
        XSIAM_HA_MODE                                          = local.enable_primary_playbook_mirroring ? "primary" : local.enable_secondary_playbook_mirroring ? "secondary" : null
        GCP_PROFILER_XSOAR_ENABLED                             = !var.is_fedramp
        GCP_PROFILER_ENGINEHUB_ENABLED                         = !var.is_fedramp
        GCP_PROFILER_XSOAR_INIT_ENABLED                        = !var.is_fedramp
        GCP_PROFILER_XSOAR_API_ENABLED                         = !var.is_fedramp
        GCP_PROFILER_PBRUNNER_ENABLED                          = !var.is_fedramp
        MYSQLCONF_DB                                           = "${var.lcaas}_xsoar"
        MYSQLCONF_HOST                                         = "xdr-st-${var.lcaas}-mysql"
        WEBAPPCONF_PIN_HOSTNAME                                = var.external_fqdn
        WEBAPPCONF_PIN_SECONDARY_HOSTNAME                      = local.enable_primary_playbook_mirroring ? lookup(var.overrides, "secondary_external_fqdn", "") : null
        XSOARCONF_XSOAR_NG_ENABLED                             = var.enable_xsoar_shared_components
        GENERIC_PRODUCT_TYPE                                   = var.product_type
        GENERIC_PRODUCT_CODE                                   = var.product_code
        SELECTIVE_PROPAGATION_ENABLED                          = local.selective_propagation
        TIMINDICATORSCONF_ALERTS_TO_XSOAR_SUBSCRIPTION         = lookup(var.subscriptions_output, "alerts_to_xsoar_sub")
        TIMINDICATORSCONF_ALERTS_TO_XSOAR_TOPIC                = lookup(var.topics_output, "alerts_to_xsoar_topic")
        TIMINDICATORSCONF_ARTIFACT_ALERT_RELATION_SUBSCRIPTION = lookup(var.subscriptions_output, "xsoar_artifacts_extraction_incident_notifications_sub")
        TIMINDICATORSCONF_ARTIFACT_ALERT_RELATION_TOPIC        = lookup(var.topics_output, "xsoar_artifacts_extraction_incident_notifications")
        TIMINDICATORSCONF_ARTIFACT_EXTRACTION_TOPIC            = lookup(var.topics_output, "artifact_extraction_topic")
        TIMINDICATORSCONF_ARTIFACT_EXTRACTION_SUBSCRIPTION     = lookup(var.subscriptions_output, "artifact_extraction_sub")
        REDIS_CONNECTION_STRING                                = local.gonzo_redis_connection_string
        XSOAR_MSSP_CHILD                                       = var.xsoar_mssp_child
        XSOAR_MSSP_MASTER                                      = var.xsoar_mssp_master && !var.is_xsiam
        XSOAR_REDIS_ENABLED                                    = var.is_xsiam ? true : null
        XSOAR_REDIS_URL                                        = var.is_xsiam ? "xdr-st-${var.lcaas}-xsoar-redis:6379" : null
        XSOAR_OBJECTS_ARCHIVING_BUCKET_NAME                    = lookup(var.buckets_output, "xsoar_objects_archiving")
        XSOARCONF_WS_INDICATORS_PAGE_PATH                      = "/threat-intel/indicators"
        XSOARMIGRATIONCONF_SOURCE_TENANT_HOST                  = var.xsoar_6_host
        XSOARMIGRATIONCONF_IS_ON_PREM                          = var.is_xsoar_onprem_migration
        XSOARMIGRATIONCONF_SIZE                                = var.xsoar_6_mig_size
        WORKER_RUNNER_ROUTER_ADDRESS                           = "xdr-st-${var.lcaas}-xsoar-workers-router:9090"
        NO_PROXY                                               = "gcr.io,xdr-st-${var.lcaas}-frontend,${local.core_apps.api.app_name},${local.core_apps.api.app_name}.xdr-st,${local.core_apps.api-xsoar.app_name},${local.core_apps.api-xsoar.app_name}.xdr-st,xdr-st-${var.lcaas}-engine-hub,xdr-st-${var.lcaas}-xsoar-workers-router,.internal,.googleapis.com,************,***********,***********,*********,elastic-es-xsoar,source.developers.google.com,metadata.google.internal,${var.region}-docker.pkg.dev,***********,xdr-st-${var.lcaas}-xsoar-api,xdr-st-${var.lcaas}-xsoar-api.xdr-st"
        GCR_CREDHELPER                                         = "${var.region}-docker.pkg.dev"
        XSOARCONF_XSOAR_HOST                                   = "***********:5566"
        XSOARCONF_API_HOST                                     = var.api_split && var.is_xsiam ? "${local.core_apps.api-xsoar.app_name}.xdr-st:${local.core_apps.api-xsoar.port}" : "${local.core_apps.api.app_name}.xdr-st:${local.core_apps.api.port}"
        XSOAR_API_SERVICE_HOST                                 = "xdr-st-${var.lcaas}-xsoar-api.xdr-st:5566"
        http_proxy                                             = "http://${var.tenant_proxy}:13128"
        https_proxy                                            = "http://${var.tenant_proxy}:13128"
        XSOAR_INCIDENTS_CREATION_BUCKET_NAME                   = lookup(var.buckets_output, "xsoar_incidents_to_create")
        XSOAR_INCIDENTS_CREATION_TOPIC                         = lookup(var.topics_output, "xsoar_incidents_to_create_topic")
        SAVE_INCIDENTS_TO_BUCKET_ENABLED                       = var.is_xsoar
        GOOGLE_ARTIFACT_DOCKER_REGISTRY                        = "${var.region}-docker.pkg.dev/${var.project_id}/xsoar-docker"
        IS_USING_GOOGLE_ARTIFACT_REGISTRY                      = "true"
        BULK_MSGBUS_INIT_BOTH_PUBLISHERS                       = true
        BULK_MSGBUS_ENABLED                                    = true
        XSOAR_ENGINE_MESSAGE_BUCKET_NAME                       = lookup(var.buckets_output, "xsoar_default_runner")
        XSOAR_NG_REDIS_MAX_MESSAGE_DIRECT_TO_ENGINE_ENABLED    = true
        XSOAR_NG_REDIS_MAX_MESSAGE_DIRECT_ENABLED              = true
        KUBERNETES_JOBS_RUNTIME_NAMESPACE                      = "xsoar-jobs"
        EXDELETE_INCIDENTS_JOB_SERVICE_ACCOUNT                 = "xsoar-job-pod"
        EXDELETE_INCIDENTS_JOB_XSOAR_ARGS                      = "--proc.logger.format gcloud"
        EXDELETE_INCIDENTS_JOB_XSOAR_COMMAND                   = "/usr/local/demisto/app"
        EXDELETE_INCIDENTS_JOB_XSOAR_IMAGE                     = replace(lookup(var.app_images, "xsoar/xsoar", null), "demisto/server", "demisto/xsoar-job-worker")
        UI_TERM_INCIDENT                                       = local.enable_cortex_platform ? "6" : null
        GENERIC_TENANT_DEPLOYMENT_BATCH       = var.is_fedramp ? null : var.viso_env == "dev" ? "P1" : var.upgrade_phase
        GCPCONF_REAL_REGION                                    = var.region
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-content"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = var.enable_xsoar_shared_components
      data = {
        "demisto.conf" = templatefile(
          "${path.module}/files/xsoar-content.conf.tpl",
          {
            xsoarconf_bucket_name                           = lookup(var.buckets_output, "xsoar_files")
            engine_id                                       = "${var.project_id}-engine-01"
            repo_branch                                     = var.customer_dev_tenant ? local.xsoar_ng_dev_branch : ""
            enable_vc                                       = local.enable_vc
            is_xsoar                                        = var.is_xsoar
            is_xsoar_6_migration                            = var.is_xsoar_6_migration
            lcaas                                           = var.lcaas
            is_xsiam                                        = var.is_xsiam || var.is_xpanse
            git_url                                         = "https://source.developers.google.com/p/${local.parent_project_id}/r/content"
            gcp_git                                         = var.viso_env != "prod-gv"
            gcppubsub_playbook_exec_topic_name              = lookup(var.topics_output, "playbook_execution_topic")
            external_fqdn                                   = var.external_fqdn
            is_fedramp                                      = var.is_fedramp
            workers_count                                   = var.viso_env == "dev" && !var.prod_spec ? "8" : "56"
            migration_token                                 = base64encode(var.xsoar_6_migration_token)
            marketplace_bootstrap_bypass_url                = lookup(var.overrides, "marketplace_bootstrap_bypass_url", local.enable_cortex_platform ? "marketplace-cortex-content-${var.multi_project_postfix}/april-content" : "marketplace-${var.product_type}-${var.multi_project_postfix}")
	    marketplace_v2_url                              = lookup(var.overrides, "marketplace_bootstrap_bypass_url", local.enable_cortex_platform ? "marketplace-cortex-content-${var.multi_project_postfix}/april-content" : "marketplace-${var.product_type}-${var.multi_project_postfix}")
            marketplace_sso_endpoint                        = var.viso_env == "dev" ? "https://ssopreview.paloaltonetworks.com/app/panw-ciam_xsoarmarketplace_1/exk3ux5w6rGPmLt2P1d7/sso/saml" : "https://sso.paloaltonetworks.com/app/panw-ciam_xsoarmarketplace_1/exk2fire9DUUcnW7X0j6/sso/saml"
            marketplace_premium_content_gateway_service_url = var.viso_env == "dev" ? "https://xsoar-premium-content-gateway-dev.paloaltonetworks.com" : "https://xsoar-premium-content-gateway.paloaltonetworks.com"
            marketplace_subscription_service_url            = var.viso_env == "dev" ? "https://xsoar-marketplace-subscriptions-dev.paloaltonetworks.com" : "https://xsoar-marketplace-subscriptions.paloaltonetworks.com"
            hashed_xdr_http_token                           = var.hashed_xdr_http_token != null ? var.hashed_xdr_http_token : ""
            hashed_wildfire_key                             = var.hashed_wildfire_key != null ? var.hashed_wildfire_key : ""
            hashed_monitoring_key                           = var.hashed_monitoring_key != null ? var.hashed_monitoring_key : ""
            hashed_autofocus_key                            = var.hashed_autofocus_key != null ? var.hashed_autofocus_key : ""
            unit42intel_service_url                         = var.viso_env == "dev" ? "https://unit42intel-dev.xsoar.paloaltonetworks.com" : "https://unit42intel.xsoar.paloaltonetworks.com"
            customer_dev_tenant                             = var.customer_dev_tenant
          }
        )
        GCP_PROFILER_CONTENT_ENABLED                 = !var.is_fedramp
        GCPCONF_PROJECT_ID                           = var.project_id
        GCPPUBSUB_PLAYBOOK_EXEC_TOPIC_NAME           = lookup(var.topics_output, "playbook_execution_topic")
        LOGGINGSERVICE_LCAAS_TENANT                  = var.lcaas
        ALERTSCONF_FETCHER_TOPIC_NAME                = lookup(var.topics_output, "alerts_fetcher")
        XSOARCONF_FRONTEND_HOST                      = "***********:80"
        GCPPUBSUB_UI_NOTIFICATIONS_PUBSUB_TOPIC_NAME = "notifications-notifier-${var.lcaas}"
        GCPPUBSUB_AUDIT_PUBSUB_TOPIC_NAME            = "mgmt-audit-notifier-${var.lcaas}"
        WEBAPPCONF_PIN_HOSTNAME                      = var.external_fqdn
        XSOARCONF_XSOAR_NG_ENABLED                   = var.enable_xsoar_shared_components
        GENERIC_PRODUCT_TYPE                         = var.product_type
        GENERIC_PRODUCT_CODE                         = var.product_code
        GENERIC_XDR_TENANT_NAME                      = var.instance_name
        POPULATE_REPO_SKIP                           = true
        MYSQLCONF_DB                                 = "${var.lcaas}_xsoar"
        MYSQLCONF_HOST                               = "xdr-st-${var.lcaas}-mysql"
        REPO_MYSQL                                   = "true"
        REDIS_CONNECTION_STRING                      = local.gonzo_redis_connection_string
        XSOARCONF_WS_INDICATORS_PAGE_PATH            = "/threat-intel/indicators"
        NO_PROXY                                     = "xdr-st-${var.lcaas}-frontend,xdr-st-${var.lcaas}-engine-hub,.internal,.googleapis.com,***********,************,***********,elastic-es-xsoar,source.developers.google.com,metadata.google.internal,***********,xdr-st-${var.lcaas}-xsoar-api,${local.core_apps.api.app_name},${local.core_apps.api-xsoar.app_name}"
        XSOARCONF_XSOAR_HOST                         = "***********:5566"
        SELECTIVE_PROPAGATION_ENABLED                = local.selective_propagation
        http_proxy                                   = "http://${var.tenant_proxy}:13128"
        https_proxy                                  = "http://${var.tenant_proxy}:13128"
        XSOARMAILINGCONF_SMTP_SERVER_ADDR            = "************"
        XSOARMAILINGCONF_SMTP_SERVER_PORT            = var.is_fedramp ? "11129" : "11127"
        XSOARMAILINGCONF_USE_SSL                     = var.is_fedramp ? "True" : "False"
        XSOARMAILINGCONF_DEFAULT_FROM_ADDR           = "<EMAIL>"
        XSOARMAILINGCONF_DEFAULT_FROM_NAME           = "Cortex"
        XSOARMAILINGCONF_USERNAME                    = "apikey"
        XSOARMIGRATIONCONF_SOURCE_TENANT_HOST        = var.xsoar_6_host
        XSOARMIGRATIONCONF_IS_ON_PREM                = var.is_xsoar_onprem_migration
        XSOAR_MSSP_CHILD                             = var.xsoar_mssp_child
        XSOAR_MSSP_MASTER                            = var.xsoar_mssp_master && !var.is_xsiam
        XSOAR_REDIS_ENABLED                          = var.is_xsiam ? true : null
        XSOAR_REDIS_URL                              = var.is_xsiam ? "xdr-st-${var.lcaas}-xsoar-redis:6379" : null
        XSOAR_OBJECTS_ARCHIVING_BUCKET_NAME          = lookup(var.buckets_output, "xsoar_objects_archiving")
        MARKETPLACE_OVERRIDABLE_BUCKET_NAME          = "${var.project_id}-xsoar-marketplace-overridable"
        XSOARCONF_API_HOST                           = var.api_split && var.is_xsiam ? "${local.core_apps.api-xsoar.app_name}:${local.core_apps.api-xsoar.port}" : "${local.core_apps.api.app_name}:${local.core_apps.api.port}"
        GENERIC_TENANT_DEPLOYMENT_BATCH              = var.is_fedramp ? null : var.viso_env == "dev" ? "P1" : var.upgrade_phase
        GCPCONF_REAL_REGION                          = var.region
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-pb-ng"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = var.enable_xsoar_shared_components
      data = {
        GCPCONF_BQ_DATASET                           = lookup(var.bq_output, "ds_xsoar", "ds_${var.lcaas}_xsoar")
        ENGINE_UPGRADE_ON_INIT                       = "false"
        PLAYBOOKRUNNER_AUTOEXIT_ENABLED              = "false"
        PLAYBOOKRUNNER_AUTOEXIT_TIMEOUTSECONDS       = "60"
        PLAYBOOKRUNNER_MONITORING_ENABLED            = "true"
        PLAYBOOKRUNNER_TERMINATIONGRACEPERIODSECONDS = "600"
        SERVER_INV_PLAYBOOK_CACHE_ENABLED            = "false"
        XSOAR_NG_PLAYBOOK_PUBLISHER                  = "false"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-registry"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = var.enable_xsoar_shared_components || var.is_xsoar_6_migration
      data = {
        "999-xsoar-registry.conf" = templatefile("${path.module}/files/999-xsoar-registry.conf.tpl", {
          xsoar_registry = "${local.xsoar_registry}/demisto" }
        )
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-workers-router"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_xsoar_workers
      data = {
        XSOAR_PROC_LOGGER_FORMAT = "gcloud"
        XSOAR_PROC_LOGGER_LEVEL  = "debug"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-workers-gateway"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_xsoar_workers
      data = {
        XSOAR_APP_GATEWAY_NGINX_WORKERS_ROUTER_HOST = "xdr-st-${var.lcaas}-xsoar-workers-router:9090"
        XSOAR_APP_GATEWAY_SIGN_HANDLER_EXPIRES_IN   = "5m"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-no-restart"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = var.enable_xsoar_shared_components
      data = {
        HPA_INFO_PB_RUNNERS_V2_DOWNSCALE_THRESHOLD = "5"
        HPA_INFO_PB_RUNNERS_V2_TARGET_VALUE        = "1.33"
        HPA_INFO_PB_RUNNERS_V2_DEPLOYMENT_NAME     = "xdr-st-${var.lcaas}-pb-runner-v2"
        KUBERNETES_NAMESPACE                       = "xdr-st"
        HPA_INFO_PB_RUNNERS_V2_MIN                 = local.xsoar_playbook_runner_min_hpa + local.xsoar_playbook_priority_min_hpa
        HPA_INFO_PB_RUNNERS_V2_MAX                 = local.xsoar_playbook_runner_max_hpa + local.xsoar_playbook_priority_max_hpa
        PLAYBOOK_RUNNERS_AMOUNT_RESERVED           = local.xsoar_playbook_priority_max_hpa
      }
    }
  ]
  scortex_deployment_cm = [
    {
      name      = "${var.lcaas}-configmap-scortex-deployment"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = !var.is_metro_tenant
      data = {
        GENERIC_LCAAS_ID                                 = var.lcaas
        LOGGINGSERVICE_LCAAS_TENANT                      = var.project_id
        CatboostModelConf_content_management_project_id  = var.global_bioc_project
        CatboostModelConf_content_management_bucket_name = var.global_bioc_bucket
        SecurityConf_public_key_project_id               = "xdr-kms-project-${var.multi_project_postfix}-01"
        SecurityConf_public_key_location                 = var.kms_keyring_region
        SecurityConf_verify_models_signature             = var.viso_env == "dev" ? "False" : "True"

      }
    }
  ]
  feature_flags_cm = [
    {
      name      = "${var.lcaas}-configmap-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init                                         = "init"
        CONTENTCONF_BW_LIMIT_OPTIMIZATION_FF_ENABLED = "True"
        XqlGlobalConfDefault_case_sensitive          = "False"
        ALPHAFEATURES_PR_CLOUD_COMMAND_CENTER        = "True"
        AlphaFeatures_ios_support                    = var.is_fedramp ? "False" : null
        ALPHAFEATURES_ENABLE_CUSTOMER_ASM_UPLOAD     = var.is_fedramp ? "False" : null
      }
    },
    {
      name      = "${var.lcaas}-configmap-dml-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-scortex-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-rocksdb-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-rocksdb-writer-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init                              = "init"
        IS_USING_GOOGLE_ARTIFACT_REGISTRY = "true"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-workers-gateway-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_xsoar_workers
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xsoar-feature-flags-no-restart"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-xql-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        # we needed the ability to preserve config values only for xql-engine without TF reverting them if they were in the deployment
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-cwp-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-ciem-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-x-files-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-fda-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-cb-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "cas-configmap-feature-flags"
      namespace = module.create_namespace.ns[local.cas_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
        CAS_MULTI_TENANT_PROJECT_ID                  = "xdr-cas-${var.multi_project_postfix}-01"
        CAS_ENABLE_NEW_PR_SCAN_FLOW                  = "true"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-mac-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-dspm-ai-data-connector-feature-flags"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        init = "init"
      }
    },
    {
      name      = "${var.lcaas}-configmap-frontend-feature-flags-no-restart"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data = {
        init = "init"
        ITDR_CONDITIONAL_ACCESS_POLICY_FLOW_FEATURE_ENABLED = "false"
      }
    }
  ]
  xcloud_cm = [
    {
      name           = "${var.lcaas}-configmap-xcloud"
      namespace      = module.create_namespace.ns[local.st_namespace]
      enabled        = var.enable_xcloud
      ignore_changes = []
      data = {
        HPL_PZ_INGESTER_MB_TRANSPORT_ASSETS_ENABLED    = local.enable_cortex_platform ? null : var.enable_xcloud
        HPL_PZ_INGESTER_ENABLE_XCLOUD_EDITORS_INGESTER = local.enable_cortex_platform ? null : var.enable_xcloud
        HPL_REDIS_FLOW_LOGS_CONNECTION_STRING          = local.xcloud_redis_standalone_deployment ? "xdr-st-${var.lcaas}-xcloud-redis:6379" : local.gonzo_redis_connection_string
        HPL_XCLOUD_SCYLLA_CLIENT_HOSTS                 = local.scylla_xcloud_endpoint
        HPL_XCLOUD_SCYLLA_CLIENT_USER                  = local.scylla_xcloud_username
        HPL_XCLOUD_INTEGRATION_ENABLED                 = var.enable_xcloud
      }
    }
  ]
  prometheus_config_map = []
  prometheus_config_map_data = [
    {
      name      = "prometheus-scrape-conf"
      namespace = "monitoring"
      enabled   = var.is_metro_tenant
      data = {
        "metro_resident_${var.lcaas}.yml" = templatefile(
          "${path.module}/files/prometheus/prom-config-map-scrape-config-metro-resident.yaml.tpl",
          {
            module_path                      = path.module
            lcaas_id                         = var.lcaas
            tenant_type                      = var.tenant_type
            xdr_id                           = var.pool_tenant_creation ? "pool-tenant" : var.xdr_id
            product_type                     = var.product_type
            product_tier                     = var.product_tier
            # "metrics_debug_all" and "metrics_debug_selected" overrides are there for the backward capability
            metrics_whitelist_debug_all      = tobool(lower(lookup(var.overrides, "metrics_whitelist_debug_all", lookup(var.overrides, "metrics_debug_all", "true"))))
            metrics_whitelist_debug_selected = lookup(var.overrides, "metrics_whitelist_debug_selected", lookup(var.overrides, "metrics_debug_selected", "")) # metric1;metric2;metric3
            metrics_blacklist_debug_selected = lookup(var.overrides, "metrics_blacklist_debug_selected", "") # metric1;metric2;metric3
          }
        )
      }
    }
  ]
}

locals {
  egress_proxy_port                           = lookup(var.overrides, "enable_egress_proxy_platform", false) || local.enable_cortex_platform || var.is_xsoar ? "11117" : "11114"
  prisma_service_accounts                     = [for app, app_data in local.platform_apps : "${app_data.service_account_name}@${var.project_id}.iam.gserviceaccount.com" if lookup(app_data, "enabled", true)]
  commercial_env                              = !contains(["dev", "prod-fr", "prod-gv"], var.viso_env)
  gonzo_redis_host                            = "xdr-st-${var.lcaas}-redis"
  gonzo_redis_port                            = "6379"
  gonzo_redis_connection_string               = "${local.gonzo_redis_host}:${local.gonzo_redis_port}"
  dss_redis_connection_string                 = "xdr-st-${var.lcaas}-dss-redis:6379"
  ca_collection_connection_string             = local.enable_cortex_platform ? "xdr-st-${var.lcaas}-ca-collection-redis:6379" : null
  hpl_gonzo_redis_connection_string           = lookup(var.overrides, "enable_gonzo_dragonfly", false) ? "xdr-st-${var.lcaas}-gonzo-dragonfly:6379" : var.redis_split ? "xdr-st-${var.lcaas}-gonzo-redis:6379" : local.gonzo_redis_connection_string
  hpl_gonzo_redis_use_default                 = var.redis_split || lookup(var.overrides, "enable_gonzo_dragonfly", false) ? "False" : "True"
  dspm_redis_host                             = "xdr-st-${var.lcaas}-dspm-redis"
  dspm_redis_port                             = "6379"
  xpanse_multi_tenants_bucket_name            = contains(["dev", "prod-fr", "prod-gv"], var.viso_env) ? "xpanse-policies-${var.multi_project_postfix}" : "xpanse-policies-prod"
  missed_flips_bucket_name                    = var.viso_env == "dev" ? "missing-wf-verdict-flips-dev" : var.is_fedramp ? "missing-wf-verdict-flips-${var.viso_env}}" : "missing-wf-verdict-flips-prod"
  analytics2_enabled                          = var.enable_pipeline
  xpanse_analytics_enabled                    = var.is_xpanse
  geo_location_project                        = "xdr-bq-mt-stats-${var.viso_env}-01"
  geo_location_dataset                        = var.viso_env == "dev" ? "geolite_${var.viso_env}" : "geolite_${replace(var.viso_env, "-", "_")}"
  geo_location_table                          = "maxmind"
  global_config_bucket                        = "xdr-global-configs-${var.viso_env == "dev" ? "dev" : var.is_fedramp ? var.viso_env : "prod"}"
  rulesconf_global_export_file_name           = "xdrv2_global.zip"
  gonzo_slack_token                           = data.google_secret_manager_secret_version.be_error_reporting_slack_token.secret_data
  distributions_postfix                       = var.multi_project_postfix == "dev" ? "-dev" : var.multi_project_postfix == "prod-fr" ? "-prod-fed" : var.multi_project_postfix == "prod-gv" ? "-prod-gv" : ""
  xdr_use_slack_channel                       = var.project_prefix == "xdr-us" || var.project_prefix == "xdr-eu" ? "True" : "False"
  LicensingConf_disable_frontend_license      = "False"
  LicensingConf_disable_backend_license_check = "False"
  enable_vc                                   = var.is_xsoar_6_migration && var.customer_dev_tenant ? false : var.customer_dev_tenant
  is_uat                                      = length(regexall(".*uat", element(split(".", var.external_fqdn), 1))) > 0
  rbacconf_base_uri                           = local.is_uat ? var.rbacconf_base_uri_uat : var.rbacconf_base_uri
  logout_url                                  = format(replace(var.logout_url, "/^(https?://)([\\w|\\.]+)/(.*)/", "$1%s/$3"), var.external_fqdn)
  scylla_endpoint                             = var.new_scylla ? "scylla-client.scylla.svc" : "scylla-cluster-client.scylla.svc"
  xcloud_endpoint                             = var.new_scylla ? "scylla-xcloud-client.scylla-xcloud.svc" : "scylla-cluster-client.scylla-xcloud.svc"
  enrichment_endpoint                         = "ipl-scylla-client.scylla-iplen.svc"
  scylla_xcloud_endpoint                      = var.xcloud_standalone_deployment ? local.xcloud_endpoint : local.scylla_endpoint
  scylla_enrichment_endpoint                  = local.enrichment_endpoint
  xsoar_registry                              = var.is_fedramp ? "us-central1-docker.pkg.dev/xdr-xsoar-registry-${var.viso_env}-01" : "gcr.io/xsoar-registry"
  selective_propagation                       = var.xsoar_mssp_dev ? true : null
  dssconf_29_suffix                           = ""
  region_short_code                           = replace(var.viso_env, "prod-", "")
  rule_management_mt_bucket_name              = var.viso_env == "dev" ? "rule-management-dev" : var.viso_env == "prod-gv" ? "rule-management-prod-gv" : var.viso_env == "prod-fr" ? "rule-management-prod-fr" : "rule-management-prod"
  scan_results_pubsub_topic                   = "cwp-scan-results-${var.lcaas}"
  scan_results_bucket                         = "${var.project_id}-cwp-scan-results"
  temporal_address                            = "temporal-${var.lcaas}-frontend:7233"
  st_proxydome_ip_list = {
    dev     = "**************,*************"
    prod-au = "************,*************"
    prod-ca = "*************,**************"
    prod-ch = "*************,*************"
    prod-de = "**************,************"
    prod-es = "*************,************"
    prod-eu = "*************,************"
    prod-fa = "*************,************"
    prod-id = "*************,**************"
    prod-il = "*************,*************"
    prod-in = "*************,***********"
    prod-jp = "**************,************"
    prod-qt = "************,************"
    prod-sa = "*************,************"
    prod-sg = "*************,**************"
    prod-tw = "************,************"
    prod-uk = "**************,**************"
    prod-us = "***********,**************"
    prod-pr = "*************,************"
    prod-it = "*************,*************"
    prod-kr = "************,************"
    prod-za = "************,************"
  }
  harvester_ip_list = {
    dev     = "*************,**************"
    prod-au = "**************,*************"
    prod-ca = "***********,************"
    prod-ch = "*************,**********"
    prod-de = "************,************"
    prod-es = "*************,*************"
    prod-eu = "************,**************"
    prod-fa = "**************,**************"
    prod-fr = "************,*************"
    prod-id = "*************,*************"
    prod-il = "**************,**************"
    prod-in = "*************,***********"
    prod-it = "**************,*************"
    prod-jp = "************,************"
    prod-kr = "***********,*************"
    prod-pl = "*************,**************"
    prod-pr = "**************,************"
    prod-qt = "***********,************"
    prod-sa = "************,*************"
    prod-sg = "*************,*************"
    prod-tw = "*************,**************"
    prod-uk = "*************,**************"
    prod-us = "************,35.202.21.123"
    prod-za = "34.35.69.156,34.35.60.86"
  }
  dssconf_dss_url_map = {
    default = "https://app-directory-sync.${local.region_short_code}.apps.paloaltonetworks.com"
    dev     = "https://app-directory-sync-qa.us.qa.appsvc.paloaltonetworks.com"
    prod-eu = "https://app-directory-sync.eu.paloaltonetworks.com"
    prod-fa = "https://app-directory-sync.fr.apps.paloaltonetworks.com"
    prod-fr = "https://app-directory-sync.gov.apps.paloaltonetworks.com"
    prod-gv = "https://app-directory-sync.fed.apps.paloaltonetworks.com"
    prod-qt = "https://app-directory-sync.qa.apps.paloaltonetworks.com"
    prod-sg = "https://app-directory-sync.sg.paloaltonetworks.com"
    prod-uk = "https://app-directory-sync.uk.paloaltonetworks.com"
    prod-us = "https://app-directory-sync.us.paloaltonetworks.com"
    prod-pr = "https://app-directory-sync.us.paloaltonetworks.com"
  }
  region_abbr = {
    default = local.region_short_code
    prod-pr = "us"
    prod-fa = "fr"
    prod-fr = "us"
    prod-gv = "us"
    prod-qt = "qa"
    dev     = "us"
  }
  gvs_url_map = {
    default = "https://${local.region_short_code}gvs.wildfire.paloaltonetworks.com"
    dev     = "https://usgvs.wildfire.paloaltonetworks.com"
    prod-pr = "https://usgvs.wildfire.paloaltonetworks.com"
    prod-fa = "https://frgvs.wildfire.paloaltonetworks.com"
    prod-fr = "https://globalservice.wildfire.gov.paloaltonetworks.com"
    prod-gv = "https://govgvs.wildfire.paloaltonetworks.com"
    prod-qt = "https://qagvs.wildfire.paloaltonetworks.com"
  }
  xql_global_xql_content_bucket   = "global-xql-content-${var.viso_env == "dev" ? "qa" : var.is_fedramp ? var.viso_env : "prod"}"
  xdr_gateway_url                 = var.viso_env == "dev" ? "https://cortex-gateway-dev.paloaltonetworks.com" : var.viso_env == "prod-fr" ? "https://cortex-gateway-federal.paloaltonetworks.com" : var.viso_env == "prod-gv" ? "https://cortex-gateway-gov.paloaltonetworks.com" : "https://cortex-gateway.paloaltonetworks.com"
  xcloud_onboarding_s3_bucket_url = var.viso_env == "dev" ? "https://cortex-xdr-dev-onboarding-scripts.s3.us-east-2.amazonaws.com" : var.is_fedramp ? "https://cortex-xdr-gov-onboarding-scripts.s3-us-gov-east-1.amazonaws.com" : "https://cortex-xdr-onboarding-scripts.s3.us-east-2.amazonaws.com"
  # todo: prod-gv
  xcloud_onboarding_master_template_url = "${local.xcloud_onboarding_s3_bucket_url}/cortex-xdr-aws-master-ro-1.0.0.template"
  xcloud_onboarding_member_template_url = "${local.xcloud_onboarding_s3_bucket_url}/cortex-xdr-aws-member-ro-1.0.0.template"
  wif_sa                                = var.is_xsoar_6_migration && !var.is_xsoar_onprem_migration ? "{\"type\":\"external_account\",\"audience\":\"//iam.googleapis.com/${google_iam_workload_identity_pool_provider.aws-provider.0.name}\",\"subject_token_type\":\"urn:ietf:params:aws:token-type:aws4_request\",\"service_account_impersonation_url\":\"https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/${data.google_service_account.xsoar-migration-sa.0.email}:generateAccessToken\",\"token_url\":\"https://sts.googleapis.com/v1/token\",\"credential_source\":{\"environment_id\":\"aws1\",\"region_url\":\"http://***************/latest/meta-data/placement/availability-zone\",\"url\":\"http://***************/latest/meta-data/iam/security-credentials\",\"regional_cred_verification_url\":\"https://sts.{region}.amazonaws.com?Action=GetCallerIdentity&Version=2011-06-15\",\"imdsv2_session_token_url\":\"http://***************/latest/api/token\"}}" : ""
  tenant_endpoint_mysql                 = var.is_metro_tenant ? "xdr-mt-${var.metro_host_id}-mysql.xdr-mt.svc.cluster.local" : "xdr-st-${var.lcaas}-mysql"
  tenant_endpoint_scortex               = var.is_metro_tenant ? "xdr-mt-${var.metro_host_id}-scortex.xdr-mt.svc.cluster.local" : "xdr-st-${var.lcaas}-scortex"
  asm_management_mt_bucket_name         = var.viso_env == "dev" ? "asm-general-dev" : var.viso_env == "prod-gv" ? "xp-prod-gv-asm-general" : var.viso_env == "prod-fr" ? "xp-empty-no-perms-bucket" : "asm-general-prod"
  vip_cve_conf_bucket_name              = var.viso_env == "dev" ? "xpanse-policies-dev" : var.viso_env == "prod-gv" ? "xpanse-policies-prod-gv" : var.viso_env == "prod-fr" ? "xpanse-policies-prod-fr" : "xpanse-policies-prod"

  ads_environment_variables = {
    ADS_CONTROLLER_ASSUME_ROLE_ARN = {
      dev     = var.creation_date > local.march_20_2025_epoch ? "arn:aws:iam::************:role/pan/cwp-account-controller20250305140355883100000002" : "arn:aws:iam::************:role/pan/cwp-account-controller20240721132734044200000002"
      prod-pr = "arn:aws:iam::************:role/pan/cwp-account-controller20241029083210222000000002"
      prod-us = "arn:aws:iam::************:role/pan/cwp-account-controller20250213101717836900000002"
      prod-uk = "arn:aws:iam::************:role/pan/cwp-account-controller20250213101510267000000002"
      prod-tw = "arn:aws:iam::************:role/pan/cwp-account-controller20250213101308702900000002"
      prod-sg = "arn:aws:iam::************:role/pan/cwp-account-controller20250213101109251100000002"
      prod-sa = "arn:aws:iam::************:role/pan/cwp-account-controller20250213100810848100000002"
      prod-qt = "arn:aws:iam::************:role/pan/cwp-account-controller20250213100552436900000002"
      prod-pl = "arn:aws:iam::************:role/pan/cwp-account-controller20250213100146985300000002"
      prod-kr = "arn:aws:iam::************:role/pan/cwp-account-controller20250213095902851700000002"
      prod-jp = "arn:aws:iam::************:role/pan/cwp-account-controller20250213095702831900000002"
      prod-it = "arn:aws:iam::************:role/pan/cwp-account-controller20250213095452172100000002"
      prod-in = "arn:aws:iam::************:role/pan/cwp-account-controller20250213095253715100000002"
      prod-il = "arn:aws:iam::************:role/pan/cwp-account-controller20250213095050835000000002"
      prod-id = "arn:aws:iam::************:role/pan/cwp-account-controller20250213094840637200000002"
      prod-fa = "arn:aws:iam::************:role/pan/cwp-account-controller20250213094539954400000002"
      prod-eu = "arn:aws:iam::************:role/pan/cwp-account-controller20250213094329521500000002"
      prod-es = "arn:aws:iam::************:role/pan/cwp-account-controller20250213094137591600000002"
      prod-de = "arn:aws:iam::************:role/pan/cwp-account-controller20250213093938868500000002"
      prod-ch = "arn:aws:iam::************:role/pan/cwp-account-controller20250213093709215000000002"
      prod-ca = "arn:aws:iam::************:role/pan/cwp-account-controller20250213093504817600000002"
      prod-au = "arn:aws:iam::************:role/pan/cwp-account-controller20250213093249205100000002"
      prod-za = "arn:aws:iam::************:role/pan/cwp-account-controller20250324133230642700000002"
      prod-fr = "arn:aws:iam::************:role/pan/cwp-account-controller20250625142031916900000002"
      prod-gv = "arn:aws:iam::************:role/pan/cwp-account-controller20250626081722749600000002"
    }
    ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT = {
      dev     = "<EMAIL>"
      prod-pr = "<EMAIL>"
      prod-us = "<EMAIL>"
      prod-uk = "<EMAIL>"
      prod-tw = "<EMAIL>"
      prod-sg = "<EMAIL>"
      prod-sa = "<EMAIL>"
      prod-qt = "<EMAIL>"
      prod-pl = "<EMAIL>"
      prod-kr = "<EMAIL>"
      prod-jp = "<EMAIL>"
      prod-it = "<EMAIL>"
      prod-in = "<EMAIL>"
      prod-il = "<EMAIL>"
      prod-id = "<EMAIL>"
      prod-fa = "<EMAIL>"
      prod-eu = "<EMAIL>"
      prod-es = "<EMAIL>"
      prod-de = "<EMAIL>"
      prod-ch = "<EMAIL>"
      prod-ca = "<EMAIL>"
      prod-au = "<EMAIL>"
      prod-za = "<EMAIL>"
      prod-fr = "<EMAIL>"
      prod-gv = "<EMAIL>"
    }
    ADS_CONTROLLER_KMS_KEY_ALIAS = {
      dev     = "dev-pan-managed-encrypt-snapshots-customers-generic"
      prod-pr = "prod-pr-pan-managed-encrypt-snapshots-customers-generic"
      prod-us = "prod-us-pan-managed-encrypt-snapshots-customers-generic"
      prod-uk = "prod-uk-pan-managed-encrypt-snapshots-customers-generic"
      prod-tw = "prod-tw-pan-managed-encrypt-snapshots-customers-generic"
      prod-sg = "prod-sg-pan-managed-encrypt-snapshots-customers-generic"
      prod-sa = "prod-sa-pan-managed-encrypt-snapshots-customers-generic"
      prod-qt = "prod-qt-pan-managed-encrypt-snapshots-customers-generic"
      prod-pl = "prod-pl-pan-managed-encrypt-snapshots-customers-generic"
      prod-kr = "prod-kr-pan-managed-encrypt-snapshots-customers-generic"
      prod-jp = "prod-jp-pan-managed-encrypt-snapshots-customers-generic"
      prod-it = "prod-it-pan-managed-encrypt-snapshots-customers-generic"
      prod-in = "prod-in-pan-managed-encrypt-snapshots-customers-generic"
      prod-il = "prod-il-pan-managed-encrypt-snapshots-customers-generic"
      prod-id = "prod-id-pan-managed-encrypt-snapshots-customers-generic"
      prod-fa = "prod-fa-pan-managed-encrypt-snapshots-customers-generic"
      prod-eu = "prod-eu-pan-managed-encrypt-snapshots-customers-generic"
      prod-es = "prod-es-pan-managed-encrypt-snapshots-customers-generic"
      prod-de = "prod-de-pan-managed-encrypt-snapshots-customers-generic"
      prod-ch = "prod-ch-pan-managed-encrypt-snapshots-customers-generic"
      prod-ca = "prod-ca-pan-managed-encrypt-snapshots-customers-generic"
      prod-au = "prod-au-pan-managed-encrypt-snapshots-customers-generic"
      prod-za = "prod-za-pan-managed-encrypt-snapshots-customers-generic"
    }
    ADS_SNAPSHOT_CORTEX_KMS_KEY_ALIAS = {
      dev     = "dev-pan-managed-encrypt-snapshots-customers-generic"
      prod-pr = "prod-pr-pan-managed-encrypt-snapshots-customers-generic"
      prod-us = "prod-us-pan-managed-encrypt-snapshots-customers-generic"
      prod-uk = "prod-uk-pan-managed-encrypt-snapshots-customers-generic"
      prod-tw = "prod-tw-pan-managed-encrypt-snapshots-customers-generic"
      prod-sg = "prod-sg-pan-managed-encrypt-snapshots-customers-generic"
      prod-sa = "prod-sa-pan-managed-encrypt-snapshots-customers-generic"
      prod-qt = "prod-qt-pan-managed-encrypt-snapshots-customers-generic"
      prod-pl = "prod-pl-pan-managed-encrypt-snapshots-customers-generic"
      prod-kr = "prod-kr-pan-managed-encrypt-snapshots-customers-generic"
      prod-jp = "prod-jp-pan-managed-encrypt-snapshots-customers-generic"
      prod-it = "prod-it-pan-managed-encrypt-snapshots-customers-generic"
      prod-in = "prod-in-pan-managed-encrypt-snapshots-customers-generic"
      prod-il = "prod-il-pan-managed-encrypt-snapshots-customers-generic"
      prod-id = "prod-id-pan-managed-encrypt-snapshots-customers-generic"
      prod-fa = "prod-fa-pan-managed-encrypt-snapshots-customers-generic"
      prod-eu = "prod-eu-pan-managed-encrypt-snapshots-customers-generic"
      prod-es = "prod-es-pan-managed-encrypt-snapshots-customers-generic"
      prod-de = "prod-de-pan-managed-encrypt-snapshots-customers-generic"
      prod-ch = "prod-ch-pan-managed-encrypt-snapshots-customers-generic"
      prod-ca = "prod-ca-pan-managed-encrypt-snapshots-customers-generic"
      prod-au = "prod-au-pan-managed-encrypt-snapshots-customers-generic"
      prod-za = "prod-za-pan-managed-encrypt-snapshots-customers-generic"
    }
  }

  cwp_environment_variables = {
    CWP_AWS_SHARED_RESOURCES_ACCOUNT_ID = {
      dev     = var.viso_env == "dev" ? ( var.creation_date > local.march_20_2025_epoch ? "************" : "************") : "************"
      prod-pr = "************"
      prod-us = "************"
      prod-uk = "************"
      prod-tw = "************"
      prod-sg = "************"
      prod-sa = "************"
      prod-qt = "************"
      prod-pl = "************"
      prod-kr = "************"
      prod-za = "************"
      prod-jp = "************"
      prod-it = "************"
      prod-in = "************"
      prod-il = "************"
      prod-id = "************"
      prod-fa = "************"
      prod-eu = "************"
      prod-es = "************"
      prod-de = "************"
      prod-ch = "************"
      prod-ca = "************"
      prod-au = "************"
      prod-fr = "************"
      prod-gv = "************"
    }
  }
  march_20_2025_epoch = *************
}
