module "tool_workload_identity" {
  source = "../../modules/workload_identity"

  for_each = {
    for app, app_value in local.tools_apps :
    app => app_value
    if lookup(app_value, "enabled", true) && contains(keys(app_value), "workload_identity")
  }

  service_account_name = lookup(each.value, "service_account_name", null)
  mt_dedicated_group   = lookup(each.value, "mt_dedicated_group", false)
  project_id           = var.project_id
  wi_project_id        = local.workload_identity_project_id
  data                 = lookup(each.value, "workload_identity", {})
  namespace            = module.create_namespace.ns[local.st_namespace]
  viso_env             = var.viso_env

  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes
  }
}

module "create_tools_apps" {
  source               = "../../modules/app"
  for_each = {
    for app, app_value in local.tools_apps :
    app => app_value
    if lookup(app_value, "enabled", true)
  }
  app_name             = each.value.app_name
  cron_job             = lookup(each.value, "cron_job", {})
  deployment           = lookup(each.value, "deployment", {})
  only_cron            = lookup(each.value, "only_cron", false)
  hpa                  = lookup(each.value, "hpa", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns[local.st_namespace])
  pool_tenant_creation = var.pool_tenant_creation
  service              = lookup(each.value, "service", {})
  service_account_name = lookup(each.value, "service_account_name", null)
  project_id           = var.project_id
  region               = var.region
  secrets              = lookup(each.value, "secrets", {})
}

module "create_daemonsets" {
  source               = "../../modules/daemonset"
  for_each             = {
  for k, v in local.daemonsets:
        k => v
        if lookup(v, "enabled", true)
  }
  app_name             = each.value.app_name
  daemonset            = lookup(each.value, "daemonset", {})
  namespace            = lookup(each.value, "namespace", module.create_namespace.ns[local.st_namespace])
  service_account_name = lookup(each.value, "service_account_name", null)
}

resource "kubernetes_secret" "xdr-daemon-secret" {
  count = local.enable_xdr_agent_daemon ? 1 : 0
  metadata {
    name      = "cortex-docker-secret"
    namespace = local.xdr_agent_namespace
  }
  data = {
    ".dockerconfigjson" = base64decode(data.google_secret_manager_secret_version.xdr_daemon_dockersecret.secret_data)
  }
  type = "kubernetes.io/dockerconfigjson"
}

locals {
  tools_apps = {
    custom-metrics-stackdriver-adapter = {
      enabled = !var.is_metro_tenant
      app_name             = "custom-metrics-stackdriver-adapter"
      namespace            = local.custom_metrics_namespace
      service_account_name = "custom-metrics-stackdriver-adapter"
      deployment = {
        template_namespace = null
        node_selector = !var.small_epp ? local.dynamic_node_label : local.static_node_label
        host_network = true
        toleration = [{
          operator = "Exists"
        }]
        labels_override = {
          run     = "custom-metrics-stackdriver-adapter"
          k8s-app = "custom-metrics-stackdriver-adapter"
        }
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          name            = "pod-custom-metrics-stackdriver-adapter"
          image           = "${var.docker_image_gar_location}/golden-images/custom-metrics-stackdriver-adapter:${local.custom_metrics_stackdriver_adapter_version}"
          command         = concat(["/adapter", "--use-new-resource-model=true"], var.enable_external_metrics_cache ? ["--external-metric-cache-ttl=1m"] : [])
          requests = {
            cpu    = "50m"
            memory = "50Mi"
          }
          limits = {
            cpu      = "250m"
            memory   = "200Mi"
          }
        }]
      }
      service = {
        spec = {
          name      = "custom-metrics-stackdriver-adapter"
          namespace = local.custom_metrics_namespace
          labels = {
            run                             = "custom-metrics-stackdriver-adapter"
            k8s-app                         = "custom-metrics-stackdriver-adapter"
            "kubernetes.io/cluster-service" = "true"
            "kubernetes.io/name"            = "Adapter"
          }
          selector = {
            run     = "custom-metrics-stackdriver-adapter"
            k8s-app = "custom-metrics-stackdriver-adapter"
          }
          port = [{
            port        = 443
            target_port = 443
            protocol    = "TCP"
          }]
        }
      }
    }
    kube_state_metrics = {
      enabled = !var.is_metro_tenant
      app_name             = "kube-state-metrics-${var.lcaas}"
      namespace            = "kube-system"
      service_account_name = "kube-state-metrics"
      deployment = {
        labels_override                 = { k8s-app = "kube-state-metrics" }
        node_selector                   = !var.small_epp ? local.dynamic_node_label : local.static_node_label
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          name            = "kube-state-metrics"
          image           = "${var.docker_image_gar_location}/golden-images/kube-state-metrics:${local.kube_state_metrics_version}"
          ports           = [8080, 8081]
          limits = {
            cpu      = lookup(var.overrides, "kube-state-metrics_limit_cpu", "300m")
            memory   = lookup(var.overrides, "kube-state-metrics_limit_memory", "320Mi")
          }
          requests = {
            cpu    = lookup(var.overrides, "kube-state-metrics_request_cpu", var.small_epp ? "10m" : "150m")
            memory = lookup(var.overrides, "kube-state-metrics_request_memory", "50Mi")
          }
          readiness_probe = [{
            http_get = [{
              path = "/healthz"
              port = "8080"
            }]
            initial_delay_seconds = "5"
            timeout_seconds       = "5"
          }]
          }]
      }
      service = {
        spec = {
          name = "kube-state-metrics-${var.lcaas}"
          annotations = {
            "prometheus.io/scrape" = "true"
          }
          labels = {
            k8s-app = "kube-state-metrics"
          }
          selector = {
            k8s-app = "kube-state-metrics"
          }
          port = [{
            name        = "http-metrics"
            port        = 8080
            target_port = 8080
            protocol    = "TCP"
            }, {
            name        = "telemetry"
            port        = 8081
            target_port = 8081
            protocol    = "TCP"
          }]
        }
      }
    }
    prometheus = {
      enabled = !var.is_metro_tenant
      app_name             = "monitoring-${var.lcaas}-prometheus"
      namespace            = local.monitoring_namespace
      service_account_name = "prometheus"
      deployment = {
        metadata_annotations = {
          "configmap.reloader.stakater.com/reload" = "prometheus-server-conf"
        }
        node_selector  = !var.small_epp ? local.dynamic_node_label : local.static_node_label
        strategy_type                   = "Recreate"
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          image = local.enable_cortex_platform ? "${var.docker_image_gar_location}/golden-images/prom-tools/prometheus:v3.4.2" : "${var.docker_image_gar_location}/golden-images/prom-tools/prometheus:${local.prometheus_image_tag}"
          name  = "prometheus"
          args  = concat([
            "--config.file=/etc/prometheus/prometheus.yml",
            "--storage.tsdb.path=/prometheus",
            "--storage.tsdb.retention.size=0B",
            "--storage.tsdb.retention.time=12h",
            "--log.format=json",
          ],
          local.enable_cortex_platform ? ["--web.enable-otlp-receiver"] : []
          )
          ports = [9090]
          volume_mount = [{
              mount_path = "/etc/prometheus/"
              name       = "prometheus-config-volume"
            }]
          requests = {
            cpu    = lookup(var.overrides, "prometheus_request_cpu", var.megatron_xdr ? "2" : local.needs_large_spec ? "1" : var.small_epp ? "70m" : "0.3")
            memory = lookup(var.overrides, "prometheus_request_memory", var.megatron_xdr ? "12Gi" : local.needs_large_spec ? "8Gi" : var.small_epp ? "512Mi" : "1Gi")
          }
          limits = {
            cpu      = lookup(var.overrides, "prometheus_limit_cpu", var.megatron_xdr ? "3" : local.needs_large_spec ? "2" : "1")
            memory   = lookup(var.overrides, "prometheus_limit_memory", var.megatron_xdr ? "14Gi" : local.needs_large_spec ? "8Gi" : "2Gi")
          }
        }]
        volume_config_map = [{
          name         = "prometheus-config-volume"
          cm_name      = "prometheus-server-conf"
          default_mode = "0777"
        }]
        persistent_volume = []
        security_context = [{
          fs_group        = 65534
          run_as_group    = 65534
          run_as_user     = 65534
          run_as_non_root = true
        }]
      }
      service = {
        spec = {
          name = "monitoring-${var.lcaas}-prometheus"
          annotations = {
            "networking.gke.io/load-balancer-type" = "Internal"
          }
          port = [{
            port        = 8080
            protocol    = "TCP"
            target_port = "9090"
          }]
          load_balancer_ip = var.xdr_prometheus_loadbalancer_ip
          type             = "LoadBalancer"
        }
      }
    }
    prom_adapter ={
      enabled = !var.is_metro_tenant
      app_name             = "monitoring-${var.lcaas}-prom-adapter"
      namespace            = local.monitoring_namespace
      service_account_name = "monitoring-${var.lcaas}-prom-adapter"
      deployment = {
        metadata_annotations = {
          "configmap.reloader.stakater.com/reload" = "monitoring-${var.lcaas}-prom-adapter"
        }
        node_selector  = local.static_node_label
        automount_service_account_token = true
        containers = [{
          image = "${var.docker_image_gar_location}/golden-images/prom-tools/k8s-prometheus-adapter:v0.12.0"
          name  = "prometheus-adapter"
          args = [
            "/adapter",
            "--secure-port=443",
            "--cert-dir=/tmp/cert",
            "--prometheus-url=http://monitoring-${var.lcaas}-prometheus.monitoring.svc:8080",
            "--metrics-relist-interval=1m",
            "--v=10",
            "--config=/etc/adapter/config.yaml",
          ]
          ports = [443]
          security_context = true
          privileged   = false
          run_as_group = 0
          run_as_user  = 0
          volume_mount = [{
            mount_path = "/etc/adapter/"
            name       = "config"
          },{
            mount_path = "/tmp/"
            name       = "tmp"
          }]
          liveness_probe = [{
            http_get = [{
              path = "/healthz"
              port = 443
              scheme = "HTTPS"
            }]
            initial_delay_seconds = 30
          }]
          readiness_probe = [{
            http_get = [{
              path = "/healthz"
              port = 443
              scheme = "HTTPS"
            }]
            initial_delay_seconds = 30
          }]
          requests = {
            cpu    = "0.01"
            memory = "12Mi"
          }
          limits = {
            cpu      = "0.2"
            memory   = "256Mi"
          }
        }]
        volume_config_map = [{
          name         = "config"
          cm_name      = "monitoring-${var.lcaas}-prom-adapter"
          default_mode = "0777"
        }]
        volume_empty_dir = [{
          name = "tmp"
        }]
      }
      service = {
        spec = {
          name = "monitoring-${var.lcaas}-prom-adapter"
          annotations = {}
          port = [{
            port        = 443
            protocol    = "TCP"
            target_port = 443
          }]
          type             = "ClusterIP"
          selector = {
            app     = "monitoring-${var.lcaas}-prom-adapter"
          }
        }
      }
    }
    opentelemetry-collector = {
      enabled = local.enable_otel_collector
      app_name = "opentelemetry-collector"
      service_account_name = "opentelemetry-collector"
      namespace            = local.monitoring_namespace
      deployment = {
      metadata_annotations = {
          "configmap.reloader.stakater.com/reload" = "monitoring-${var.lcaas}-configmap-opentelemetry-collector"
        }
      annotations = {
          "prometheus.io/scrape" = "true"
          "prometheus.io/port"   = "8889"
          "prometheus.io/path"   = "/metrics"
        }
        node_selector                   = local.dynamic_node_label
        replicas                        = 1
        automount_service_account_token = true
        strategy_type                   = "RollingUpdate"
        containers = [{
          name              = "opentelemetry-collector"
          image             = "${var.docker_image_gar_location}/golden-images/otel:0.110.0-amd64"
          command = ["/otelcol-contrib"]
          args  = ["--config=/conf/relay.yaml"]
          ports = [8889,4317,4318]
          image_pull_policy = "IfNotPresent"
          requests = {
            cpu      = lookup(var.overrides, "otel_request_cpu", var.megatron_xdr ? "3" : local.needs_large_spec ? "2" : "1")
            memory   = local.otel_request_mem
          }
          limits = {
            cpu        = lookup(var.overrides, "otel_limit_cpu", var.megatron_xdr ? "3" : local.needs_large_spec ? "2" : "1")
            memory     = local.otel_limit_mem
          }
          liveness_probe = [{
            http_get = [{
              path = "/"
              port = 13133
            }]
            initial_delay_seconds = 30
          }]
          readiness_probe = [{
            http_get = [{
              path = "/"
              port = 13133
            }]
            initial_delay_seconds = 30
          }]
          env = [{
                name  = "GOMEMLIMIT"
                value = "${local.otel_gomemlimit}MiB"
              }
            ]
          fields = [{
                name       = "MY_POD_IP"
                field_path = "status.podIP"
          }]
          volume_mount = [{
            mount_path = "/conf"
            name       = "opentelemetry-collector-configmap"
          }]
        }]
        volume_config_map = [{
          name         = "opentelemetry-collector-configmap"
          cm_name      = "monitoring-${var.lcaas}-configmap-opentelemetry-collector"
          default_mode = "0777"
        }]
      }
      service = {
        spec = {
          name = "opentelemetry-collector"
          annotations = {
            "prometheus.io/scrape" = "true"
            "prometheus.io/port"   = "8889"
            "prometheus.io/path"   = "/metrics"
          }
          labels = {
            app = "opentelemetry-collector"
          }
          selector = {
            app = "opentelemetry-collector"
          }
          port = [{
            name        = "metrics"
            port        = 8889
            target_port = 8889
            protocol    = "TCP"
            }, {
            name        = "otlp"
            port        = 4317
            target_port = 4317
            protocol    = "TCP"
          },{
            name        = "otlp-http"
            port        = 4318
            target_port = 4318
            protocol    = "TCP"
          }]
        }
    }
    }
    reloader = {
      enabled = !var.is_metro_tenant
      app_name             = "reloader"
      service_account_name = "reloader"
      deployment = {
        node_selector                   = local.static_node_label
        replicas                        = 1
        automount_service_account_token = true
        containers = [{
          name              = "reloader"
          image             = "${var.docker_image_gar_location}/golden-images/stakater/reloader:v1.2.0"
          args  = [
            "--reload-strategy=annotations",
          ]
          image_pull_policy = "IfNotPresent"
          requests = {
            cpu      = "10m"
            memory   = "128Mi"
          }
          limits = {
            cpu        = "0.1"
            memory     = "512Mi"
          }
        }]
      }
    }
    elastic_search_exporter = {
      enabled = var.enable_xsoar_shared_components
      app_name = "elastic-es-xsoar-exporter"
      deployment = {
        node_selector = local.static_node_label
        annotations = {
          "prometheus.io/scrape" = "true"
          "prometheus.io/port"   = "9108"
        }
        containers = [{
          name  = "exporter"
          ports = [9108]
          image = "${var.docker_image_gar_location}/golden-images/elasticsearch-exporter:${local.elastic_exporter_image_tag}"
          command = [
            "elasticsearch_exporter",
            "--log.format=logfmt",
            "--log.level=info",
            "--es.uri=https://elastic-es-http:9200",
            "--es.all",
            "--es.indices",
            "--es.indices_settings",
            "--es.indices_mappings",
            "--es.shards",
            "--collector.snapshots",
            "--es.timeout=30s",
            "--es.ssl-skip-verify",
            "--web.listen-address=:9108",
            "--web.telemetry-path=/metrics"
          ]
          image_pull_policy = "IfNotPresent"
          env = [{
            name  = "ES_USERNAME"
            value = "elastic"
          }]
          secrets = [{
            name   = "ES_PASSWORD"
            secret = "elastic-es-elastic-user"
            key    = "elastic"
          }]
          liveness_probe = [{
            http_get = [{
              path = "/healthz"
              scheme = "HTTP"
              port   =  9108
            }]
            initial_delay_seconds = 5
            timeout_seconds       = 5
            period_seconds        = 5
          }]
          readiness_probe = [{
            http_get = [{
              path = "/healthz"
              scheme = "HTTP"
              port   =  9108
            }]
            initial_delay_seconds = 1
            timeout_seconds       = 5
            period_seconds        = 5
          }]
          lifecycle      = local.lifecycle_elastic
          requests = {
            cpu    = "10m"
            memory = "128Mi"
          }
          limits = {
            cpu    = "0.1"
            memory = "512Mi"
          }
        }]
      }
    }
    stackdriver_exporter_pod = {
      enabled = !var.is_metro_tenant
      app_name             = "stackdriver-exporter-${var.lcaas}"
      namespace            = local.monitoring_namespace
      service_account_name = "prometheus"
      deployment = {
        strategy_type = "Recreate"
        node_selector  = !var.small_epp ? local.dynamic_node_label : local.static_node_label
        annotations = {
          "prometheus.io/scrape" = "true"
        }
        labels_override = {
          app = "stackdriver-exporter"
        }
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          image = local.full_stackdriver_exporter_version
          name  = "stackdriver-exporter"
          ports = [9255]
          args  = sort([
            "--google.project-id=${var.project_id}",
            "--monitoring.metrics-type-prefixes=${local.stackdriver_metrics_list}",
            "--monitoring.metrics-interval=1m",
            "--monitoring.metrics-offset=10m"
          ])
          requests = {
            cpu    = lookup(var.overrides, "stackdriver_exporter_cpu_request", var.small_epp ? "0.01" : "50m")
            memory = lookup(var.overrides, "stackdriver_exporter_memory_request", var.small_epp ? "10Mi" : "50Mi")
          }
          limits = {
            cpu    = lookup(var.overrides, "stackdriver_exporter_cpu_limit", var.small_epp ? "0.02" : "100m")
            memory = lookup(var.overrides, "stackdriver_exporter_memory_limit", "100Mi")
          }
        }]
      }
    }
    stackdriver_exporter_pod_7h = {
      enabled       = var.enable_pipeline && !var.is_metro_tenant
      app_name             = "stackdriver-exporter-7h-${var.lcaas}"
      namespace            = local.monitoring_namespace
      service_account_name = "prometheus"
      deployment = {
        strategy_type = "Recreate"
        annotations = {
          "prometheus.io/scrape" = "true"
        }
        labels_override = {
          app = "stackdriver-exporter-7h"
        }
        node_selector  = !var.small_epp ? local.dynamic_node_label : local.static_node_label
        automount_service_account_token = true
        restart_policy                  = "Always"
        containers = [{
          image = local.full_stackdriver_exporter_version
          name  = "stackdriver-exporter-7h"
          ports = [9244]
          args  = sort([
            "--google.project-id=${var.project_id}",
            "--monitoring.metrics-type-prefixes=${local.stackdriver_metrics_7h_list}",
            "--monitoring.metrics-interval=1m",
            "--monitoring.metrics-offset=7h",
            "--web.listen-address=:9244"
          ])
          requests = {
            cpu    = lookup(var.overrides, "stackdriver_exporter_7h_cpu_request", var.small_epp ? "0.01" : "20m")
            memory = lookup(var.overrides, "stackdriver_exporter_7h_memory_request", var.small_epp ? "10Mi" : "50Mi")
          }
          limits = {
            cpu    = lookup(var.overrides, "stackdriver_exporter_7h_cpu_limit", var.small_epp ? "0.02" : "100m")
            memory = lookup(var.overrides, "stackdriver_exporter_7h_memory_limit", "100Mi")
          }
        }]
      }
    }
    association-replication = {
      enabled = (var.is_xpanse || var.enable_pipeline) && (var.pool_tenant_activation || ! var.pool_tenant)
      app_name             = "association-replication"
      only_cron = true
      service_account_name = "association-replication-job"
      mt_dedicated_group   = var.is_fedramp ? false : true
      # todo(yfried): here too?
      # Adding dev and beta flag for association-replication-cron-job
      cron_job = {
        spec = {
          automount_service_account_token = true
          concurrency_policy            = "Forbid"
          schedule                      = "*/30 * * * *"
          starting_deadline_seconds     = 20
          successful_jobs_history_limit = 4
          node_selector                 = local.dynamic_node_label
          backoff_limit                 = 0
          security_context = [{
            fs_group = "888"
          }]
          containers = [{
            image           = local.backend_image
            command         = ["/bin/sh", "/src/runner_error_handling.sh", "/src/secdo/app.py", "association_replication"]
            ports           = [8080]
            config_maps     = local.config_map_dml
            secrets         = [
              {
                name   = "MYSQLCONF_PASSWORD"
                secret = var.is_metro_tenant ? "association-replication-mysql" : "${var.lcaas}-secrets"
                key    = "mysql_password"
              },
              {
                name   = "REDISCONF_PASSWORD"
                secret = "${var.lcaas}-secrets"
                key    = "redis_password"
              },
              {
                name   = "ScyllaConf_password"
                secret = "${var.lcaas}-secrets"
                key    = "scylla_password"
              },
            ]
            env = [
              {
                name  = "ScyllaConf_user"
                value = local.scylla_username
              }, {
                name  = "ScyllaConf_force_disable_scylla"
                value = "False"
              }, {
                name  = "ScyllaConf_scylla_endpoint"
                value = local.scylla_endpoint
              }, {
                name  = "MYSQLCONF_USER"
                value = var.is_metro_tenant ? "${var.metro_tenant_index}-association_replication": "association_replication_cron_job"
              }
            ]
            liveness_probe  = [{
                exec = [{
                  command = ["/bin/sh"]
                }]
                period_seconds = 15
                initial_delay_seconds = 30
              }]
            requests        = {
              cpu    = "0.2"
              memory = "600Mi"
            }
            limits          = {
              cpu      = "0.3"
              memory   = "${min(local.base_ar_memory + local.base_ar_load, 2000) * 2}Mi"
            }
          }]
        }
      }
      workload_identity = {
        bq = [
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "temp_tables"), "roles/bigquery.dataEditor"],
        ]

        bq_job_user = true

       topics = [
         lookup(var.topics_output, "dp_uai_asset_observations_topic", ""),
       ]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-association_replication"
      }
    }
    kube_dns = {
      enabled                         = local.enable_custom_kube_dns
      app_name             = "kube-dns-${var.lcaas}"
      namespace            = "kube-system"
      service_account_name = "kube-dns-${var.lcaas}"
      deployment = {
        metadata_annotations            = {
          "components.gke.io/component-name"           = "kubedns"
          "prometheus.io/port"                         = "10054"
          "prometheus.io/scrape"                       = "true"
          "scheduler.alpha.kubernetes.io/critical-pod" = ""
          "seccomp.security.alpha.kubernetes.io/pod"   = "runtime/default"
        }
        labels_override                 = { k8s-app = "kube-dns" }
        node_selector                   = { "kubernetes.io/os" = "linux" }
        strategy_type                   = "RollingUpdate"
        strategy_max_surge              = "10%"
        strategy_max_unavailable        = "0"
        template_namespace              = null
        priority_class_name             = "system-cluster-critical"
        restart_policy                  = "Always"
        automount_service_account_token = true
        toleration                      = [{operator = "Exists", key = "CriticalAddonsOnly"},
                                           {operator = "Exists", key = "components.gke.io/gke-managed-components"},
                                           {operator = "Equal", effect = "NoSchedule", key = "kubernetes.io/arch", value = "arm64"}]
        containers = [{
          name              = "kubedns"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_image}"
          image_pull_policy = "IfNotPresent"
          security_context  = true
          privileged        = false
          run_as_group      = 1001
          run_as_user       = 1001
          allow_privilege_escalation      = false
          read_only_root_filesystem       = true
          named_ports = [{
            container_port = "10053"
            name           = "dns-local"
            protocol       = "UDP"
          },
          {
            container_port = "10053"
            name           = "dns-tcp-local"
            protocol       = "TCP"
          },
          {
            container_port = "10055"
            name           = "metrics"
            protocol       = "TCP"
          }]
          args = [
            "--domain=cluster.local.",
            "--dns-port=10053",
            "--config-dir=/kube-dns-config",
            "--v=2",
          ]
          volume_mount = [{
            mount_path = "/kube-dns-config"
            name       = "kube-dns-config"
          }]
          env = [{
            name  = "PROMETHEUS_PORT"
            value = "10055"
          }]
          limits = {
            memory   = "210Mi"
          }
          requests = {
            cpu    = "50m"
            memory = "70Mi"
          }
          readiness_probe = [{
            http_get = [{
              path = "/readiness"
              port = "8081"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 3
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 3
          }]
          liveness_probe = [{
            http_get = [{
              path = "/healthcheck/kubedns"
              port = "10054"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 60
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 5
          }]
        },
        {
          name              = "dnsmasq"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_dnsmasq_image}"
          image_pull_policy = "IfNotPresent"
          named_ports = [{
            container_port = "53"
            name           = "dns"
            protocol       = "UDP"
          },
          {
            container_port = "53"
            name           = "dns-tcp"
            protocol       = "TCP"
          }]
          args = [
            "-v=2",
            "-logtostderr",
            "-configDir=/etc/k8s/dns/dnsmasq-nanny",
            "-restartDnsmasq=true",
            "--",
            "-k",
            "--cache-size=1000",
            "--no-negcache",
            "--dns-forward-max=1500",
            "--log-facility=-",
            "--server=/cluster.local/127.0.0.1#10053",
            "--server=/in-addr.arpa/127.0.0.1#10053",
            "--server=/ip6.arpa/127.0.0.1#10053",
            "--max-ttl=30",
            "--max-cache-ttl=30",
          ]
          volume_mount = [{
            mount_path = "/etc/k8s/dns/dnsmasq-nanny"
            name       = "kube-dns-config"
          }]
          requests = {
            cpu    = "50m"
            memory = "20Mi"
          }
          security_context = true
          privileged       = false
          capabilities     = true
          drop = [ "all" ]
          add = [
            "NET_BIND_SERVICE",
            "SETGID"
          ]
          liveness_probe = [{
            http_get = [{
              path = "/healthcheck/dnsmasq"
              port = "10054"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 60
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 5
          }]
        },
        {
          name              = "sidecar"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_sidecar_image}"
          image_pull_policy = "IfNotPresent"
          security_context  = true
          privileged        = false
          run_as_group      = 1001
          run_as_user       = 1001
          allow_privilege_escalation = false
          read_only_root_filesystem  = true
          named_ports = [{
            container_port = "10054"
            name           = "metrics"
            protocol       = "TCP"
          }]
          args = [
            "--v=2",
            "--logtostderr",
            "--probe=kubedns,127.0.0.1:10053,kubernetes.default.svc.cluster.local,5,SRV",
            "--probe=dnsmasq,127.0.0.1:53,kubernetes.default.svc.cluster.local,5,SRV",
          ]
          requests = {
            cpu    = "10m"
            memory = "20Mi"
          }
          liveness_probe = [{
            http_get = [{
              path = "/metrics"
              port = "10054"
              scheme = "HTTP"
            }]
            initial_delay_seconds = 60
            timeout_seconds       = 5
            period_seconds        = 10
            success_threshold     = 1
            failure_threshold     = 5
          }]
        }]
        volume_config_map = [{
          name         = "kube-dns-config"
          cm_name      = "kube-dns"
          default_mode = "0420"
          optional     = true
        }]
        security_context = [{
            fs_group            = "65534"
            supplemental_groups = [
              "65534"
            ]
        }]
        dns_policy = "Default"
        affinity = [{
          pod_anti_affinity = [{
            preferred_during_scheduling_ignored_during_execution = [{
              weight                                    = 100
              topology_key                              = "kubernetes.io/hostname"
              label_selector_match_expressions_key      = "k8s-app"
              label_selector_match_expressions_operator = "In"
              label_selector_match_expressions_values   = ["kube-dns"]
            }]
          }]
        }]
      }
    }
    kube_dns_autoscaler = {
      enabled                         = local.enable_custom_kube_dns
      app_name             = "kube-dns-autoscaler-${var.lcaas}"
      namespace            = "kube-system"
      service_account_name = "kube-dns-autoscaler-${var.lcaas}"
      deployment = {
        metadata_annotations            = {
          "seccomp.security.alpha.kubernetes.io/pod"           = "docker/default"
        }
        labels_override                 = { k8s-app = "kube-dns-autoscaler-${var.lcaas}" }
        node_selector                   = { "kubernetes.io/os" = "linux" }
        template_namespace              = null
        priority_class_name             = "system-cluster-critical"
        toleration                      = [{operator = "Exists", key = "CriticalAddonsOnly"},
                                           {operator = "Exists", key = "components.gke.io/gke-managed-components"},]
        automount_service_account_token = true
        containers = [{
          name              = "autoscaler"
          image             = "${var.docker_image_gar_location}/golden-images/${local.custom_kube_dns_autoscaler_image}"
          image_pull_policy = "IfNotPresent"
          command = [
            "/cluster-proportional-autoscaler",
            "--namespace=kube-system",
            "--configmap=kube-dns-autoscaler",
            "--target=Deployment/kube-dns-${var.lcaas}",
            "--default-params={\"linear\":{\"coresPerReplica\":${lookup(var.overrides, "kube_dns_cores_per_replica", 256)},\"nodesPerReplica\":${lookup(var.overrides, "kube_dns_nodes_per_replica", 16)},\"preventSinglePointFailure\":true,\"includeUnschedulableNodes\":true}}",
            "--logtostderr=true",
            "--v=2",
          ]
          requests = {
            cpu    = "20m"
            memory = "10Mi"
          }
        }]
        security_context = [{
            fs_group             = "65534"
            supplemental_groups  = [
              "65534"
            ]
        }]
      }
    }
  }
  daemonsets = {
    fuse = {
      enabled              = var.enable_xsoar_shared_components
      app_name             = "fuse-device-plugin-daemonset"
      namespace            = "kube-system"
      service_account_name = ""
      daemonset = {
        node_selector = local.xsoar_node_label
        host_network = true
        toleration = local.xsoar_toleration
        labels_override = {
          name     = "fuse-device-plugin-ds"
        }
        restart_policy                  = "Always"
        containers = [{
          name            = "fuse-device-plugin-ctr"
          image           = local.fuse_image
          security_context = true
          privileged = false
          drop = ["all"]
          volume_mount = [{
            mount_path = "/var/lib/kubelet/device-plugins"
            name       = "device-plugin"
          }]
        }]
        volume = [{
          name = "device-plugin"
          path = "/var/lib/kubelet/device-plugins"
        }]
        image_pull_secrets = [{
          name = "registry-secret"
        }]
      }
    },
    fuse_migration = {
      enabled              = var.is_xsoar_6_migration
      app_name             = "fuse-device-plugin-daemonset-migration"
      namespace            = "kube-system"
      service_account_name = ""
      daemonset = {
        node_selector = local.xsoar_migration_node_label
        host_network = true
        toleration = local.xsoar_migration_toleration
        labels_override = {
          name     = "fuse-device-plugin-ds"
        }
        restart_policy                  = "Always"
        containers = [{
          name            = "fuse-device-plugin-ctr"
          image           = local.fuse_image
          security_context = true
          privileged = false
          drop = ["all"]
          volume_mount = [{
            mount_path = "/var/lib/kubelet/device-plugins"
            name       = "device-plugin"
          }]
        }]
        volume = [{
          name = "device-plugin"
          path = "/var/lib/kubelet/device-plugins"
        }]
        image_pull_secrets = [{
          name = "registry-secret"
        }]
      }
    },
    xdr-agent-daemon = {
      enabled              = local.enable_xdr_agent_daemon
      app_name             = "xdr-agent-daemon"
      namespace            = local.xdr_agent_namespace
      service_account_name = "xdr-agent-user"
      termination_grace_period_seconds = 90
      daemonset = {
        node_selector = {"kubernetes.io/os" = "linux"}
        host_network = true
        host_pid = true
        host_ipc = true
        annotations = {"container.apparmor.security.beta.kubernetes.io/cortex-agent" = "unconfined"}
        toleration = [{ operator = "Exists" , effect = "NoSchedule"},
                      {operator = "Exists" , effect = "PreferNoSchedule"},
                      {operator = "Exists" , effect = "NoExecute"}]
        meta_labels_override = {
          "app.kubernetes.io/name" = "cortex-agent",
          "app.kubernetes.io/part-of" = "cortex",
          "app.kubernetes.io/component" = "agent" ,
          "app.kubernetes.io/version" = local.xdr_agent_daemon_version
          }
        labels_override = {
          "app.kubernetes.io/name" = "cortex-agent"
        }
        restart_policy                  = "Always"
        priority_class_name             = "xdr-agent-priority"
        containers = [{
          name            = "cortex-agent"
          image           = "${local.xdr_agent_daemon_registry}:${local.xdr_agent_daemon_version}"
          security_context = true
          privileged = false
          add = ["SYS_ADMIN",
                 "SYS_CHROOT",
                "SYS_MODULE",
                "SYS_PTRACE",
                "SYS_RESOURCE",
                "SYS_RAWIO",
                "DAC_OVERRIDE",
                "DAC_READ_SEARCH",
                "NET_ADMIN",
                "NET_RAW",
                "IPC_LOCK",
                "FOWNER",
                "KILL",
                "SETGID",
                "SETUID"]
          requests = {
            cpu    = lookup(var.overrides, "xdr_agent_daemon_cpu_request", "100m")
            memory = lookup(var.overrides, "xdr_agent_daemon_memory_request", "128Mi")
          }
          limits = {
            cpu    = lookup(var.overrides, "xdr_agent_daemon_cpu_limit", "1000m")
            memory = lookup(var.overrides, "xdr_agent_daemon_memory_limit", "4Gi")
          }
          env = [
            { name = "XDR_CLUSTER_NAME_URL", value = "metadata2" },
            { name = "XDR_HOST_ROOT", value = "/host-fs" },
            { name = "XDR_POD_INFO", value = "/var/run/pod-info" },
            { name = "XDR_PROXY_LIST", value = "************:13128" }
          ]
          volume_mount = [{
            mount_path = "/host-fs"
            name       = "host-fs"
            read_only  = true
          },
          {
            mount_path = "/var/log"
            name       = "var-log"
          },
          {
            mount_path = "/lib/modules"
            name       = "host-km-directory"
          },
          {
            mount_path = "/var/run/pod-info"
            name       = "pod-info"
            read_only  = true
          },
          {
            mount_path = "/etc/traps"
            name       = "agent-ids"
          },
          {
            mount_path = "/opt/traps/config/deployment"
            name       = "deployment-secrets"
            read_only  = true
          }]
        }]
        volume = [{
          name = "host-fs"
          path = "/"
          type = "Directory"
        },
        {
          name = "var-log"
          path = "/var/log"
          type = "Directory"
        },
        {
          name = "host-km-directory"
          path = "/lib/modules"
          type = "Directory"
        },
        {
          name = "agent-ids"
          path = "/etc/traps"
          type = "DirectoryOrCreate"
        }]
        downwardApi_volume = [
          {
            name = "pod-info"
            items = [{path = "uid", field_path = "metadata.uid"},
                     {path = "name", field_path = "metadata.name"},
                     {path = "labels", field_path = "metadata.labels"},
                     {path = "annotations", field_path = "metadata.annotations"}]
          }
        ]
        secret_volume = [
          {
            name = "deployment-secrets"
            secret_name = "xdr-agent-deployment"
          }
        ]
        image_pull_secrets = [{
          name = "cortex-docker-secret"
        }]
      }
    }
    disable-health-monitor = {
      enabled              = local.disable_health_monitor
      app_name             = "disable-health-monitor"
      namespace            = "kube-system"
      daemonset = {
        node_selector = {"kubernetes.io/os" = "linux"}
        host_pid = true
        volume = [{
          name = "host"
          path = "/"
          type = ""
        }]
        init_containers = [{
          name                       = "startup-script"
          image                      = local.disable_health_monitor_image
          security_context           = true
          privileged                 = true
          allow_privilege_escalation = true
          env = [{
            name = "STARTUP_SCRIPT",
            value = <<-SCRIPT
              set -o errexit
              set -o pipefail
              set -o nounset
              echo "Stopping kubelet health-monitor"
              chroot /host nsenter -a -t1 -- systemctl stop kubelet-monitor.service

              echo "Disabling kubelet health-monitor"
              chroot /host nsenter -a -t1 -- systemctl disable kubelet-monitor.service

              echo "Stopping containerd health-monitor"
              chroot /host nsenter -a -t1 -- systemctl stop kube-container-runtime-monitor.service

              echo "Disabling containerd health-monitor"
              chroot /host nsenter -a -t1 -- systemctl disable kube-container-runtime-monitor.service
            SCRIPT
          }]
          command = [
            "/bin/bash",
            "-c",
            <<-SCRIPT
            do_startup_script() {
              local err=0;
              bash -c "$STARTUP_SCRIPT" && err=0 || err=$?
              if [[ $err != 0 ]]; then
                echo "!!! startup-script failed! exit code '$err'" 1>&2
                return 1
              fi
              echo "!!! startup-script succeeded!" 1>&2
              return 0
            }

            do_startup_script
            SCRIPT
          ]
          volume_mount = [
            {
              mount_path = "/host"
              name       = "host"
            }
          ]
        }]
        containers = [{
          name                       = "pause"
          image                      = local.disable_health_monitor_pause_image
        }]
      }
    }
  }
  prom_stackdriver_metrics_epp = [
    "pubsub.googleapis.com/subscription/ack_message_count",
    "pubsub.googleapis.com/subscription/num_undelivered_messages",
    "pubsub.googleapis.com/subscription/oldest_unacked_message_age",
    "pubsub.googleapis.com/topic/send_message_operation_count",
    "logging.googleapis.com/byte_count",
    "logging.googleapis.com/log_entry_count",
    "monitoring.googleapis.com/uptime_check/check_passed"
  ]
  prom_stackdriver_metrics_full = [
    "bigquery.googleapis.com/slots/allocated_for_project_and_job_type",
    "bigquery.googleapis.com/query/count"
  ]
  prom_stackdriver_metrics_7h = [
    "bigquery.googleapis.com/storage/uploaded_bytes",
    "bigquery.googleapis.com/storage/uploaded_row_count",
    "bigquery.googleapis.com/storage/stored_bytes"
  ]
  stackdriver_metrics_list = contains(["small_epp", "epp"], var.product_tier) ? join(",", local.prom_stackdriver_metrics_epp) : join(",", concat(local.prom_stackdriver_metrics_epp, local.prom_stackdriver_metrics_full))
  stackdriver_metrics_7h_list = join(",", local.prom_stackdriver_metrics_7h)
  log_forwarding_namespace = var.viso_env == "dev" && var.is_perf_tenant ? "performance-log-forwarding" : "${var.viso_env}-log-forwarding"
  base_ar_memory = 600
  base_ar_load = floor((var.pro_agents / 65 + var.tb_licenses) / 100) * 100
  fuse_image = "${var.docker_image_gar_location}/cortex-xdr/fuse-plugin/fuse-device-plugin:v7"
  stackdriver_exporter_version = lookup(var.overrides, "stackdriver_exporter_version", "v0.17.0")
  full_stackdriver_exporter_version = "${var.docker_image_gar_location}/golden-images/stackdriver-exporter:${local.stackdriver_exporter_version}"
  xdr_agent_daemon_registry = var.viso_env == "prod-fr" ? "us-central1-docker.pkg.dev/xdr-fr-**********/agent-docker/cortex-agent" :var.viso_env == "prod-gv" ? "us-central1-docker.pkg.dev/xdr-gv-1908378801456/agent-docker/cortex-agent": var.viso_env == "dev" ? "us-central1-docker.pkg.dev/xdr-us-24072002/agent-docker/cortex-agent" : "us-central1-docker.pkg.dev/xdr-us-24072002/agent-docker/cortex-agent"
  xdr_agent_daemon_version = lookup(var.overrides, "xdr_agent_daemon_version", var.viso_env == "prod-gv" ? "8.6.1.130921" : var.viso_env == "prod-fr" ? "8.6.1.129214" : "8.7.100.136016")
  kube_state_metrics_version = "v2.13.0"
  custom_metrics_stackdriver_adapter_version = "v0.16.1-gke.0"
}

resource "helm_release" "twistlock-defender" {
  count     = lower(var.enable_twistlock) ? 1 : 0
  name      = "twistlock-defender-${var.lcaas}"
  namespace = module.create_namespace.ns["twistlock"]
  chart     = "${path.module}/files/helm/twistlock-defenders/${var.viso_env}"
  provider  = helm.xdr
  timeout   = "360"
  depends_on = [kubernetes_manifest.scaledobject_ns_lf_scaledobject]

  set {
    name  = "cluster_id"
    value = var.twistlock_defender_cluster_id
  }

  set {
    name  = "service_parameter"
    value = base64encode(data.google_secret_manager_secret_version.twistlock_defenders_service_parameter[0].secret_data)
  }

  set {
    name  = "ws_address"
    value = var.twistlock_defender_ws_address
  }

  set {
    name  = "ca_cert"
    value = base64encode(data.google_secret_manager_secret_version.twistlock_defenders_ca[0].secret_data)
  }

  set {
    name  = "client_cert"
    value = base64encode(data.google_secret_manager_secret_version.twistlock_defenders_client_cert[0].secret_data)
  }

  set {
    name  = "client_key"
    value = base64encode(data.google_secret_manager_secret_version.twistlock_defenders_client_key[0].secret_data)
  }

  set {
    name  = "admission_key"
    value = base64encode(data.google_secret_manager_secret_version.twistlock_defenders_admission_key[0].secret_data)
  }

  set {
    name  = "admission_cert"
    value = base64encode(data.google_secret_manager_secret_version.twistlock_defenders_admission_cert[0].secret_data)
  }
}

resource "helm_release" "open_telemetry_collector" {
  count      = local.enable_cortex_platform ? 1 : 0
  chart      = "${path.module}/files/helm/opentelemetry-collector"
  provider   = helm.xdr
  name       = "opentelemetry-collector-agentless"
  namespace  = local.monitoring_namespace
  values     = [yamlencode({
    command = {
      name = "otelcol-contrib"
    }
    image = {
      repository = "${var.docker_image_gar_location}/golden-images/otel/opentelemetry-collector-contrib"
      tag = "0.115.1-amd64"
    }
    mode = "deployment"
    replicaCount = 0
    resources = {
      limits = {
        cpu = lookup(var.overrides, "opentelemetry-collector_cpu", "1")
        memory = lookup(var.overrides, "opentelemetry-collector_memory", "1Gi")
      }
    }
    serviceAccount = {
      create = false
      name = "otel-collector-agentless"
    }
    config = {
      exporters = {
        debug = {
          verbosity = "detailed"
        }
        otlphttp = {
          endpoint = "http://monitoring-${var.lcaas}-prometheus.monitoring.svc.cluster.local:8080"
          metrics_endpoint = "http://monitoring-${var.lcaas}-prometheus.monitoring.svc.cluster.local:8080/api/v1/otlp/v1/metrics"
          tls = {
            insecure = true
          }
        }
      }
      receivers = {
        googlecloudpubsub = {
          encoding = "otlp_proto_metric"
          project = var.project_id
          subscription = "projects/${var.project_id}/subscriptions/cwp-sp-bc-metrics-${var.lcaas}-sub"
        }
      }
      service = {
        pipelines = {
          metrics = {
            exporters = ["otlphttp", "debug"]
            receivers = ["googlecloudpubsub"]
          }
        }
      }
    }
  })]
}

module "open_telemetry_workload_identity" {
  source               = "../../modules/workload_identity"
  count                = local.enable_cortex_platform ? 1 : 0
  service_account_name = "otel-collector-agentless"
  mt_dedicated_group   = false
  project_id           = var.project_id
  wi_project_id        = local.workload_identity_project_id
  data                 = {
    subscriptions = [
      lookup(var.subscriptions_output, "cwp_sp_bc_metrics_sub", "")
    ]
  }
  namespace            = module.create_namespace.ns[local.monitoring_namespace]
  viso_env             = var.viso_env
  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes
  }
}

resource "kubernetes_api_service" "custom-metrics-api-service" {
  count = var.is_metro_tenant ? 0 : 1
  depends_on = [module.create_tools_apps]
  metadata {
    name = "v1beta1.external.metrics.k8s.io"

    annotations = {
      app = "custom-metrics-stackdriver-adapter"
    }
  }

  spec {
    service {
      namespace = local.custom_metrics_namespace
      name      = "custom-metrics-stackdriver-adapter"
    }

    group                    = "external.metrics.k8s.io"
    version                  = "v1beta1"
    insecure_skip_tls_verify = true
    group_priority_minimum   = 100
    version_priority         = 100
  }
}

resource "kubernetes_api_service" "prom-adapter-api-service" {
  count = var.is_metro_tenant ? 0 : 1
  depends_on = [module.create_tools_apps]
  metadata {
    name = "v1beta1.custom.metrics.k8s.io"

    annotations = {
      app = "prom-adapter"
    }
  }

  spec {
    service {
      namespace = local.monitoring_namespace
      name      = "monitoring-${var.lcaas}-prom-adapter"
    }

    group                    = "custom.metrics.k8s.io"
    version                  = "v1beta1"
    insecure_skip_tls_verify = true
    group_priority_minimum   = 100
    version_priority         = 100
  }
}

resource "google_cloud_identity_group_membership" "gmp_sa_group_membership" {
  count = local.enable_gmp ? 1 : 0
  provider = google.mt
  group = var.viso_env == "dev" ? "groups/04anzqyu42c7uqb" : "groups/03bj1y38268x7em"
  create_ignore_already_exists = true
  preferred_member_key {
    id = "prometheus@${var.project_id}.iam.gserviceaccount.com"
  }
  roles {
    name = "MEMBER"
  }
}

resource "google_project_iam_member" "mng_tenant_permission" {
  count    = local.enable_gmp ? 1 : 0
  project  = var.project_id
  role     = "roles/monitoring.metricWriter"
  member   = "serviceAccount:prometheus@${var.project_id}.iam.gserviceaccount.com"
}
