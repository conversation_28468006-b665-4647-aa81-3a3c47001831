data "terraform_remote_state" "xsoar-ng-parent-state" {
  count = var.customer_dev_tenant ? 1 : 0
  backend = "gcs"
  workspace = var.parent_project_id

  config = {
    bucket  = "xdr-viso-${lower(var.xdr_env)}"
    prefix  = "terraform13/state"
  }
}

data "google_secret_manager_secret_version" "inproducthelpconf_help_api_token" {
  secret = "inproducthelpconf_help_api_token"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "dss_crt" {
  secret = var.viso_env == "dev" ? "dev_dss_pem" : (var.viso_env == "prod-pr" ? "prod_pr_dss_pem" : "dss_pem")
  project = var.certs_project
}

data "google_secret_manager_secret_version" "dss_key" {
  secret = var.viso_env == "dev" ? "dev_dss_key" : "dss_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "dss_server_crt" {
  secret = var.viso_env == "dev" ? "dss_server_cert" : "dss_server_cert_prod"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "jwt_crt" {
  secret = "jwt_cert"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "pandb_api_key_2" {
  count   = var.is_fedramp ? 0 : 1
  secret  = "pandb_api_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "wf_verdict_service_crt" {
  secret = "wf_verdict_service_crt"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "wf_verdict_service_key" {
  secret = "wf_verdict_service_key"
  project = var.certs_project
}


data "google_secret_manager_secret_version" "scripts_public_key" {
  secret = "scripts_public_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "scripts_public_key_prod" {
  secret = "scripts_public_key_prod"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "harvester_shared_key" {
  secret = "harvester-${var.viso_env}-harvester-password"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "whoisapi_key" {
  secret = "whoisapi_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "twistlock_defenders_ca" {
  count     = var.is_fedramp ? 1 : 0
  secret = "twistlock-defenders-${var.viso_env}-ca"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "twistlock_defenders_service_parameter" {
  count     = var.is_fedramp ? 1 : 0
  secret = "twistlock-defenders-${var.viso_env}-service-parameter"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "twistlock_defenders_client_cert" {
  count     = var.is_fedramp ? 1 : 0
  secret = "twistlock-defenders-${var.viso_env}-client-cert"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "twistlock_defenders_client_key" {
  count     = var.is_fedramp ? 1 : 0
  secret = "twistlock-defenders-${var.viso_env}-client-key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "twistlock_defenders_admission_key" {
  count     = var.is_fedramp ? 1 : 0
  secret = "twistlock-defenders-${var.viso_env}-admission-key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "twistlock_defenders_admission_cert" {
  count     = var.is_fedramp ? 1 : 0
  secret = "twistlock-defenders-${var.viso_env}-admission-cert"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "egress_proxy-ca" {
  secret = "${replace(var.viso_env,"-","_")}_egress_proxy_ca_crt"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "publicapi_encryption_key" {
  secret = "publicapi_encryption_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "analytics_conf_salt_key" {
  secret = "analytics_conf_salt_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "aws_connector_secret" {
  secret = "aws_connector_secret_${local.env_type}"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "aws_connector_key" {
  secret = "aws_connector_key_${local.env_type}"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "sendgrid_api_key" {
  secret = "sendgrid_api_key"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "mailexchangerouter_sensitive_data_key_2" {
  count = var.is_fedramp ? 0 : 1
  secret = "prod_us_mailexchangerouter_sensitive_data_key"
  project = var.certs_project
}

# gonzo_slack_token
data "google_secret_manager_secret_version" "be_error_reporting_slack_token" {
  secret = "be_error_reporting_slack_token"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "apple_push_key" {
  secret = var.viso_env == "dev" ? "apple_push_key" : "apple_push_key_prod"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "apple_push_key_id" {
  secret = var.viso_env == "dev" ? "apple_push_key_id" : "apple_push_key_id_prod"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "xdr_daemon_dockersecret" {
  secret = "xdr-daemon-dockersecret-${var.multi_project_postfix}"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "xdr_daemon_distributionid" {
  secret = "xdr-daemon-distributionid-${var.multi_project_postfix}"
  project = var.certs_project
}

data "google_compute_addresses" "engine0_external" {
    # GNS provisioned the engine FWs on some regions as "ipv4-address-perimeter-transit-fw01" and some as ipv4-address-perimeter-fw01
    filter = var.is_fedramp ? "name:us-central1-*" : "name:ipv4-address-perimeter-*"
    project = "xdr-shared-engines-${var.viso_env}-01"
    provider = google.mt
}
data "google_secret_manager_secret_version" "singlestore_license_key" {
  count   = 1
  secret  = "singlestore_license_key"
  project = var.certs_project
}
data "google_secret_manager_secret_version" "email_authorization_flow_client_id" {
  count   = var.is_fedramp  ? 0 : 1
  secret  = "${var.multi_project_postfix}_EMAILO365AUTHORIZATIONFLOW_CLIENT_ID"
  project = var.certs_project
}
data "google_secret_manager_secret_version" "email_authorization_flow_client_secret" {
  count   = var.is_fedramp  ? 0 : 1
  secret  = "${var.multi_project_postfix}_EMAILO365AUTHORIZATIONFLOW_CLIENT_SECRET"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "apisec_uai_api_key" {
  project = var.certs_project
  secret = var.viso_env == "dev" ? "apisec_uai_api_key_dev" : "apisec_uai_api_key_prod"
}

data "google_secret_manager_secret_version" "github_client_id" {
  secret  = "${replace(var.viso_env,"-","_")}_github_client_id"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "github_client_secret" {
  secret  = "${replace(var.viso_env,"-","_")}_github_client_secret"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "github_app_id" {
  secret  = "${replace(var.viso_env,"-","_")}_github_app_id"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "github_app_pem" {
  secret  = "${replace(var.viso_env,"-","_")}_github_app_pem"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "github_app_name" {
  secret  = "${replace(var.viso_env,"-","_")}_github_app_name"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "outpost_orchestrator_ready_job_evaluator_secret_key" {
  secret = "outpost_orchestrator_ready_job_evaluator"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "github_webhook_token" {
  secret  = "github_webhook_token"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "azure_repos_client_secret" {
  secret  = "${replace(var.viso_env,"-","_")}_azure_repos_client_secret"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "azure_repos_app_secret" {
  secret  = "${replace(var.viso_env,"-","_")}_azure_repos_app_secret"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "azure_repos_app_id" {
  secret  = "${replace(var.viso_env,"-","_")}_azure_repos_app_id"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "azure_repos_app_name" {
  secret  = "${replace(var.viso_env,"-","_")}_azure_repos_app_name"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "bitbucket_client_id" {
  secret  = "${replace(var.viso_env,"-","_")}_bitbucket_client_id"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "bitbucket_client_secret" {
  secret  = "${replace(var.viso_env,"-","_")}_bitbucket_client_secret"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "gitlab_app_client_id" {
  secret  = "${replace(var.viso_env,"-","_")}_gitlab_app_client_id"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "gitlab_app_secret" {
  secret  = "${replace(var.viso_env,"-","_")}_gitlab_app_secret"
  project = var.certs_project
}

data "google_secret_manager_secret_version" "hydra_encryption_key" {
  count   = 1
  secret  = "hydra_encryption_key"
  project = var.certs_project
}