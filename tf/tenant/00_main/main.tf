module "bootstrap" {
  source                = "../01_bootstrap"
  billing_account       = var.billing_account
  csp_id                = var.csp_id
  disposable            = var.disposable
  folder_id             = local.folder_id
  host_project          = var.host_project
  is_fedramp            = local.is_fedramp
  lcaas                 = var.lcaas
  license_expiration    = var.license_expiration
  overrides             = var.overrides
  owner                 = lower(var.owner)
  owner_group           = lower(var.owner_group)
  product_type          = local.product_type
  product_tier          = local.product_tier
  project_id            = var.project_id
  project_prefix        = var.project_prefix
  region                = var.region
  creation_date         = var.creation_date
  tenant_type           = var.tenant_type
  viso_env              = var.viso_env
  is_xpanse             = local.is_xpanse
  is_xsiam              = local.is_xsiam
  is_xdr                = local.is_xdr
  is_metro_tenant       = local.is_metro_tenant
  is_qa_automation_tenant = local.is_qa_automation_tenant
  is_perf_tenant        = local.is_perf_tenant
  metro_host_project_id = var.metro_host_project_id
  cortex_platform       = var.cortex_platform
  product_code          = var.product_code
  enable_assured_workloads = var.enable_assured_workloads
  enable_research_autopilot = local.enable_research_autopilot
}

module "dns_records" {
  source    = "../02_dns_records"
  providers = {
    google.mt = google.mt
  }
  cc_fqdn                        = local.cc_fqdn
  ch_fqdn                        = local.ch_fqdn
  dc_fqdn                        = local.dc_fqdn
  external_fqdn                  = var.external_fqdn
  external_crtx_fqdn             = var.external_crtx_fqdn
  use_crtx_domain                = local.use_crtx_domain
  gcp_xdr_dns_zone_name          = local.gcp_xdr_dns_zone_name
  gcp_crtx_dns_zone_name         = local.gcp_crtx_dns_zone_name
  gcp_dns_project                = local.gcp_dns_project
  is_perf_tenant                 = local.is_perf_tenant
  pool_tenant_creation           = lower(var.pool_tenant_creation)
  viso_env                       = var.viso_env
  is_xsiam                       = local.is_xsiam
  enable_xsoar_shared_components = local.enable_xsoar_shared_components
  is_fedramp                     = local.is_fedramp
  jupyter_fqdn                   = local.jupyter_fqdn
  observability_fqdn             = local.observability_fqdn
  overrides                      = var.overrides
}

module "resources_and_permissions" {
  source    = "../02_resources_and_permissions"
  providers = {
    google.mt = google.mt
  }
  backend_version                      = local.backend_version
  big_tenant                           = lower(var.big_tenant)
  bq_location                          = local.bq_location
  cold_retention                       = var.cold_retention
  compute_zones_names                  = module.bootstrap.compute_zones
  consul_datacenter                    = var.consul_datacenter
  cronus_deploy_idle_resources         = local.cronus_deploy_idle_resources
  cronus_standalone                    = local.cronus_standalone
  cronus_node_count                    = local.cronus_node_count
  scylla_iplen_nodes_count             = local.scylla_iplen_node_count
  dml_scale_base                       = local.dml_scale_base
  enable_asm                           = var.enable_asm
  enable_byok                          = var.enable_byok
  enable_replicator                    = lower(var.enable_replicator)
  rocksdb_standalone                   = local.rocksdb_standalone
  elasticsearch_standalone             = local.elasticsearch_standalone
  enable_scylla                        = lower(local.enable_scylla)
  enable_xcloud                        = local.enable_xcloud
  enable_xsoar_shared_components       = local.enable_xsoar_shared_components
  enable_gke_metering                  = var.enable_gke_metering
  enable_assured_workloads             = var.enable_assured_workloads
  metering_start_date                  = local.USAGE_METERING_START_DATE
  egress_enabled                       = var.egress_enabled
  enable_pipeline                      = local.enable_pipeline
  enable_network_egress_metering       = var.enable_network_egress_metering
  enable_resource_consumption_metering = var.enable_resource_consumption_metering
  external_fqdn                        = local.use_crtx_domain ? var.external_crtx_fqdn : var.external_fqdn
  app_proxy_blue_green_target          = local.app_proxy_blue_green_target
  forensics                            = var.forensics
  host_project                         = var.host_project
  host_project_subnetwork_name         = var.host_project_subnetwork_name
  prod_spec                            = local.prod_spec
  is_fedramp                           = local.is_fedramp
  is_xdr                               = local.is_xdr
  is_xsoar                             = local.is_xsoar
  is_xsiam                             = local.is_xsiam
  is_xpanse                            = local.is_xpanse
  is_xsoar_6_migration                 = var.is_xsoar_6_migration
  is_xsoar_onprem_migration            = var.is_xsoar_onprem_migration
  lcaas                                = var.lcaas
  megatron_xdr                         = local.megatron_xdr
  megatron_xsoar                       = local.megatron_xsoar
  multi_project_postfix                = var.multi_project_postfix
  overrides                            = var.overrides
  mysql_prod_spec_override             = lookup(var.overrides, "mysql_prod_spec_override", false)
  pool_tenant                          = var.pool_tenant_creation || var.pool_tenant_activation
  pool_tenant_activation               = var.pool_tenant_activation
  pool_tenant_creation                 = lower(var.pool_tenant_creation)
  pro_agents                           = local.pro_agents
  total_agents                         = local.total_agents
  pro_tenant                           = local.pro_tenant
  project_id                           = module.bootstrap.project_id
  project_network                      = module.bootstrap.project_network
  project_number                       = module.bootstrap.project_number
  project_prefix                       = var.project_prefix
  project_subnetwork                   = module.bootstrap.project_subnetwork
  region                               = var.region
  gke_location                         = local.gke_location
  regional_kubernetes                  = lower(local.regional_kubernetes)
  multi_zoned_nodepools                = local.multi_zoned_nodepools
  router_sa_google_group               = local.router_sa_google_group
  autopilot_sa_google_group            = local.autopilot_sa_google_group
  scylla_blue_nodepool                 = lookup(var.overrides, "scylla_blue_nodepool", true)
  scylla_green_nodepool                = lookup(var.overrides, "scylla_green_nodepool", false)
  scylla_nodes_count                   = local.scylla_nodes_count
  scylla_standalone                    = local.scylla_standalone
  enable_scylla_iplen                  = var.enable_scylla_iplen
  small_epp                            = local.small_epp
  streaming_api_sa                     = local.streaming_api_sa
  creation_date                        = var.creation_date
  terraform_iam                        = module.bootstrap.terraform_iam
  traps_sa_bq_reader                   = local.traps_sa_bq_reader
  viso_env                             = var.viso_env
  xcloud_standalone_deployment         = local.xcloud_standalone_deployment
  xdr_env                              = var.xdr_env
  customer_dev_tenant                  = local.customer_dev_tenant
  zone                                 = var.zone
  tenant_type                          = var.tenant_type
  uptime_otp_id                        = var.uptime_otp_id
  redis_split                          = local.redis_split
  xsoar_6_mig_size                     = local.xsoar_6_mig_size
  xsoar_mig_specs                      = local.xsoar_mig_specs
  jupyter_fqdn                         = local.jupyter_fqdn
  observability_fqdn                   = local.observability_fqdn
  is_metro_tenant                      = local.is_metro_tenant
  metro_host_project_id                = var.metro_host_project_id
  metro_host_id                        = var.metro_host_id
  metro_tenant_index                   = var.metro_tenant_index
  enable_itdr                          = local.enable_itdr
  enable_research_autopilot            = local.enable_research_autopilot
  product_code                         = local.product_code
  is_xsoar_legacy_spec                 = var.is_xsoar_legacy_spec
  viso_version                         = local.viso_version
  enable_cloud_appsec                  = var.enable_cloud_appsec
  enable_cloud_posture                 = var.enable_cloud_posture
}

module "crds" {
  source    = "../03_kube_custom_resource_definitions"
  providers = {
    google.mt = google.mt
  }

  enabled               = !local.is_metro_tenant
  lcaas                 = var.lcaas
  project_id            = module.bootstrap.project_id
  region                = var.region
  terraform_iam         = module.bootstrap.terraform_iam
  zone                  = var.zone
  is_metro_tenant       = local.is_metro_tenant
  metro_host_id         = var.metro_host_id
  metro_host_project_id = var.metro_host_project_id
  metro_host_zone       = var.metro_host_zone
  gke_location          = local.gke_location
}
