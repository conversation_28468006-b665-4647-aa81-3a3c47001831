module "create_gke" {
  for_each = local.clusters
  source   = "../../modules/gke"

  cluster                  = each.key
  clusters_data            = each.value
  project_id               = var.project_id
  viso_env                 = var.viso_env
  enable_dataplane_v2      = true
  enable_gateway_api       = true
  shielded_nodes           = true
  enable_secure_gke        = lookup(var.overrides, "enable_secure_gke", var.creation_date > local.enable_secure_gke_date )
  enable_byok              = var.enable_byok
  default_kms_key_id       = var.enable_byok ? var.default_byok_key_id : module.create_kms_crypto_key.st_kms_key_id["gke_application_layer"]
  dynamic_node_pools       = each.value.dynamic_node_pools
  depends_on               = [google_kms_crypto_key_iam_member.container_engine_crypto_key_iam]
}

locals {
  enable_secure_gke_date = var.viso_env == "dev" ? 1731315189000 : var.is_fedramp ? 1740990287000 : 1740989869000 # Enable in production only for new tenants after Batch 4 of version 3.13.
  clusters = {
    xdr = {
      name                  = "app-hub-cluster-${var.lcaas}"
      initial_node_count    = local.initial_node_count
      autoscaling_profile   = local.autoscaling_profile
      node_autoprovisioning = local.node_autoprovisioning
      location              = var.gke_location
      project               = var.project_id
      min_master_version    = "1.33"
      node_locations        = local.node_locations
      network               = var.project_network
      subnetwork            = var.project_subnetwork
      cidr_blocks           = concat(local.global_authorized_networks, local.environment_networks[var.xdr_env])
      machine_type          = local.default_instance_type
      service_account       = local.proxy_service_account_email
      dynamic_node_pools    = [
        {
          enabled         = true
          key_name        = "application-hub-octopus"
          name            = "application-hub-octopus-${local.gke_version}"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          machine_type    = "e2-standard-4"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GKE_METADATA"
          autoscaling     = [
            {
              min_node_count = 0
              max_node_count = 2
            }
          ]
          labels = {
            xdr-pool = "application-hub-octopus"
            product  = "xsiam"
          }
        },
        {
          enabled         = true
          key_name        = "application-hub-jupyter"
          name            = "application-hub-jupyter-${local.gke_version}"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          machine_type    = "e2-highmem-16"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GKE_METADATA"
          autoscaling     = [
            {
              min_node_count = 0
              max_node_count = 1
            }
          ]
          labels = {
            xdr-pool = "application-hub-jupyter"
            product  = "xsiam"
          }
        },
        {
          enabled         = true
          key_name        = "application-hub-observability"
          name            = "application-hub-observability-${local.gke_version}"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          machine_type    = "e2-highmem-4"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GKE_METADATA"
          autoscaling     = [
            {
              min_node_count = 0
              max_node_count = 1
            }
          ]
          labels = {
            xdr-pool = "application-hub-observability"
            product  = "xsiam"
          }
        }
      ]
    }
  }
  gke_version              = "1-33"
  initial_sum_node_count   = 1
  initial_zonal_node_count = var.regional_kubernetes ? local.initial_sum_node_count * (var.kube_num_extra_zones + 1) : local.initial_sum_node_count
  initial_node_count       = local.initial_zonal_node_count >= 5 ? local.initial_zonal_node_count : 1
  lcaas                    = length(var.lcaas) > 17 ? substr(var.lcaas, 0, 16) : var.lcaas
  environment_networks     = {
    default = [],
    QA5-GCP = [
      {
        cidr_block   = "34.69.217.32/32"
        display_name = "orchestrator-nat-dev1"
      },
      {
        cidr_block   = "35.224.157.142/32"
        display_name = "orchestrator-nat-dev2"
      },
      {
        cidr_block   = "34.69.217.32/32"
        display_name = "orchestrator-nat-dev3"
      },
      {
        cidr_block   = "34.66.162.118/32"
        display_name = "orchestrator-nat-dev4"
      },
    ],
    QA4-GCP = [
      {
        cidr_block   = "104.198.25.192/32"
        display_name = "orchestrator-dev-fw01"
      },
      {
        cidr_block   = "35.184.172.150/32"
        display_name = "orchestrator-dev-fw02"
      },
      {
        cidr_block   = "34.69.217.32/32"
        display_name = "orchestrator-dev-nat-0"
      },
      {
        cidr_block   = "35.224.157.142/32"
        display_name = "orchestrator-dev-nat-1"
      },
      {
        cidr_block   = "34.66.50.48/32"
        display_name = "orchestrator-dev-nat-2"
      },
      {
        cidr_block   = "34.66.162.118/32"
        display_name = "orchestrator-dev-nat-3"
      },
      {
        cidr_block   = "35.239.112.25/32"
        display_name = "orchestrator-dev-nat-4"
      },
    ],
    QA2-GCP = [
      {
        cidr_block   = "104.198.25.192/32"
        display_name = "orchestrator-dev-fw01"
      },
      {
        cidr_block   = "35.184.172.150/32"
        display_name = "orchestrator-dev-fw02"
      },
      {
        cidr_block   = "34.69.217.32/32"
        display_name = "orchestrator-dev-nat-0"
      },
      {
        cidr_block   = "35.224.157.142/32"
        display_name = "orchestrator-dev-nat-1"
      },
      {
        cidr_block   = "34.66.50.48/32"
        display_name = "orchestrator-dev-nat-2"
      },
      {
        cidr_block   = "34.66.162.118/32"
        display_name = "orchestrator-dev-nat-3"
      },
      {
        cidr_block   = "35.239.112.25/32"
        display_name = "orchestrator-dev-nat-4"
      },
    ],
    STG4 = [
      {
        cidr_block   = "35.193.5.83/32"
        display_name = "orchestrator-nat-stg1"
      },
      {
        cidr_block   = "35.192.183.131/32"
        display_name = "orchestrator-nat-stg2"
      },
    ],
    PROD-US = [
      {
        cidr_block   = "104.198.71.154/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-us1"
      },
      {
        cidr_block   = "35.202.111.149/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-us2"
      },
      {
        cidr_block   = "34.122.135.75/32"
        display_name = "orchestrator-nat-prod-us3"
      },
      {
        cidr_block   = "35.222.70.185/32"
        display_name = "orchestrator-nat-prod-us4"
      },
      {
        cidr_block   = "34.66.117.27/32"
        display_name = "orchestrator-fw-prod-us1"
      },
      {
        cidr_block   = "34.71.188.91/32"
        display_name = "orchestrator-fw-prod-us2"
      },
      {
        cidr_block   = "35.222.151.94/32"
        display_name = "orchestrator-fw-prod-us3"
      },
      {
        cidr_block   = "104.154.234.216/32"
        display_name = "orchestrator-fw-prod-us4"
      },
    ],
    PROD-EU = [
      {
        cidr_block   = "35.204.123.213/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-eu1"
      },
      {
        cidr_block   = "34.90.85.150/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-eu2"
      },
      {
        cidr_block   = "34.90.190.139/32"
        display_name = "orchestrator-nat-prod-eu3"
      },
      {
        cidr_block   = "34.34.41.237/32"
        display_name = "orchestrator-nat-prod-eu4"
      },
      {
        cidr_block   = "34.90.230.10/32"
        display_name = "orchestrator-fw-prod-eu1"
      },
      {
        cidr_block   = "34.91.45.138/32"
        display_name = "orchestrator-fw-prod-eu2"
      },
    ],
    PROD-UK = [
      {
        cidr_block   = "35.242.137.26/32"
        display_name = "orchestrator-fw-prod-uk1"
      },
      {
        cidr_block   = "34.89.83.194/32"
        display_name = "orchestrator-fw-prod-uk2"
      },
    ],
    PROD-SG = [
      {
        cidr_block   = "34.87.3.85/32"
        display_name = "orchestrator-fw-prod-sg1"
      },
      {
        cidr_block   = "34.87.44.205/32"
        display_name = "orchestrator-fw-prod-sg2"
      },
    ],
    PROD-JP = [
      {
        cidr_block   = "34.84.156.231/32"
        display_name = "orchestrator-fw-prod-jp1"
      },
      {
        cidr_block   = "34.84.47.120/32"
        display_name = "orchestrator-fw-prod-jp2"
      },
    ],
    PROD-CA = [
      {
        cidr_block   = "34.95.56.222/32"
        display_name = "orchestrator-fw-prod-ca1"
      },
      {
        cidr_block   = "34.95.6.224/32"
        display_name = "orchestrator-fw-prod-ca2"
      },
    ],
    PROD-AU = [
      {
        cidr_block   = "34.87.247.130/32"
        display_name = "orchestrator-fw-prod-au1"
      },
      {
        cidr_block   = "35.244.122.206/32"
        display_name = "orchestrator-fw-prod-au2"
      },
    ],
    PROD-CH = [
      {
        cidr_block   = "34.65.240.75/32"
        display_name = "orchestrator-fw-prod-ch1"
      },
      {
        cidr_block   = "34.65.3.57/32"
        display_name = "orchestrator-fw-prod-ch2"
      },
    ],
    PROD-DE = [
      {
        cidr_block   = "35.198.109.115/32"
        display_name = "orchestrator-fw-prod-de1"
      },
      {
        cidr_block   = "35.198.91.31/32"
        display_name = "orchestrator-fw-prod-de2"
      },
    ],
    PROD-IN = [
      {
        cidr_block   = "34.93.243.162/32"
        display_name = "orchestrator-fw-prod-in1"
      },
      {
        cidr_block   = "34.93.232.128/32"
        display_name = "orchestrator-fw-prod-in2"
      }
    ],
    PROD-PL = [
      {
        cidr_block   = "34.118.37.220/32"
        display_name = "orchestrator-fw-prod-pl1"
      },
      {
        cidr_block   = "34.118.31.166/32"
        display_name = "orchestrator-fw-prod-pl2"
      },
    ],
    PROD-QT = [
      {
        cidr_block   = "34.18.43.152/32"
        display_name = "orchestrator-fw-prod-qt1"
      },
      {
        cidr_block   = "34.18.19.202/32"
        display_name = "orchestrator-fw-prod-qt2"
      },
    ],
    PROD-TW = [
      {
        cidr_block   = "35.221.248.85/32"
        display_name = "orchestrator-fw-prod-tw1"
      },
      {
        cidr_block   = "34.80.10.47/32"
        display_name = "orchestrator-fw-prod-tw2"
      },
    ],
    PROD-FA = [
      {
        cidr_block   = "34.163.116.217/32"
        display_name = "orchestrator-fw-prod-fa1"
      },
      {
        cidr_block   = "34.163.127.73/32"
        display_name = "orchestrator-fw-prod-fa2"
      },
    ],
    PROD-IL = [
      {
        cidr_block   = "34.165.155.7/32"
        display_name = "orchestrator-fw-prod-il1"
      },
      {
        cidr_block   = "34.165.23.18/32"
        display_name = "orchestrator-fw-prod-il2"
      },
    ],
    PROD-ID = [
      {
        cidr_block   = "34.101.43.58/32"
        display_name = "orchestrator-fw-prod-id1"
      },
      {
        cidr_block   = "34.101.43.214/32"
        display_name = "orchestrator-fw-prod-id2"
      },
    ],
    PROD-SA = [
      {
        cidr_block   = "34.166.32.224/32"
        display_name = "orchestrator-fw-prod-sa1"
      },
      {
        cidr_block   = "34.166.61.154/32"
        display_name = "orchestrator-fw-prod-sa2"
      },
    ],
    PROD-ES = [
      {
        cidr_block   = "34.175.16.125/32"
        display_name = "orchestrator-fw-prod-es1"
      },
      {
        cidr_block   = "34.175.7.162/32"
        display_name = "orchestrator-fw-prod-es2"
      },
    ],
    PROD-PR = [
      {
        cidr_block   = "34.41.24.134/32"
        display_name = "orchestrator-fw-prod-pr1"
      },
      {
        cidr_block   = "34.135.191.119/32"
        display_name = "orchestrator-fw-prod-pr2"
      },
    ],
    PROD-IT = [
      {
        cidr_block   = "34.154.16.56/32"
        display_name = "orchestrator-fw-prod-it1"
      },
      {
        cidr_block   = "34.154.132.40/32"
        display_name = "orchestrator-fw-prod-it2"
      },
    ],
    PROD-KR = [
      {
        cidr_block   = "34.64.85.33/32"
        display_name = "orchestrator-fw-prod-kr1"
      },
      {
        cidr_block   = "34.47.68.85/32"
        display_name = "orchestrator-fw-prod-kr2"
      },
    ],
    PROD-ZA = [
      {
        cidr_block = "34.35.12.15/32"
        display_name = "orchestrator-fw-prod-za1"
      },
      {
        cidr_block = "34.35.30.50/32"
        display_name = "orchestrator-fw-prod-za2"
      },
    ],
    PROD-FR = [
      {
        cidr_block   = "34.71.25.114/32"
        display_name = "orchestrator-fw-prod-fr"
      },
      {
        cidr_block   = "35.224.29.195/32"
        display_name = "orchestrator-fw-prod-fr2"
      },
    ],
    PROD-GV = [
      {
        cidr_block   = "34.135.155.135/32"
        display_name = "orchestrator-fw-prod-gv"
      },
      {
        cidr_block   = "130.211.194.24/32"
        display_name = "orchestrator-fw-prod-gv2"
      },
    ],
  }
  global_authorized_networks = [
    {
      cidr_block   = "82.166.99.178/32"
      display_name = "GP-ISR"
    },
    {
      cidr_block   = "199.167.52.5/32"
      display_name = "GP-USA-CA"
    },
    {
      cidr_block   = "31.154.166.144/29"
      display_name = "TLV_Office1"
    },
    {
      cidr_block   = "199.203.162.208/29"
      display_name = "TLV_Office2"
    },
    {
      cidr_block   = "35.239.69.227/32"
      display_name = "jenkins-xdr-mgmt-us-fw1"
    },
    {
      cidr_block   = "35.184.108.54/32"
      display_name = "jenkins-xdr-mgmt-us-fw2"
    },
    {
      cidr_block   = "34.91.65.156/32"
      display_name = "jenkins-xdr-mgmt-eu-fw1"
    },
    {
      cidr_block   = "34.91.88.236/32"
      display_name = "jenkins-xdr-mgmt-eu-fw2"
    },
    {
      cidr_block   = "35.184.108.54/32"
      display_name = "Jenkins-FW2"
    },
    {
      cidr_block   = "35.232.169.130/32"
      display_name = "backend_mgmt1"
    },
    {
      cidr_block   = "35.226.23.214/32"
      display_name = "backend_mgmt2"
    },
    {
      cidr_block   = "74.217.90.10/32"
      display_name = "dc10_stg4"
    },
    {
      cidr_block   = "65.154.226.10/32"
      display_name = "DL1"
    },
    {
      cidr_block   = "154.59.126.10/32"
      display_name = "AM6"
    },
    {
      cidr_block   = "34.100.71.242/32"
      display_name = "GP-USA-NE"
    },
    {
      cidr_block   = "34.91.52.250/32"
      display_name = "xdr-jumpbox-fw1"
    },
    {
      cidr_block   = "34.91.57.245/32"
      display_name = "xdr-jumpbox-fw2"
    },
    {
      cidr_block   = "128.177.26.193/32"
      display_name = "GP-RESTON-GW"
    },
    {
      cidr_block   = "137.83.220.243/32"
      display_name = "PANGP-US-Northeast"
    },
    {
      cidr_block   = "137.83.249.90/32"
      display_name = "PANGP-US-Southeast"
    },
    {
      cidr_block   = "137.83.193.1/32"
      display_name = "PANGP-US-West"
    },
    {
      cidr_block   = "137.83.220.240/32"
      display_name = "PANGP-US-Northeast2"
    }
  ]
}

resource "terraform_data" "scale_gke_kube_dns" {
  count            = var.pool_tenant_creation ? 0 : 1
  triggers_replace = {
    enable_custom_kube_dns = timestamp() # timestamp() makes sure this is triggered on every run
  }
  provisioner "local-exec" {
    command = "python /xdr/terraform/tf/tenant/02_resources_and_permissions/files/scale_gke_kube_dns.py ${var.project_id} app-hub-cluster-${var.lcaas} ${var.gke_location} ${local.enable_custom_kube_dns}"
  }

  depends_on = [module.create_gke]
}