"""
This script validates a list of YAML files for syntax and content before merging.
"""
from typing import List
import sys
import yaml


# Define file paths as a list of constants for easy management


YAML_FILE_PATHS: List[str] = [
    'configengine/charts/tenant/prometheus/files/prometheus.rules',
    'configengine/charts/tenant/prometheus/files/whitelist.yaml',
    'configengine/charts/tenant/prometheus/files/blacklist.yaml',
]


def validate_yaml_file(file_path: str) -> bool:
    """
    Checks a single YAML file for syntax errors or if it's empty.

    Args:
        file_path (str): The path to the YAML file to check.

    Returns:
        bool: True if the file is valid, False otherwise.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # The FullLoader is more secure and recommended over the default loader
            data = yaml.full_load(f)
            if data is None:
                print(f"❌ Validation FAILED for '{file_path}': File is empty.")
                return False
    except FileNotFoundError:
        print(f"❌ Validation FAILED for '{file_path}': File not found.")
        return False
    except yaml.YAMLError as e:
        print(f"❌ Validation FAILED for '{file_path}': Error parsing YAML.")
        # The traceback from the exception is often the most useful part
        print(f"   Details: {e}")
        return False

    print(f"✅ Validation Successful for '{file_path}'")
    return True


def main() -> None:
    """
    Main function to loop through and validate all specified YAML files.
    Exits with a non-zero status code if any validation fails.
    """
    print("--- Starting YAML Validation ---")
    # Use a list comprehension to run validation on all files and store results
    validation_results = [validate_yaml_file(path) for path in YAML_FILE_PATHS]

    print("--- Validation Summary ---")
    # The `all()` function neatly checks if all results were True
    if all(validation_results):
        print("🎉 All YAML files are valid.")
        sys.exit(0)
    else:
        print("\n🚫 One or more YAML files failed validation. Please review the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
