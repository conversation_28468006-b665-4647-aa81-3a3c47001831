# Prometheus Configuration Validator

This tool validates Prometheus configuration files by actually starting a Prometheus instance and checking if it loads successfully.

## Features

- ✅ Validates configuration file syntax by starting Prometheus
- ✅ Checks if Prometheus binary is available
- ✅ Waits for Prometheus to become ready
- ✅ Automatically cleans up resources
- ✅ Configurable timeout and port
- ✅ Detailed logging and error reporting
- ✅ Graceful handling of interrupts

## Requirements

- Python 3.6+
- Prometheus binary (must be in PATH or specify with `--prometheus-binary`)
- Required Python packages (install with `pip install -r requirements.txt`)

## Installation

```bash
# Install Python dependencies
pip install -r requirements.txt

# Make sure Prometheus is installed and available
# Option 1: Install via package manager (e.g., brew install prometheus)
# Option 2: Download from https://prometheus.io/download/
# Option 3: Use Docker (see Docker usage below)
```

## Usage

### Basic Usage

```bash
# Validate a configuration file
python prometheus-loader.py prometheus.yml

# Use the example configuration
python prometheus-loader.py example-prometheus.yml
```

### Advanced Usage

```bash
# Custom timeout (default: 30 seconds)
python prometheus-loader.py prometheus.yml --timeout=60

# Custom Prometheus binary path
python prometheus-loader.py prometheus.yml --prometheus-binary=/usr/local/bin/prometheus

# Custom port (default: 9090)
python prometheus-loader.py prometheus.yml --port=9091

# Verbose logging
python prometheus-loader.py prometheus.yml --verbose
```

### Docker Usage

If you don't have Prometheus installed locally, you can use Docker:

```bash
# Pull Prometheus image
docker pull prom/prometheus

# Run the validator using Docker
docker run --rm -v $(pwd):/workspace -w /workspace \
  prom/prometheus python3 prometheus-loader.py example-prometheus.yml
```

## Exit Codes

- `0`: Configuration is valid and Prometheus started successfully
- `1`: Configuration is invalid or Prometheus failed to start

## Examples

### Successful Validation

```bash
$ python prometheus-loader.py example-prometheus.yml
2024-01-15 10:30:00,123 - INFO - Starting Prometheus configuration validation...
2024-01-15 10:30:00,124 - INFO - Configuration file is readable: example-prometheus.yml
2024-01-15 10:30:00,234 - INFO - Prometheus binary found: prometheus
2024-01-15 10:30:00,235 - INFO - Version: prometheus, version 2.45.0
2024-01-15 10:30:00,345 - INFO - Starting Prometheus with command: prometheus --config.file=example-prometheus.yml --storage.tsdb.path=/tmp/tmpxyz --web.listen-address=:9090 --web.enable-lifecycle --log.level=info
2024-01-15 10:30:00,456 - INFO - Prometheus process started with PID: 12345
2024-01-15 10:30:00,567 - INFO - Waiting for Prometheus to be ready (timeout: 30s)...
2024-01-15 10:30:02,678 - INFO - Prometheus is ready!
2024-01-15 10:30:02,789 - INFO - ✅ Prometheus configuration validation successful!
2024-01-15 10:30:02,890 - INFO - Terminating Prometheus process...
2024-01-15 10:30:03,001 - INFO - 🎉 Configuration validation completed successfully!
```

### Failed Validation

```bash
$ python prometheus-loader.py invalid-config.yml
2024-01-15 10:30:00,123 - INFO - Starting Prometheus configuration validation...
2024-01-15 10:30:00,124 - ERROR - Configuration file does not exist: invalid-config.yml
2024-01-15 10:30:00,125 - ERROR - ❌ Configuration validation failed!
```

## Integration with CI/CD

You can integrate this tool into your CI/CD pipeline:

### GitLab CI

```yaml
prometheus-config-validation:
  stage: test
  image: prom/prometheus
  script:
    - python3 -m pip install -r ci/tools/prometheus-load-validator/requirements.txt
    - python3 ci/tools/prometheus-load-validator/prometheus-loader.py path/to/prometheus.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master)$/
```

### GitHub Actions

```yaml
- name: Validate Prometheus Configuration
  run: |
    pip install -r ci/tools/prometheus-load-validator/requirements.txt
    python ci/tools/prometheus-load-validator/prometheus-loader.py path/to/prometheus.yml
```

## Troubleshooting

### Common Issues

1. **Prometheus binary not found**
   - Make sure Prometheus is installed and in your PATH
   - Use `--prometheus-binary` to specify the full path

2. **Port already in use**
   - Use `--port` to specify a different port
   - Make sure no other Prometheus instances are running

3. **Configuration file not found**
   - Check the file path is correct
   - Use absolute paths if needed

4. **Timeout issues**
   - Increase timeout with `--timeout`
   - Check system resources (CPU, memory)

### Debug Mode

Use `--verbose` flag for detailed logging to help diagnose issues.

## Files

- `prometheus-loader.py` - Main validation script
- `requirements.txt` - Python dependencies
- `example-prometheus.yml` - Example configuration for testing
- `README.md` - This documentation
