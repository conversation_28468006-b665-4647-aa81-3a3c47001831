global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "configengine/charts/app-hub/prometheus/files/prometheus.rules"
  - "configengine/charts/tenant/prometheus/files/prometheus.rules"
  - "configengine/charts/engine/prometheus/files/prometheus.rules"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
