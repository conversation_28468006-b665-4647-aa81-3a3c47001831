#!/bin/bash
set -e

# Prometheus Configuration Validator Script
# This script validates Prometheus configuration using the prometheus binary

CONFIG_FILE="$1"
TIMEOUT="${2:-30}"
PORT="${3:-9090}"

if [ -z "$CONFIG_FILE" ]; then
    echo "Usage: $0 <config_file> [timeout] [port]"
    echo "Example: $0 prometheus.yml 30 9090"
    exit 1
fi

if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

echo "🔍 Validating Prometheus configuration: $CONFIG_FILE"

# Step 1: Check configuration syntax
echo "📋 Checking configuration syntax..."

# Step 1a: Check main config syntax
if prometheus --config.file="$CONFIG_FILE" --dry-run 2>/dev/null; then
    echo "✅ Main configuration syntax is valid"
else
    echo "❌ Main configuration syntax is invalid"
    prometheus --config.file="$CONFIG_FILE" --dry-run
    exit 1
fi

# Step 1b: Extract and validate rule files
echo "📋 Checking rule files..."
RULE_FILES=$(grep -E "^\s*-\s*[\"']?[^\"']*\.rules[\"']?\s*$" "$CONFIG_FILE" | sed -E 's/^\s*-\s*[\"']?([^\"']*)[\"']?\s*$/\1/' || true)

if [ -n "$RULE_FILES" ]; then
    echo "Found rule files to validate:"
    echo "$RULE_FILES" | while read -r rule_file; do
        if [ -n "$rule_file" ]; then
            echo "  - $rule_file"

            # Check if rule file exists
            if [ ! -f "$rule_file" ]; then
                echo "❌ Rule file not found: $rule_file"
                exit 1
            fi

            # Validate rule file syntax using promtool
            if command -v promtool >/dev/null 2>&1; then
                if promtool check rules "$rule_file" >/dev/null 2>&1; then
                    echo "    ✅ Rule file syntax is valid"
                else
                    echo "    ❌ Rule file syntax is invalid:"
                    promtool check rules "$rule_file"
                    exit 1
                fi
            else
                echo "    ⚠️  promtool not available, skipping rule validation"
            fi
        fi
    done

    # Check if any rule validation failed
    if [ $? -ne 0 ]; then
        exit 1
    fi
else
    echo "No rule files found in configuration"
fi

# Step 2: Test actual startup (optional, more thorough)
echo "🚀 Testing Prometheus startup..."

# Create temporary directory for data
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR; pkill -f 'prometheus.*$CONFIG_FILE' 2>/dev/null || true" EXIT

# Start Prometheus in background
prometheus \
    --config.file="$CONFIG_FILE" \
    --storage.tsdb.path="$TEMP_DIR" \
    --web.listen-address=":$PORT" \
    --log.level=info &

PROMETHEUS_PID=$!

# Wait for Prometheus to be ready
echo "⏳ Waiting for Prometheus to be ready (timeout: ${TIMEOUT}s)..."
for i in $(seq 1 $TIMEOUT); do
    if kill -0 $PROMETHEUS_PID 2>/dev/null; then
        if wget -q --spider "http://localhost:$PORT/-/ready" 2>/dev/null; then
            echo "✅ Prometheus is ready!"
            kill $PROMETHEUS_PID 2>/dev/null || true
            echo "🎉 Configuration validation successful!"
            exit 0
        fi
    else
        echo "❌ Prometheus process died"
        exit 1
    fi
    sleep 1
done

echo "❌ Prometheus did not become ready within $TIMEOUT seconds"
kill $PROMETHEUS_PID 2>/dev/null || true
exit 1
