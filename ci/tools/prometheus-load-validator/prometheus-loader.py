#!/usr/bin/env python3
"""
Prometheus Configuration Validator

This script runs Prometheus with a specific configuration file and validates
that the service loads successfully. If the service fails to load, the script
exits with a failure code.

Usage:
    python prometheus-loader.py <config_file_path> [--timeout=30] [--prometheus-binary=prometheus]

Example:
    python prometheus-loader.py /path/to/prometheus.yml --timeout=60
"""

import argparse
import subprocess
import sys
import time
import signal
import os
import tempfile
import requests
import logging
import yaml
import re
from pathlib import Path
from typing import Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PrometheusValidator:
    """Validates Prometheus configuration by attempting to start the service."""

    def __init__(self, config_path: str, prometheus_binary: str = "prometheus",
                 timeout: int = 30, port: int = 9090):
        self.config_path = Path(config_path)
        self.prometheus_binary = prometheus_binary
        self.timeout = timeout
        self.port = port
        self.process: Optional[subprocess.Popen] = None
        self.temp_dir: Optional[tempfile.TemporaryDirectory] = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()

    def cleanup(self):
        """Clean up resources."""
        if self.process:
            try:
                logger.info("Terminating Prometheus process...")
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("Process didn't terminate gracefully, killing...")
                    self.process.kill()
                    self.process.wait()
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")

        if self.temp_dir:
            self.temp_dir.cleanup()

    def validate_config_file(self) -> bool:
        """Validate that the configuration file exists and is readable."""
        if not self.config_path.exists():
            logger.error(f"Configuration file does not exist: {self.config_path}")
            return False

        if not self.config_path.is_file():
            logger.error(f"Configuration path is not a file: {self.config_path}")
            return False

        try:
            with open(self.config_path, 'r') as f:
                f.read()
            logger.info(f"Configuration file is readable: {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"Cannot read configuration file: {e}")
            return False

    def check_prometheus_binary(self) -> bool:
        """Check if Prometheus binary is available."""
        try:
            result = subprocess.run(
                [self.prometheus_binary, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                logger.info(f"Prometheus binary found: {self.prometheus_binary}")
                logger.info(f"Version: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"Prometheus binary check failed: {result.stderr}")
                return False
        except FileNotFoundError:
            logger.error(f"Prometheus binary not found: {self.prometheus_binary}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("Prometheus version check timed out")
            return False
        except Exception as e:
            logger.error(f"Error checking Prometheus binary: {e}")
            return False

    def start_prometheus(self) -> bool:
        """Start Prometheus with the given configuration."""
        # Create temporary directory for Prometheus data
        self.temp_dir = tempfile.TemporaryDirectory()
        data_dir = self.temp_dir.name

        cmd = [
            self.prometheus_binary,
            f"--config.file={self.config_path}",
            f"--storage.tsdb.path={data_dir}",
            f"--web.listen-address=:{self.port}",
            "--web.enable-lifecycle",
            "--log.level=info"
        ]

        logger.info(f"Starting Prometheus with command: {' '.join(cmd)}")

        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                preexec_fn=os.setsid  # Create new process group
            )
            logger.info(f"Prometheus process started with PID: {self.process.pid}")
            return True
        except Exception as e:
            logger.error(f"Failed to start Prometheus: {e}")
            return False

    def wait_for_prometheus_ready(self) -> bool:
        """Wait for Prometheus to be ready by checking the health endpoint."""
        logger.info(f"Waiting for Prometheus to be ready (timeout: {self.timeout}s)...")

        start_time = time.time()
        while time.time() - start_time < self.timeout:
            # Check if process is still running
            if self.process and self.process.poll() is not None:
                stdout, stderr = self.process.communicate()
                logger.error("Prometheus process exited unexpectedly")
                logger.error(f"Exit code: {self.process.returncode}")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False

            try:
                # Try to connect to Prometheus health endpoint
                response = requests.get(
                    f"http://localhost:{self.port}/-/ready",
                    timeout=2
                )
                if response.status_code == 200:
                    logger.info("Prometheus is ready!")
                    return True
                else:
                    logger.debug(f"Health check returned status: {response.status_code}")
            except requests.exceptions.RequestException:
                # Connection failed, Prometheus not ready yet
                pass

            time.sleep(1)

        logger.error(f"Prometheus did not become ready within {self.timeout} seconds")
        return False

    def validate(self) -> bool:
        """Run the complete validation process."""
        logger.info("Starting Prometheus configuration validation...")

        # Step 1: Validate configuration file
        if not self.validate_config_file():
            return False

        # Step 2: Check Prometheus binary
        if not self.check_prometheus_binary():
            return False

        # Step 3: Start Prometheus
        if not self.start_prometheus():
            return False

        # Step 4: Wait for Prometheus to be ready
        if not self.wait_for_prometheus_ready():
            return False

        logger.info("✅ Prometheus configuration validation successful!")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Validate Prometheus configuration by starting the service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s prometheus.yml
  %(prog)s /path/to/prometheus.yml --timeout=60
  %(prog)s prometheus.yml --prometheus-binary=/usr/local/bin/prometheus
        """
    )

    parser.add_argument(
        "config_file",
        help="Path to the Prometheus configuration file"
    )

    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="Timeout in seconds to wait for Prometheus to start (default: 30)"
    )

    parser.add_argument(
        "--prometheus-binary",
        default="prometheus",
        help="Path to the Prometheus binary (default: prometheus)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=9090,
        help="Port for Prometheus to listen on (default: 9090)"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Handle Ctrl+C gracefully
    def signal_handler(signum, frame):
        logger.info("Received interrupt signal, cleaning up...")
        sys.exit(1)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        with PrometheusValidator(
            config_path=args.config_file,
            prometheus_binary=args.prometheus_binary,
            timeout=args.timeout,
            port=args.port
        ) as validator:
            success = validator.validate()

        if success:
            logger.info("🎉 Configuration validation completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Configuration validation failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()