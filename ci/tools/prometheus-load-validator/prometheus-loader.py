#!/usr/bin/env python3
"""
Prometheus Configuration Validator

This script runs Prometheus with a specific configuration file and validates
that the service loads successfully. If the service fails to load, the script
exits with a failure code.

Usage:
    python prometheus-loader.py <config_file_path> [--timeout=30] [--prometheus-binary=prometheus]

Example:
    python prometheus-loader.py /path/to/prometheus.yml --timeout=60
"""

import argparse
import subprocess
import sys
import time
import signal
import tempfile
import requests
import logging
from pathlib import Path
from typing import Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PrometheusValidator:
    """Validates Prometheus configuration by attempting to start the service."""

    def __init__(self, config_path: str, prometheus_binary: str = "prometheus",
                 timeout: int = 30, port: int = 9090):
        self.config_path = Path(config_path)
        self.prometheus_binary = prometheus_binary
        self.timeout = timeout
        self.port = port
        self.process: Optional[subprocess.Popen] = None
        self.temp_dir: Optional[tempfile.TemporaryDirectory] = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()

    def cleanup(self):
        """Clean up resources."""
        if self.process:
            try:
                logger.info("Terminating Prometheus process...")
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("Process didn't terminate gracefully, killing...")
                    self.process.kill()
                    self.process.wait()
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")

        if self.temp_dir:
            self.temp_dir.cleanup()

    def validate_config_file(self) -> bool:
        """Validate that the configuration file exists and is readable."""
        if not self.config_path.exists():
            logger.error(f"Configuration file does not exist: {self.config_path}")
            return False

        if not self.config_path.is_file():
            logger.error(f"Configuration path is not a file: {self.config_path}")
            return False

        try:
            with open(self.config_path, 'r') as f:
                f.read()
            logger.info(f"Configuration file is readable: {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"Cannot read configuration file: {e}")
            return False


    def check_prometheus_binary(self) -> bool:
        """Check if Prometheus binary is available."""
        try:
            result = subprocess.run(
                [self.prometheus_binary, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                logger.info(f"Prometheus binary found: {self.prometheus_binary}")
                logger.info(f"Version: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"Prometheus binary check failed: {result.stderr}")
                return False
        except FileNotFoundError:
            logger.error(f"Prometheus binary not found: {self.prometheus_binary}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("Prometheus version check timed out")
            return False
        except Exception as e:
            logger.error(f"Error checking Prometheus binary: {e}")
            return False

    def start_prometheus(self) -> bool:
        """Start Prometheus with the given configuration."""
        # Create temporary directory for Prometheus data
        self.temp_dir = tempfile.TemporaryDirectory()
        data_dir = self.temp_dir.name

        cmd = [
            self.prometheus_binary,
            f"--config.file={self.config_path}",
            f"--storage.tsdb.path={data_dir}",
            f"--web.listen-address=:{self.port}",
            "--web.enable-lifecycle",
            "--log.level=info"
        ]

        logger.info(f"Starting Prometheus with command: {' '.join(cmd)}")

        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Combine stderr into stdout
                text=True,
                bufsize=1,  # Line buffered
                universal_newlines=True
            )
            logger.info(f"Prometheus process started with PID: {self.process.pid}")
            return True
        except Exception as e:
            logger.error(f"Failed to start Prometheus: {e}")
            return False

    def wait_for_prometheus_ready(self) -> bool:
        """Wait for Prometheus to be ready by monitoring logs and health endpoint."""
        logger.info(f"Waiting for Prometheus to be ready (timeout: {self.timeout}s)...")

        start_time = time.time()
        log_lines = []
        prometheus_ready = False
        ready_time = None
        additional_monitoring_time = 60  # Monitor logs for 60 more seconds after ready

        while True:
            current_time = time.time()

            # Check if process is still running
            if self.process and self.process.poll() is not None:
                # Process has exited, read remaining output
                remaining_output = self.process.stdout.read()
                if remaining_output:
                    log_lines.extend(remaining_output.strip().split('\n'))

                logger.error("Prometheus process exited unexpectedly")
                logger.error(f"Exit code: {self.process.returncode}")
                self.print_logs(log_lines)
                return False

            # Read available log output
            if self.process and self.process.stdout:
                try:
                    # Use select to check if data is available (non-blocking)
                    import select
                    ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                    if ready:
                        line = self.process.stdout.readline()
                        if line:
                            line = line.strip()
                            log_lines.append(line)
                            logger.info(f"Prometheus: {line}")

                            # Check for errors in this line
                            if self.is_error_log(line):
                                logger.error(f"Error detected in Prometheus logs: {line}")
                                self.print_logs(log_lines)
                                return False
                except Exception as e:
                    # If select fails, continue with health check
                    logger.debug(f"Error reading logs: {e}")
                    pass

            # Try health endpoint only if not already ready
            if not prometheus_ready:
                # Check if we've exceeded the initial timeout
                if current_time - start_time > self.timeout:
                    logger.error(f"Prometheus did not become ready within {self.timeout} seconds")
                    self.print_logs(log_lines)
                    return False

                try:
                    response = requests.get(
                        f"http://localhost:{self.port}/-/ready",
                        timeout=1
                    )
                    if response.status_code == 200:
                        logger.info("Prometheus is ready! Monitoring logs for additional errors...")
                        prometheus_ready = True
                        ready_time = current_time
                except requests.exceptions.RequestException:
                    # Connection failed, Prometheus not ready yet
                    pass

            # If Prometheus is ready, monitor logs for additional time
            if prometheus_ready and ready_time is not None:
                if current_time - ready_time >= additional_monitoring_time:
                    # Always print logs for debugging
                    logger.info("=== FINAL LOG ANALYSIS ===")
                    self.print_logs(log_lines)

                    # Final check for rule errors in all collected logs
                    if self.has_rule_errors(log_lines):
                        logger.error("Rule file errors detected in logs")
                        return False
                    logger.info("✅ No errors detected during monitoring period")
                    return True

            time.sleep(0.5)

    def is_error_log(self, line: str) -> bool:
        """Check if a log line indicates an error."""
        error_indicators = [
            "level=error",
            "level=fatal",
            "panic:",
            "Error loading config",
            "Error parsing config",
            "couldn't load configuration",
            "invalid configuration",
            "failed to reload config",
            "error reloading config"
        ]

        line_lower = line.lower()
        for indicator in error_indicators:
            if indicator.lower() in line_lower:
                return True
        return False

    def has_rule_errors(self, log_lines: List[str]) -> bool:
        """Check if logs contain rule file errors."""
        rule_error_patterns = [
            "error loading rules",
            "error parsing rules",
            "invalid rule",
            "bad_data",
            "could not parse expression",
            "parse error",
            "yaml: line",
            "yaml: unmarshal",
            "cannot unmarshal",
            "mapping values are not allowed",
            "found character that cannot start any token",
            "error validating rules",
            "rule validation error",
            "syntax error"
        ]

        for line in log_lines:
            line_lower = line.lower()
            for pattern in rule_error_patterns:
                if pattern in line_lower:
                    logger.error(f"Found rule error pattern '{pattern}' in: {line}")
                    return True
        return False

    def print_logs(self, log_lines: List[str]):
        """Print Prometheus logs for debugging."""
        if log_lines:
            logger.error("=== ALL PROMETHEUS LOGS ===")
            for i, line in enumerate(log_lines, 1):
                logger.error(f"{i:3d}: {line}")
            logger.error("=== END LOGS ===")
        else:
            logger.error("No logs captured")

    def validate(self) -> bool:
        """Run the complete validation process."""
        logger.info("Starting Prometheus configuration validation...")

        # Step 1: Validate configuration file
        if not self.validate_config_file():
            return False

        # Step 2: Check Prometheus binary
        if not self.check_prometheus_binary():
            return False

        # Step 3: Start Prometheus
        if not self.start_prometheus():
            return False

        # Step 4: Wait for Prometheus to be ready
        if not self.wait_for_prometheus_ready():
            return False

        logger.info("✅ Prometheus configuration validation successful!")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Validate Prometheus configuration by starting the service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s prometheus.yml
  %(prog)s /path/to/prometheus.yml --timeout=60
  %(prog)s prometheus.yml --prometheus-binary=/usr/local/bin/prometheus
        """
    )

    parser.add_argument(
        "config_file",
        help="Path to the Prometheus configuration file"
    )

    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="Timeout in seconds to wait for Prometheus to start (default: 30)"
    )

    parser.add_argument(
        "--prometheus-binary",
        default="prometheus",
        help="Path to the Prometheus binary (default: prometheus)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=9090,
        help="Port for Prometheus to listen on (default: 9090)"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Handle Ctrl+C gracefully
    def signal_handler(signum, frame):
        logger.info("Received interrupt signal, cleaning up...")
        sys.exit(1)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        with PrometheusValidator(
            config_path=args.config_file,
            prometheus_binary=args.prometheus_binary,
            timeout=args.timeout,
            port=args.port
        ) as validator:
            success = validator.validate()

        if success:
            logger.info("🎉 Configuration validation completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Configuration validation failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()