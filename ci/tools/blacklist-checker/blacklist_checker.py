import os
import sys
import pprint as p
import yaml
import argparse
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import List, Tuple
YELLOW_COLOR = '\033[93m'
RESET_COLOR = '\033[0m'
GREEN_COLOR = '\033[92m'
RED_COLOR = '\033[31m'
BLUE_COLOR = "\033[34m"
# this script checks if added whitelist metrics additions have blacklist entries 
file_path = 'configengine/charts/tenant/prometheus/files/whitelist.yaml'
blacklist_file = 'configengine/charts/tenant/prometheus/files/blacklist.yaml'
recording_rule_file = 'configengine/charts/tenant/prometheus/files/prometheus.rules'
exclusions_file = 'ci/tools/blacklist-checker/exclusions.yml'
destination_branch = os.getenv('CI_MERGE_REQUEST_TARGET_BRANCH_NAME')
destination_git_branch = f"origin/{destination_branch}"
    
def find_string_in_yaml(search_string, file_path ) -> bool:
    # """
    # Checks if a string exists as a key or value in a YAML file.

    # Args:
    #     file_path: The path to the .yml file.
    #     search_string: The string to search for.

    # Returns:
    #     True if the string is found, False otherwise.
    # """
    if not os.path.exists(file_path):
        print(f"Error: File not found at '{file_path}'")
        return False

    try:
        with open(file_path, 'r') as f:
            data = yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file: {e}")
        return False

    # This is a recursive helper function to search through the data
    def recursive_find(item):
        if isinstance(item, dict):
            for key, value in item.items():
                if search_string in str(key) or recursive_find(value):
                    return True
        elif isinstance(item, list):
            for element in item:
                if recursive_find(element):
                    return True
        elif search_string in str(item):
            return True
        return False

    return recursive_find(data)



class TestResult:
    def __init__(self, metric: str, status: str, message: str, is_fatal: bool = False):
        self.metric = metric
        self.status = status
        self.message = message
        self.is_fatal = is_fatal
        self.test_name = f"blacklist_check_{metric}"


def generate_junit_xml(test_results: List[TestResult], output_file: str):
    ## """Generate JUnit XML report from test results."""
    root = ET.Element("testsuite")
    root.set("name", "blacklist-checker")
    root.set("tests", str(len(test_results)))
    root.set("failures", str(sum(1 for r in test_results if r.is_fatal)))
    root.set("time", "0")
    root.set("timestamp", datetime.now().isoformat())

    for result in test_results:
        testcase = ET.SubElement(root, "testcase")
        testcase.set("classname", "blacklist_checker")
        testcase.set("name", result.test_name)
        testcase.set("time", "0")

        if result.is_fatal:
            failure = ET.SubElement(testcase, "failure")
            failure.set("message", result.message)
            failure.text = f"Metric: {result.metric}\nStatus: {result.status}\nMessage: {result.message}"

    tree = ET.ElementTree(root)
    tree.write(output_file, encoding="utf-8", xml_declaration=True)


def check_blacklist_for_metric(metrics_list: List[str], junit_output: str = None) -> Tuple[int, List[TestResult]]:
    # """
    # Check blacklist for metrics and return results.

    # Args:
    #     metrics_list: List of metrics to check
    #     junit_output: Path to JUnit XML output file (optional)

    # Returns:
    #     Tuple of (fatal_count, test_results)
    # """
    fatal_ticker = 0
    test_results = []

    try:
        with open(blacklist_file, 'r') as f:
            data = yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file: {e}")
        return 1, []

    for metric in metrics_list:
        has_recording_rule = find_string_in_yaml(search_string=metric, file_path=recording_rule_file)
        has_blacklist = find_string_in_yaml(search_string=metric, file_path=blacklist_file)

        # Y + N condition
        if has_recording_rule and not has_blacklist:
            message = "Does Not Have Blacklist and has Recording Rule - Blacklist entry REQUIRED"
            result = TestResult(metric, "FATAL", message, is_fatal=True)
            test_results.append(result)
            print(f"{BLUE_COLOR} Metric:{RESET_COLOR}", metric, f'{RED_COLOR} ---- > ** Fatal ** {message}{RESET_COLOR}')
            fatal_ticker += 1

        # Y + Y condition

        # N + Y condition
        elif not has_recording_rule and has_blacklist:
            message = "Blacklist Entry Exists without Recording Rule"
            result = TestResult(metric, "FATAL", message, is_fatal=True)
            test_results.append(result)
            print(f"{BLUE_COLOR} Metric:{RESET_COLOR}", metric, f'{RED_COLOR} ---- > ** Fatal ** {message}{RESET_COLOR}')
            fatal_ticker += 1

    return fatal_ticker, test_results


def get_all_whitelisted_metrics(file_path: str) -> list[str]:
    # """
    # Get all whitelisted metrics from the whitelist file.
    # """
    try:
        with open(file_path, 'r') as f:
            data = yaml.safe_load(f)
            all_metrics = []
            for dictionary in data.values():
                if isinstance(dictionary, dict):
                    for key, value in dictionary.items():
                        if key == "regex":
                            all_metrics.extend(value)
            return all_metrics
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file: {e}")
        return []



# def get_added_lines_pythonic(file_path: str, repo_path: str = '.') -> list[str]:
#     # """
#     # Uses the GitPython library to find lines added to a specific file.

#     # Args:
#     #     file_path: The path to the file relative to the repo root.
#     #     repo_path: The path to the git repository.

#     # Returns:
#     #     A list of strings, where each string is an added line of content.
#     # """
#     added_lines = []
#     try:
#         # Initialize the repository object
#         repo = git.Repo(repo_path)
        
#         # Get the raw text diff for the specific file against the index (staging area)
#         # The output is the same format as the command-line tool
#         diff_output = repo.git.diff(destination_git_branch, file_path, unified=0)
#         for line in diff_output.splitlines():
#             if line.startswith('+') and not line.startswith('+++'):
#                 added_lines.append(line[1:])

#     except git.exc.InvalidGitRepositoryError:
#         print(f"Error: '{repo_path}' is not a valid Git repository.")
#         sys.exit(1)
#     except Exception as e:
#         print(f"An unexpected error occurred: {e}")
#         sys.exit(1)
#     return added_lines

# def clean_added_line(added_lines):


def parse_arguments():
    # """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Check if added whitelist metrics have corresponding blacklist entries"
    )
    parser.add_argument(
        "--junit",
        type=str,
        help="Generate JUnit XML report and save to specified file path"
    )
    return parser.parse_args()


def main():
    # """Main function to run the blacklist checker."""
    args = parse_arguments()

    # Ensure the file from the previous example exists and is modified
    if not os.path.exists(file_path):
        error_msg = "Cannot find or access the whitelist file - Please check directory reference Var:file_path"
        print(error_msg)

        if args.junit:
            # Create a failed test result for missing file
            test_results = [TestResult("file_check", "FATAL", error_msg, is_fatal=True)]
            generate_junit_xml(test_results, args.junit)

        sys.exit(1)

    # newly_added_py = get_added_lines_pythonic(file_path)
    all_metrics = get_all_whitelisted_metrics(file_path)
    excluded_metrics = get_all_whitelisted_metrics(exclusions_file)
    if all_metrics:
        clean_metrics_list = []

        for line in all_metrics:
            metric_item = line.split()[-1]
            if metric_item not in excluded_metrics:
                clean_metrics_list.append(metric_item)

        fatal_count, test_results = check_blacklist_for_metric(clean_metrics_list, args.junit)

        if args.junit:
            generate_junit_xml(test_results, args.junit)
            print(f"JUnit XML report generated: {args.junit}")

        if fatal_count != 0:
            print(f"{RED_COLOR}Found {fatal_count} fatal issues{RESET_COLOR}")
            sys.exit(1)  # fatal messages block
        else:
            print(f"{GREEN_COLOR} All Checks Passed{RESET_COLOR}")
            sys.exit(0)  # no fatal messages
    else:
        # No changes found - create a passing test for JUnit if requested
        if args.junit:
            test_results = [TestResult("no_changes", "PASS", "No metrics added - no checks needed", is_fatal=False)]
            generate_junit_xml(test_results, args.junit)
            print(f"JUnit XML report generated: {args.junit}")

        sys.exit(0)  # since no changes - return success


if __name__ == "__main__":
    main()