apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    type: files-fetching-template
  name: files-fetching-template
  namespace: {{.Values.namespaceOverride}}
spec:
  arguments:
    parameters:
    - name: trace-id
      value: missing-trace-id
    - name: scan-id
      value: '{{`{{workflow.uid}}`}}'
  entrypoint: clone-runner
  metrics:
    prometheus:
    - help: Workflow duration by name
      histogram:
        buckets:
        - 5.0
        - 30.0
        - 60.0
        - 120.0
        - 300.0
        - 600.0
        - 1800.0
        - 3600.0
        value: '{{`{{workflow.duration}}`}}'
      labels:
      - key: name
        value: files_fetching_template
      - key: namespace
        value: {{.Values.namespaceOverride}}
      - key: status
        value: '{{`{{workflow.status}}`}}'
      - key: flow
        value: '{{`{{= lower(inputs.parameters[''flow''])}}`}}'
      name: workflow_duration_secs
  synchronization:
    semaphore:
      configMapKeyRef:
        key: files-fetching
        name: source-control-argo-concurrency
  templates:
  - container:
      command:
      - node
      - --import
      - ./apps/source-control/dist/instrumentation-bundle.js
      - apps/source-control/dist/files-fetching.js
      env:
      - name: ENCRYPTION_KEY
        valueFrom:
          secretKeyRef:
            key: integration_encryption_key
            name: applications-secrets
      - name: CRTX_INTEGRATION_URL
        valueFrom:
          configMapKeyRef:
            key: CRTX_INTEGRATION_URL
            name: cas-configmap
      - name: GENERIC_EXTERNAL_FQDN
        valueFrom:
          configMapKeyRef:
            key: GENERIC_EXTERNAL_FQDN
            name: cas-configmap
      - name: CUSTOMER_NAME
        valueFrom:
          configMapKeyRef:
            key: LCAAS_ID
            name: cas-configmap
      - name: EGRESSPROXY_URL
        valueFrom:
          configMapKeyRef:
            key: EGRESSPROXY_URL
            name: cas-configmap
            optional: true
      - name: VCS_ENRICHMENT_TOPIC
        valueFrom:
          configMapKeyRef:
            key: VCS_ENRICHMENT_TOPIC
            name: cas-configmap
            optional: true
      - name: SKIP_SSL
        valueFrom:
          configMapKeyRef:
            key: SKIP_SSL
            name: cas-configmap
            optional: true
      - name: NODE_TLS_REJECT_UNAUTHORIZED
        value: {{.Values.tlsRejectUnauthorized}}
      - name: ARGO_SERVER_URL
        value: {{.Values.argoWorkflows.serverUrl}}
      - name: RUN_ENV
        value: {{.Values.runEnv}}
      - name: LOCAL_STORAGE_ENABLED
        value: {{.Values.storage.local}}
      - name: MINIO_PORT
        value: {{.Values.storage.minioPort}}
      - name: MINIO_ACCESS_KEY
        value: {{.Values.storage.minioUser}}
      - name: MINIO_SECRET_KEY
        value: {{.Values.storage.minioPassword}}
      - name: WORKFLOWS_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
            optional: true
      - name: SOURCE_CONTROL_BUCKET
        valueFrom:
          configMapKeyRef:
            key: SOURCE_CONTROL_BUCKET
            name: cas-configmap
            optional: true
      - name: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
            name: cas-configmap
            optional: true
      - name: MONGO_DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: {{.Values.mongo.password.secretKey}}
            name: {{.Values.mongo.password.secretName}}
      - name: MONGO_DB_HOST_NAME
        valueFrom:
          configMapKeyRef:
            key: MONGO_DB_HOST_NAME
            name: cas-configmap
      - name: MONGO_DB_USERNAME
        value: {{.Values.mongo.userName}}
      - name: MONGO_DB_DATABASE_NAME
        value: {{.Values.mongo.databaseName}}
      - name: REDIS_HOST
        valueFrom:
          secretKeyRef:
            key: redis_host
            name: infra-secrets
            optional: true
      - name: REDIS_PASSWORD
        valueFrom:
          secretKeyRef:
            key: redis_password
            name: infra-secrets
            optional: true
      - name: GITHUB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: github_client_id
            name: applications-secrets
      - name: GITHUB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: github_client_secret
            name: applications-secrets
      - name: GITHUB_APP_PEM
        valueFrom:
          secretKeyRef:
            key: github_app_pem
            name: applications-secrets
      - name: GITHUB_APP_ID
        valueFrom:
          secretKeyRef:
            key: github_app_id
            name: applications-secrets
      - name: GITHUB_APP_NAME
        valueFrom:
          secretKeyRef:
            key: github_app_name
            name: applications-secrets
      - name: AZURE_REPOS_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_client_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_ID
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_id
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_NAME
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_name
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-client-id
            name: cas-azure-repos-entra-id
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_SECRET_VALUE
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-secret-value
            name: cas-azure-repos-entra-id
            optional: true
      - name: GITHUB_WEBHOOK_TOKEN
        valueFrom:
          secretKeyRef:
            key: github_webhook_token
            name: applications-secrets
            optional: true
      - name: ARGO_API_KEY
        valueFrom:
          secretKeyRef:
            key: {{.Values.argoWorkflows.password.secretKey}}
            name: {{.Values.argoWorkflows.password.secretName}}
      - name: GITLAB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: gitlab_client_id
            name: applications-secrets
            optional: true
      - name: GITLAB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: gitlab_client_secret
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_id
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_secret
            name: applications-secrets
            optional: true
      - name: METRICS_NAMESPACE
        value: file_fetching_job_runner
      - name: REPOSITORY_ID
        value: '{{`{{inputs.parameters.repositoryId}}`}}'
      - name: FLOW
        value: '{{`{{inputs.parameters.flow}}`}}'
      - name: FLOW_TO_EXECUTE
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? (inputs.parameters[''customer-modules'']
          == ''empty-value'' || count(map([''ENRY'',''DEPPY'',''IAC'',''SAST'',''SCA'',''SECRETS''],
          (fromJSON(inputs.parameters[''customer-modules''])[#] ?? false) == true))
          > 0 ? ''clone'' : ''api'') : (inputs.parameters.integrationType == ''HCP_TFC_RUN_TASKS''
          || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS'' ? ''api''
          : ''fetchFilesByCloneOrApi'') }}`}}'
      - name: SKIP_ASSET_PERSIST
        value: {{.Values.skipAssetPersist}}
      - name: CLONE_LOCATION
        value: /tmp/clone/{{`{{inputs.parameters.repositoryId}}`}}
      - name: FULL_CLONE_LOCATION
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''clones/'' + inputs.parameters.flow
          + ''/'' + inputs.parameters.repositoryId + ''/'' + ''full_clone.tgz''  :
          (inputs.parameters.flow == ''PRIVATE_EXTERNAL_MODULE'' ? ''clones/'' + inputs.parameters.flow
          + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.commit
          + ''/'' + ''clone.tgz''  : (inputs.parameters.prFlow == ''DIFF'' ? ''empty-value''
          : (inputs.parameters.integrationType == ''HCP_TFC_RUN_TASKS'' || inputs.parameters.integrationType
          == ''HCP_TFE_RUN_TASKS'' ? ''clones/'' + inputs.parameters.flow + ''/''
          + inputs.parameters.repositoryId + ''/'' + inputs.parameters.commit + ''/full/''
          + ''clone.tgz''  : ''clones/'' + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId
          + ''/'' + inputs.parameters.pr + ''/'' + inputs.parameters.commit + ''/full/''
          + ''clone.tgz'' ))) }}`}}'
      - name: CLONE_PATH
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''clones/'' + inputs.parameters.flow
          + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.branch
          + ''/'' + ''clone.tgz''  : (inputs.parameters.flow == ''PRIVATE_EXTERNAL_MODULE''
          ? ''clones/'' + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId
          + ''/'' + inputs.parameters.commit + ''/'' + ''clone.tgz''  : (inputs.parameters.prFlow
          == ''DIFF'' ? ''empty-value'' : (inputs.parameters.integrationType == ''HCP_TFC_RUN_TASKS''
          || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS'' ? ''clones/''
          + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
          + inputs.parameters.commit + ''/full/'' + ''clone.tgz''  : ''clones/'' +
          inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
          + inputs.parameters.pr + ''/'' + inputs.parameters.commit + ''/full/'' +
          ''clone.tgz'' ))) }}`}}'
      - name: COMMITS_FILTER_LOCATION
        value: /tmp/commits-filter/{{`{{inputs.parameters.branch}}`}}
      - name: CLONE_METADATA_LOCATION
        value: /tmp/clone-metadata/{{`{{inputs.parameters.branch}}`}}
      - name: FILES_LIST_PATH
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''files/'' + inputs.parameters.flow
          + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.branch
          + ''/files.json'' : ''empty-value'' }}`}}'
      - name: FILES_LIST_LOCATION
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''/tmp/files/'' + inputs.parameters.repositoryId
          : ''empty-value'' }}`}}'
      - name: DIFF_CLONE_LOCATION
        value: '{{`{{= inputs.parameters.flow == ''PR'' ? ''/tmp/clone/diff'' : ''empty-value''
          }}`}}'
      - name: PATCH_FILE_MAPPING_LOCATION
        value: '{{`{{= inputs.parameters.flow == ''PR'' ? (inputs.parameters.integrationType
          == ''HCP_TFC_RUN_TASKS'' || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS''
          ? ''/tmp/patchFileMapping/'' + inputs.parameters.repositoryId + ''/'' +  inputs.parameters.commit
          + ''/'' + ''patch-file-mapping.json'' : ''/tmp/patchFileMapping/'' + inputs.parameters.repositoryId
          + ''/'' +  inputs.parameters.pr + ''/'' +  inputs.parameters.commit + ''/''
          + ''patch-file-mapping.json'') : ''empty-value'' }}`}}'
      - name: INTEGRATION_TYPE
        value: '{{`{{inputs.parameters.integrationType}}`}}'
      - name: BRANCH
        value: '{{`{{inputs.parameters.branch}}`}}'
      - name: COMMIT
        value: '{{`{{inputs.parameters.commit}}`}}'
      - name: PR
        value: '{{`{{inputs.parameters.pr}}`}}'
      - name: PR_FLOW
        value: '{{`{{inputs.parameters.prFlow}}`}}'
      - name: TARGET_BRANCH
        value: '{{`{{inputs.parameters.targetBranch}}`}}'
      - name: FILES_PATH
        value: '{{`{{inputs.parameters.filesPath}}`}}'
      - name: FILES_COUNT
        value: '{{`{{inputs.parameters.filesCount}}`}}'
      - name: CUSTOMER_MODULES
        value: '{{`{{inputs.parameters.customer-modules}}`}}'
      - name: PLAN_SCAN_RESULT
        value: '{{`{{inputs.parameters.planScanResult}}`}}'
      - name: CLONE_BUCKET
        value: '{{`{{inputs.parameters.sensitive-customer-data-bucket-name}}`}}'
      - name: DEFAULT_BUCKET
        value: '{{`{{inputs.parameters.bucket-name}}`}}'
      - name: OTEL_TRACE_ID
        value: '{{`{{inputs.parameters.otelTraceId}}`}}'
      - name: OTEL_SPAN_ID
        value: '{{`{{inputs.parameters.otelSpanId}}`}}'
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: clone_runner
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      - name: EGRESSPROXY_CA_PATH
        value: /etc/api-certs-cas/egress.crt
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: '{{.Values.filesFetching.image.registry}}/{{.Values.filesFetching.image.repository}}:{{
        .Values.filesFetching.image.tag}}'
      imagePullPolicy: {{.Values.filesFetching.image.pullPolicy}}
      resources:
        limits:
          cpu: '6'
          memory: 6Gi
          ephemeral-storage: 100Gi
        requests:
          cpu: '2'
          memory: 1Gi
          ephemeral-storage: 500Mi
      volumeMounts:
      - mountPath: /etc/api-certs-cas
        name: api-certs-cas-volume
        readOnly: true
    inputs:
      parameters:
      - name: bucket-name
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
      - name: sensitive-customer-data-bucket-name
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
            name: cas-configmap
      - name: scan-id
      - name: repositoryId
      - name: integrationType
        value: empty-value
      - name: flow
      - name: prFlow
        value: empty-value
      - name: branch
        value: empty-value
      - name: commit
        value: empty-value
      - name: pr
        value: empty-value
      - name: targetBranch
        value: empty-value
      - name: filesPath
        value: empty-value
      - name: filesCount
        value: empty-value
      - name: customer-modules
        value: empty-value
      - name: planScanResult
        value: empty-value
      - name: otelTraceId
        value: empty-value
      - name: otelSpanId
        value: empty-value
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 30.0
          - 60.0
          - 300.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: clone_runner
        - key: status
          value: '{{`{{status}}`}}'
        name: job_duration_seconds
    name: clone-runner
    outputs:
      artifacts:
      - gcs:
          bucket: '{{`{{inputs.parameters.sensitive-customer-data-bucket-name}}`}}'
          key: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''clones/'' + inputs.parameters.flow
            + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.branch
            + ''/'' + ''clone.tgz''  : (inputs.parameters.flow == ''PRIVATE_EXTERNAL_MODULE''
            ? ''clones/'' + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId
            + ''/'' + inputs.parameters.commit + ''/'' + ''clone.tgz''  : (inputs.parameters.prFlow
            == ''DIFF'' ? ''empty-value'' : (inputs.parameters.integrationType ==
            ''HCP_TFC_RUN_TASKS'' || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS''
            ? ''clones/'' + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId
            + ''/'' + inputs.parameters.commit + ''/full/'' + ''clone.tgz''  : ''clones/''
            + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
            + inputs.parameters.pr + ''/'' + inputs.parameters.commit + ''/full/''
            + ''clone.tgz'' ))) }}`}}'
        name: clone
        optional: true
        path: /tmp/clone/{{`{{inputs.parameters.repositoryId}}`}}
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''files/'' + inputs.parameters.flow
            + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.branch
            + ''/files.json'' : ''empty-value'' }}`}}'
        name: files-list
        optional: true
        path: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''/tmp/files/'' + inputs.parameters.repositoryId
          : ''empty-value'' }}`}}'
      - gcs:
          bucket: '{{`{{inputs.parameters.sensitive-customer-data-bucket-name}}`}}'
          key: '{{`{{= inputs.parameters.flow == ''PR'' && inputs.parameters.prFlow !=
            ''FULL'' ? (inputs.parameters.integrationType == ''HCP_TFC_RUN_TASKS''
            || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS'' ? ''clones/''
            + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
            + inputs.parameters.commit + ''/diff/'' + ''clone.tgz''  : ''clones/''
            + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
            + inputs.parameters.pr + ''/'' + inputs.parameters.commit + ''/diff/''
            + ''clone.tgz'' ) : ''empty-value'' }}`}}'
        name: diff-clone
        optional: true
        path: '{{`{{= inputs.parameters.flow == ''PR'' ? ''/tmp/clone/diff'' : ''empty-value''
          }}`}}'
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: '{{`{{= inputs.parameters.flow == ''PR'' ? ''patch-file-mapping/'' + inputs.parameters.repositoryId
            + ''/'' +  inputs.parameters.pr + ''/'' +  inputs.parameters.commit +
            ''/'' + ''patch-file-mapping.json'' : ''empty-value'' }}`}}'
        name: patch-file-mapping
        optional: true
        path: '{{`{{= inputs.parameters.flow == ''PR'' ? (inputs.parameters.integrationType
          == ''HCP_TFC_RUN_TASKS'' || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS''
          ? ''/tmp/patchFileMapping/'' + inputs.parameters.repositoryId + ''/'' +  inputs.parameters.commit
          + ''/'' + ''patch-file-mapping.json'' : ''/tmp/patchFileMapping/'' + inputs.parameters.repositoryId
          + ''/'' +  inputs.parameters.pr + ''/'' +  inputs.parameters.commit + ''/''
          + ''patch-file-mapping.json'') : ''empty-value'' }}`}}'
      parameters:
      - name: commits-filter
        valueFrom:
          default: empty-value
          path: /tmp/commits-filter/{{`{{inputs.parameters.branch}}`}}
      - name: clone-metadata
        valueFrom:
          default: empty-value
          path: /tmp/clone-metadata/{{`{{inputs.parameters.branch}}`}}
      - name: clone-path
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''clones/'' + inputs.parameters.flow
          + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.branch
          + ''/'' + ''clone.tgz''  : (inputs.parameters.flow == ''PRIVATE_EXTERNAL_MODULE''
          ? ''clones/'' + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId
          + ''/'' + inputs.parameters.commit + ''/'' + ''clone.tgz''  : (inputs.parameters.prFlow
          == ''DIFF'' ? ''empty-value'' : (inputs.parameters.integrationType == ''HCP_TFC_RUN_TASKS''
          || inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS'' ? ''clones/''
          + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
          + inputs.parameters.commit + ''/full/'' + ''clone.tgz''  : ''clones/'' +
          inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
          + inputs.parameters.pr + ''/'' + inputs.parameters.commit + ''/full/'' +
          ''clone.tgz'' ))) }}`}}'
      - name: files-list-path
        value: '{{`{{= inputs.parameters.flow == ''PERIODIC'' ? ''files/'' + inputs.parameters.flow
          + ''/'' + inputs.parameters.repositoryId + ''/'' + inputs.parameters.branch
          + ''/files.json'' : ''empty-value'' }}`}}'
      - name: diff-clone-path
        value: '{{`{{= inputs.parameters.flow == ''PR'' && inputs.parameters.prFlow !=
          ''FULL'' ? (inputs.parameters.integrationType == ''HCP_TFC_RUN_TASKS'' ||
          inputs.parameters.integrationType == ''HCP_TFE_RUN_TASKS'' ? ''clones/''
          + inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
          + inputs.parameters.commit + ''/diff/'' + ''clone.tgz''  : ''clones/'' +
          inputs.parameters.flow + ''/'' + inputs.parameters.repositoryId + ''/''
          + inputs.parameters.pr + ''/'' + inputs.parameters.commit + ''/diff/'' +
          ''clone.tgz'' ) : ''empty-value'' }}`}}'
      - name: patch-file-mapping-path
        value: '{{`{{= inputs.parameters.flow == ''PR'' ? ''patch-file-mapping/'' + inputs.parameters.repositoryId
          + ''/'' +  inputs.parameters.pr + ''/'' +  inputs.parameters.commit + ''/''
          + ''patch-file-mapping.json'' : ''empty-value'' }}`}}'
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'''
      limit: 4
    serviceAccountName: {{.Values.serviceAccount.name}}
    timeout: 1h
    volumes:
    - name: api-certs-cas-volume
      secret:
        optional: true
        secretName: api-certs-cas
  workflowMetadata:
    labelsFrom:
      name:
        expression: workflow.name
      uid:
        expression: workflow.uid
      repository-id:
        expression: inputs.parameters.repositoryId
      trigger-type:
        expression: inputs.parameters.flow
