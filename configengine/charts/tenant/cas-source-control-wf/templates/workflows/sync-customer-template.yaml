apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    type: sync-customer-template
  name: sync-customer-template
  namespace: {{.Values.namespaceOverride}}
spec:
  arguments:
    parameters:
    - name: customer-name
    - name: scan-id
    - name: integration-id
      value: empty-value
    - name: integration-updates-path
      value: empty-value
    - name: trace-id
      value: missing-trace-id
  entrypoint: main
  metrics:
    prometheus:
    - help: Workflow duration by name
      histogram:
        buckets:
        - 5.0
        - 30.0
        - 60.0
        - 120.0
        - 300.0
        - 600.0
        - 1800.0
        - 3600.0
        value: '{{`{{workflow.duration}}`}}'
      labels:
      - key: name
        value: sync_customer_template
      - key: namespace
        value: {{.Values.namespaceOverride}}
      - key: status
        value: '{{`{{workflow.status}}`}}'
      name: workflow_duration_secs
  parallelism: {{.Values.parallelism.syncCustomer}}
  templates:
  - container:
      command:
      - node
      - apps/source-control/dist/sync-customer.js
      env:
      - name: ENCRYPTION_KEY
        valueFrom:
          secretKeyRef:
            key: integration_encryption_key
            name: applications-secrets
      - name: CRTX_INTEGRATION_URL
        valueFrom:
          configMapKeyRef:
            key: CRTX_INTEGRATION_URL
            name: cas-configmap
      - name: GENERIC_EXTERNAL_FQDN
        valueFrom:
          configMapKeyRef:
            key: GENERIC_EXTERNAL_FQDN
            name: cas-configmap
      - name: CUSTOMER_NAME
        valueFrom:
          configMapKeyRef:
            key: LCAAS_ID
            name: cas-configmap
      - name: EGRESSPROXY_URL
        valueFrom:
          configMapKeyRef:
            key: EGRESSPROXY_URL
            name: cas-configmap
            optional: true
      - name: VCS_ENRICHMENT_TOPIC
        valueFrom:
          configMapKeyRef:
            key: VCS_ENRICHMENT_TOPIC
            name: cas-configmap
            optional: true
      - name: SKIP_SSL
        valueFrom:
          configMapKeyRef:
            key: SKIP_SSL
            name: cas-configmap
            optional: true
      - name: NODE_TLS_REJECT_UNAUTHORIZED
        value: {{.Values.tlsRejectUnauthorized}}
      - name: ARGO_SERVER_URL
        value: {{.Values.argoWorkflows.serverUrl}}
      - name: RUN_ENV
        value: {{.Values.runEnv}}
      - name: LOCAL_STORAGE_ENABLED
        value: {{.Values.storage.local}}
      - name: MINIO_PORT
        value: {{.Values.storage.minioPort}}
      - name: MINIO_ACCESS_KEY
        value: {{.Values.storage.minioUser}}
      - name: MINIO_SECRET_KEY
        value: {{.Values.storage.minioPassword}}
      - name: WORKFLOWS_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
            optional: true
      - name: SOURCE_CONTROL_BUCKET
        valueFrom:
          configMapKeyRef:
            key: SOURCE_CONTROL_BUCKET
            name: cas-configmap
            optional: true
      - name: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
            name: cas-configmap
            optional: true
      - name: MONGO_DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: {{.Values.mongo.password.secretKey}}
            name: {{.Values.mongo.password.secretName}}
      - name: MONGO_DB_HOST_NAME
        valueFrom:
          configMapKeyRef:
            key: MONGO_DB_HOST_NAME
            name: cas-configmap
      - name: MONGO_DB_USERNAME
        value: {{.Values.mongo.userName}}
      - name: MONGO_DB_DATABASE_NAME
        value: {{.Values.mongo.databaseName}}
      - name: REDIS_HOST
        valueFrom:
          secretKeyRef:
            key: redis_host
            name: infra-secrets
            optional: true
      - name: REDIS_PASSWORD
        valueFrom:
          secretKeyRef:
            key: redis_password
            name: infra-secrets
            optional: true
      - name: GITHUB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: github_client_id
            name: applications-secrets
      - name: GITHUB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: github_client_secret
            name: applications-secrets
      - name: GITHUB_APP_PEM
        valueFrom:
          secretKeyRef:
            key: github_app_pem
            name: applications-secrets
      - name: GITHUB_APP_ID
        valueFrom:
          secretKeyRef:
            key: github_app_id
            name: applications-secrets
      - name: GITHUB_APP_NAME
        valueFrom:
          secretKeyRef:
            key: github_app_name
            name: applications-secrets
      - name: AZURE_REPOS_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_client_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_ID
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_id
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_NAME
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_name
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-client-id
            name: cas-azure-repos-entra-id
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_SECRET_VALUE
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-secret-value
            name: cas-azure-repos-entra-id
            optional: true
      - name: GITHUB_WEBHOOK_TOKEN
        valueFrom:
          secretKeyRef:
            key: github_webhook_token
            name: applications-secrets
            optional: true
      - name: ARGO_API_KEY
        valueFrom:
          secretKeyRef:
            key: {{.Values.argoWorkflows.password.secretKey}}
            name: {{.Values.argoWorkflows.password.secretName}}
      - name: GITLAB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: gitlab_client_id
            name: applications-secrets
            optional: true
      - name: GITLAB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: gitlab_client_secret
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_id
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_secret
            name: applications-secrets
            optional: true
      - name: METRICS_NAMESPACE
        value: sync_customer_job_runner
      - name: OUTPUT_PATH
        value: /tmp/output.json
      - name: CUSTOMER_MODULES_PATH
        value: /tmp/customer-modules.json
      - name: INTEGRATION_ERRORS_PATH
        value: /tmp/integration_errors_path.json
      - name: SYNC_BRANCHES_ERRORS_PATH
        value: /tmp/sync_branches_errors_path.json
      - name: BRANCHES_TO_SCAN_CHUNK_SIZE
        value: '1000'
      - name: REPOSITORIES_OUTPUT_PATH
        value: /tmp/repositories.json
      - name: REPORT_OUTPUT_PATH
        value: /tmp/report.json
      - name: ORGANIZATIONS_ASSET_REPORT_OUTPUT_PATH
        value: /tmp/organizations_asset_report.json
      - name: REPORT_WITH_SCANNED_BRANCHES_OUTPUT_PATH
        value: /tmp/report-with-branches.json
      - name: SYNC_BRANCHES_SUMMARY_OUTPUT_PATH
        value: /tmp/sync-branches-summary.json
      - name: BRANCHES_AMOUNT_OUTPUT_PATH
        value: /tmp/branches_amount.txt
      - name: SYNC_REPOSITORIES_OUTPUT_PATH
        value: /tmp/sync-repositories-output.json
      - name: INTEGRATION_UPDATES_PATH
        value: '{{`{{workflow.parameters.integration-updates-path}}`}}'
      - name: INTEGRATION_ID
        value: '{{`{{workflow.parameters.integration-id}}`}}'
      - name: SCAN_ID
        value: '{{`{{workflow.parameters.scan-id}}`}}'
      - name: CORTEX_PLATFORM_URL
        valueFrom:
          configMapKeyRef:
            key: CORTEX_PLATFORM_URL
            name: cas-configmap
            optional: false
      - name: FLOW_TO_EXECUTE
        value: syncRepositories
      - name: LIMIT_FOR_EACH_WITH_LIMIT
        value: '20'
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: sync_customer
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      - name: EGRESSPROXY_CA_PATH
        value: /etc/api-certs-cas/egress.crt
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: '{{.Values.syncCustomer.image.registry}}/{{.Values.syncCustomer.image.repository}}:{{
        .Values.syncCustomer.image.tag}}'
      imagePullPolicy: {{.Values.syncCustomer.image.pullPolicy}}
      resources:
        limits:
          cpu: '4'
          memory: 8Gi
        requests:
          cpu: '1'
          memory: 1Gi
      volumeMounts:
      - mountPath: /etc/api-certs-cas
        name: api-certs-cas-volume
        readOnly: true
    inputs:
      parameters:
      - name: bucket-name
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 30.0
          - 60.0
          - 300.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: sync_customer
        - key: status
          value: '{{`{{status}}`}}'
        name: job_duration_seconds
    name: sync-customer
    outputs:
      artifacts:
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories.json
        name: repositories-file
        path: /tmp/repositories.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories-report.json
        name: repositories-report-file
        path: /tmp/report.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/integration-errors.json
        name: integration-errors-file
        path: /tmp/integration_errors_path.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/organizations-asset-report.json
        name: organization-report-file
        path: /tmp/organizations_asset_report.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-repositories-output.json
        name: sync-repositories-output-path-file
        path: /tmp/sync-repositories-output.json
      parameters:
      - name: repositories-path
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories.json
      - name: repositories-report
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories-report.json
      - name: integration-errors
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/integration-errors.json
      - name: organization-report
        value: '"[{\"report\": \"scans/{{`{{workflow.parameters.scan-id}}`}}/organizations-asset-report.json\",
          \"entityType\": \"CortexAsset\", \"persistState\": \"Partial\", \"scanType\":
          \"PERIODIC\", \"reportIdentifier\": \"Organization\"}]"'
      - name: sync-repositories-output-path
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-repositories-output.json
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'''
      limit: 4
    serviceAccountName: {{.Values.serviceAccount.name}}
    timeout: 6h
    volumes:
    - name: api-certs-cas-volume
      secret:
        optional: true
        secretName: api-certs-cas
  - inputs:
      parameters:
      - name: scan-id
      - name: trace-id
        value: '{{`{{workflow.parameters.trace-id}}`}}'
    name: sync-customer-assets
    outputs:
      parameters:
      - name: subworkflow-name
        valueFrom:
          jsonPath: '{.metadata.name}'
      - name: subworkflow-uid
        valueFrom:
          jsonPath: '{.metadata.uid}'
    resource:
      action: create
      manifest: |
        apiVersion: argoproj.io/v1alpha1
        kind: Workflow
        metadata:
          generateName: sync-customer-assets-
        spec:
          arguments:
            parameters:
            - name: scan-id
              value: {{`{{inputs.parameters.scan-id}}`}}
            - name: trace-id
              value: {{`{{inputs.parameters.trace-id}}`}}
          priority: {{`{{= workflow.priority ?? 0}}`}}
          workflowMetadata:
            labels:
              workflows.argoproj.io/workflow-template: sync-customer-assets-template
              parent-workflow: {{`{{workflow.name}}`}}
              scan-id: {{`{{workflow.parameters.scan-id}}`}}
              trace-id: {{`{{workflow.parameters.trace-id}}`}}
          workflowTemplateRef:
            name: sync-customer-assets-template
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: '2'
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'''
      limit: 5
    timeout: 480h
  - container:
      command:
      - node
      - apps/source-control/dist/sync-external-projects.js
      env:
      - name: ENCRYPTION_KEY
        valueFrom:
          secretKeyRef:
            key: integration_encryption_key
            name: applications-secrets
      - name: CRTX_INTEGRATION_URL
        valueFrom:
          configMapKeyRef:
            key: CRTX_INTEGRATION_URL
            name: cas-configmap
      - name: GENERIC_EXTERNAL_FQDN
        valueFrom:
          configMapKeyRef:
            key: GENERIC_EXTERNAL_FQDN
            name: cas-configmap
      - name: CUSTOMER_NAME
        valueFrom:
          configMapKeyRef:
            key: LCAAS_ID
            name: cas-configmap
      - name: EGRESSPROXY_URL
        valueFrom:
          configMapKeyRef:
            key: EGRESSPROXY_URL
            name: cas-configmap
            optional: true
      - name: VCS_ENRICHMENT_TOPIC
        valueFrom:
          configMapKeyRef:
            key: VCS_ENRICHMENT_TOPIC
            name: cas-configmap
            optional: true
      - name: SKIP_SSL
        valueFrom:
          configMapKeyRef:
            key: SKIP_SSL
            name: cas-configmap
            optional: true
      - name: NODE_TLS_REJECT_UNAUTHORIZED
        value: {{.Values.tlsRejectUnauthorized}}
      - name: ARGO_SERVER_URL
        value: {{.Values.argoWorkflows.serverUrl}}
      - name: RUN_ENV
        value: {{.Values.runEnv}}
      - name: LOCAL_STORAGE_ENABLED
        value: {{.Values.storage.local}}
      - name: MINIO_PORT
        value: {{.Values.storage.minioPort}}
      - name: MINIO_ACCESS_KEY
        value: {{.Values.storage.minioUser}}
      - name: MINIO_SECRET_KEY
        value: {{.Values.storage.minioPassword}}
      - name: WORKFLOWS_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
            optional: true
      - name: SOURCE_CONTROL_BUCKET
        valueFrom:
          configMapKeyRef:
            key: SOURCE_CONTROL_BUCKET
            name: cas-configmap
            optional: true
      - name: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
            name: cas-configmap
            optional: true
      - name: MONGO_DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: {{.Values.mongo.password.secretKey}}
            name: {{.Values.mongo.password.secretName}}
      - name: MONGO_DB_HOST_NAME
        valueFrom:
          configMapKeyRef:
            key: MONGO_DB_HOST_NAME
            name: cas-configmap
      - name: MONGO_DB_USERNAME
        value: {{.Values.mongo.userName}}
      - name: MONGO_DB_DATABASE_NAME
        value: {{.Values.mongo.databaseName}}
      - name: REDIS_HOST
        valueFrom:
          secretKeyRef:
            key: redis_host
            name: infra-secrets
            optional: true
      - name: REDIS_PASSWORD
        valueFrom:
          secretKeyRef:
            key: redis_password
            name: infra-secrets
            optional: true
      - name: GITHUB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: github_client_id
            name: applications-secrets
      - name: GITHUB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: github_client_secret
            name: applications-secrets
      - name: GITHUB_APP_PEM
        valueFrom:
          secretKeyRef:
            key: github_app_pem
            name: applications-secrets
      - name: GITHUB_APP_ID
        valueFrom:
          secretKeyRef:
            key: github_app_id
            name: applications-secrets
      - name: GITHUB_APP_NAME
        valueFrom:
          secretKeyRef:
            key: github_app_name
            name: applications-secrets
      - name: AZURE_REPOS_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_client_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_ID
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_id
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_NAME
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_name
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-client-id
            name: cas-azure-repos-entra-id
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_SECRET_VALUE
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-secret-value
            name: cas-azure-repos-entra-id
            optional: true
      - name: GITHUB_WEBHOOK_TOKEN
        valueFrom:
          secretKeyRef:
            key: github_webhook_token
            name: applications-secrets
            optional: true
      - name: ARGO_API_KEY
        valueFrom:
          secretKeyRef:
            key: {{.Values.argoWorkflows.password.secretKey}}
            name: {{.Values.argoWorkflows.password.secretName}}
      - name: GITLAB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: gitlab_client_id
            name: applications-secrets
            optional: true
      - name: GITLAB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: gitlab_client_secret
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_id
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_secret
            name: applications-secrets
            optional: true
      - name: METRICS_NAMESPACE
        value: sync_customer_job_runner
      - name: OUTPUT_PATH
        value: /tmp/output.json
      - name: CUSTOMER_MODULES_PATH
        value: /tmp/customer-modules.json
      - name: INTEGRATION_ERRORS_PATH
        value: /tmp/integration_errors_path.json
      - name: SYNC_BRANCHES_ERRORS_PATH
        value: /tmp/sync_branches_errors_path.json
      - name: BRANCHES_TO_SCAN_CHUNK_SIZE
        value: '1000'
      - name: REPOSITORIES_OUTPUT_PATH
        value: /tmp/repositories.json
      - name: REPORT_OUTPUT_PATH
        value: /tmp/report.json
      - name: ORGANIZATIONS_ASSET_REPORT_OUTPUT_PATH
        value: /tmp/organizations_asset_report.json
      - name: REPORT_WITH_SCANNED_BRANCHES_OUTPUT_PATH
        value: /tmp/report-with-branches.json
      - name: SYNC_BRANCHES_SUMMARY_OUTPUT_PATH
        value: /tmp/sync-branches-summary.json
      - name: BRANCHES_AMOUNT_OUTPUT_PATH
        value: /tmp/branches_amount.txt
      - name: SYNC_REPOSITORIES_OUTPUT_PATH
        value: /tmp/sync-repositories-output.json
      - name: INTEGRATION_UPDATES_PATH
        value: '{{`{{workflow.parameters.integration-updates-path}}`}}'
      - name: INTEGRATION_ID
        value: '{{`{{workflow.parameters.integration-id}}`}}'
      - name: SCAN_ID
        value: '{{`{{workflow.parameters.scan-id}}`}}'
      - name: CORTEX_PLATFORM_URL
        valueFrom:
          configMapKeyRef:
            key: CORTEX_PLATFORM_URL
            name: cas-configmap
            optional: false
      - name: FLOW_TO_EXECUTE
        value: syncExternalProjects
      - name: PROJECT_ID
        valueFrom:
          configMapKeyRef:
            key: GCPCONF_PROJECT_ID
            name: cas-configmap
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: sync_external_project
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      - name: EGRESSPROXY_CA_PATH
        value: /etc/api-certs-cas/egress.crt
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: '{{.Values.syncExternalProjects.image.registry}}/{{.Values.syncExternalProjects.image.repository}}:{{
        .Values.syncExternalProjects.image.tag}}'
      imagePullPolicy: {{.Values.syncExternalProjects.image.pullPolicy}}
      resources:
        limits:
          cpu: '4'
          memory: 8Gi
          ephemeral-storage: 10Gi
        requests:
          cpu: '1'
          memory: 1Gi
          ephemeral-storage: 500Mi
      volumeMounts:
      - mountPath: /etc/api-certs-cas
        name: api-certs-cas-volume
        readOnly: true
    inputs:
      parameters:
      - name: bucket-name
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 30.0
          - 60.0
          - 300.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: sync_external_project
        - key: status
          value: '{{`{{status}}`}}'
        name: job_duration_seconds
    name: sync-external-project
    outputs:
      parameters:
      - name: projects
        valueFrom:
          path: /tmp/output.json
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'''
      limit: 4
    serviceAccountName: {{.Values.serviceAccount.name}}
    timeout: 6h
    volumes:
    - name: api-certs-cas-volume
      secret:
        optional: true
        secretName: api-certs-cas
  - container:
      command:
      - node
      - apps/source-control/dist/sync-branches.js
      env:
      - name: ENCRYPTION_KEY
        valueFrom:
          secretKeyRef:
            key: integration_encryption_key
            name: applications-secrets
      - name: CRTX_INTEGRATION_URL
        valueFrom:
          configMapKeyRef:
            key: CRTX_INTEGRATION_URL
            name: cas-configmap
      - name: GENERIC_EXTERNAL_FQDN
        valueFrom:
          configMapKeyRef:
            key: GENERIC_EXTERNAL_FQDN
            name: cas-configmap
      - name: CUSTOMER_NAME
        valueFrom:
          configMapKeyRef:
            key: LCAAS_ID
            name: cas-configmap
      - name: EGRESSPROXY_URL
        valueFrom:
          configMapKeyRef:
            key: EGRESSPROXY_URL
            name: cas-configmap
            optional: true
      - name: VCS_ENRICHMENT_TOPIC
        valueFrom:
          configMapKeyRef:
            key: VCS_ENRICHMENT_TOPIC
            name: cas-configmap
            optional: true
      - name: SKIP_SSL
        valueFrom:
          configMapKeyRef:
            key: SKIP_SSL
            name: cas-configmap
            optional: true
      - name: NODE_TLS_REJECT_UNAUTHORIZED
        value: {{.Values.tlsRejectUnauthorized}}
      - name: ARGO_SERVER_URL
        value: {{.Values.argoWorkflows.serverUrl}}
      - name: RUN_ENV
        value: {{.Values.runEnv}}
      - name: LOCAL_STORAGE_ENABLED
        value: {{.Values.storage.local}}
      - name: MINIO_PORT
        value: {{.Values.storage.minioPort}}
      - name: MINIO_ACCESS_KEY
        value: {{.Values.storage.minioUser}}
      - name: MINIO_SECRET_KEY
        value: {{.Values.storage.minioPassword}}
      - name: WORKFLOWS_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
            optional: true
      - name: SOURCE_CONTROL_BUCKET
        valueFrom:
          configMapKeyRef:
            key: SOURCE_CONTROL_BUCKET
            name: cas-configmap
            optional: true
      - name: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
            name: cas-configmap
            optional: true
      - name: MONGO_DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: {{.Values.mongo.password.secretKey}}
            name: {{.Values.mongo.password.secretName}}
      - name: MONGO_DB_HOST_NAME
        valueFrom:
          configMapKeyRef:
            key: MONGO_DB_HOST_NAME
            name: cas-configmap
      - name: MONGO_DB_USERNAME
        value: {{.Values.mongo.userName}}
      - name: MONGO_DB_DATABASE_NAME
        value: {{.Values.mongo.databaseName}}
      - name: REDIS_HOST
        valueFrom:
          secretKeyRef:
            key: redis_host
            name: infra-secrets
            optional: true
      - name: REDIS_PASSWORD
        valueFrom:
          secretKeyRef:
            key: redis_password
            name: infra-secrets
            optional: true
      - name: GITHUB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: github_client_id
            name: applications-secrets
      - name: GITHUB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: github_client_secret
            name: applications-secrets
      - name: GITHUB_APP_PEM
        valueFrom:
          secretKeyRef:
            key: github_app_pem
            name: applications-secrets
      - name: GITHUB_APP_ID
        valueFrom:
          secretKeyRef:
            key: github_app_id
            name: applications-secrets
      - name: GITHUB_APP_NAME
        valueFrom:
          secretKeyRef:
            key: github_app_name
            name: applications-secrets
      - name: AZURE_REPOS_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_client_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_SECRET
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_secret
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_ID
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_id
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_APP_NAME
        valueFrom:
          secretKeyRef:
            key: azure_repos_app_name
            name: applications-secrets
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-client-id
            name: cas-azure-repos-entra-id
            optional: true
      - name: AZURE_REPOS_ENTRA_ID_SECRET_VALUE
        valueFrom:
          secretKeyRef:
            key: azure-repos-entra-id-secret-value
            name: cas-azure-repos-entra-id
            optional: true
      - name: GITHUB_WEBHOOK_TOKEN
        valueFrom:
          secretKeyRef:
            key: github_webhook_token
            name: applications-secrets
            optional: true
      - name: ARGO_API_KEY
        valueFrom:
          secretKeyRef:
            key: {{.Values.argoWorkflows.password.secretKey}}
            name: {{.Values.argoWorkflows.password.secretName}}
      - name: GITLAB_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: gitlab_client_id
            name: applications-secrets
            optional: true
      - name: GITLAB_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: gitlab_client_secret
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_ID
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_id
            name: applications-secrets
            optional: true
      - name: BITBUCKET_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            key: bitbucket_client_secret
            name: applications-secrets
            optional: true
      - name: METRICS_NAMESPACE
        value: sync_customer_job_runner
      - name: OUTPUT_PATH
        value: /tmp/output.json
      - name: CUSTOMER_MODULES_PATH
        value: /tmp/customer-modules.json
      - name: INTEGRATION_ERRORS_PATH
        value: /tmp/integration_errors_path.json
      - name: SYNC_BRANCHES_ERRORS_PATH
        value: /tmp/sync_branches_errors_path.json
      - name: BRANCHES_TO_SCAN_CHUNK_SIZE
        value: '1000'
      - name: REPOSITORIES_OUTPUT_PATH
        value: /tmp/repositories.json
      - name: REPORT_OUTPUT_PATH
        value: /tmp/report.json
      - name: ORGANIZATIONS_ASSET_REPORT_OUTPUT_PATH
        value: /tmp/organizations_asset_report.json
      - name: REPORT_WITH_SCANNED_BRANCHES_OUTPUT_PATH
        value: /tmp/report-with-branches.json
      - name: SYNC_BRANCHES_SUMMARY_OUTPUT_PATH
        value: /tmp/sync-branches-summary.json
      - name: BRANCHES_AMOUNT_OUTPUT_PATH
        value: /tmp/branches_amount.txt
      - name: SYNC_REPOSITORIES_OUTPUT_PATH
        value: /tmp/sync-repositories-output.json
      - name: INTEGRATION_UPDATES_PATH
        value: '{{`{{workflow.parameters.integration-updates-path}}`}}'
      - name: INTEGRATION_ID
        value: '{{`{{workflow.parameters.integration-id}}`}}'
      - name: SCAN_ID
        value: '{{`{{workflow.parameters.scan-id}}`}}'
      - name: CORTEX_PLATFORM_URL
        valueFrom:
          configMapKeyRef:
            key: CORTEX_PLATFORM_URL
            name: cas-configmap
            optional: false
      - name: FLOW_TO_EXECUTE
        value: syncBranches
      - name: INPUT_FILE_PATHS
        value: /tmp/repositories.json,/tmp/report.json,/tmp/sync-repositories-output.json
      - name: LIMIT_FOR_EACH_WITH_LIMIT
        value: '20'
      - name: DEFAULT_BUCKET
        value: '{{`{{inputs.parameters.bucket-name}}`}}'
      - name: PROJECT_ID
        valueFrom:
          configMapKeyRef:
            key: GCPCONF_PROJECT_ID
            name: cas-configmap
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: sync_branches
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      - name: EGRESSPROXY_CA_PATH
        value: /etc/api-certs-cas/egress.crt
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: '{{.Values.syncBranches.image.registry}}/{{.Values.syncBranches.image.repository}}:{{
        .Values.syncBranches.image.tag}}'
      imagePullPolicy: {{.Values.syncBranches.image.pullPolicy}}
      volumeMounts:
      - mountPath: /etc/api-certs-cas
        name: api-certs-cas-volume
        readOnly: true
    inputs:
      artifacts:
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories.json
        mode: 644
        name: repositories-file
        path: /tmp/repositories.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories-report.json
        mode: 644
        name: repositories-report-file
        path: /tmp/report.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-repositories-output.json
        mode: 644
        name: sync-repositories-output-path-file
        path: /tmp/sync-repositories-output.json
      parameters:
      - name: bucket-name
        valueFrom:
          configMapKeyRef:
            key: WORKFLOWS_ARTIFACTS_BUCKET
            name: cas-configmap
      - name: repositories-path
      - name: repositories-report-path
      - name: sync-repositories-output-path
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 30.0
          - 60.0
          - 300.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: sync_branches
        - key: status
          value: '{{`{{status}}`}}'
        name: job_duration_seconds
    name: sync-branches
    outputs:
      artifacts:
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/repositories-with-scanned-branches-report.json
        name: repositories-with-scanned-branches-report-file
        path: /tmp/report-with-branches.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-branches-summary.json
        name: sync-branches-summary
        path: /tmp/sync-branches-summary.json
      - archive:
          none: {}
        gcs:
          bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
          key: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-branches-errors.json
        name: sync-branches-errors-file
        path: /tmp/sync_branches_errors_path.json
      parameters:
      - name: repositories-report
        value: '"[{\"report\": \"scans/{{`{{workflow.parameters.scan-id}}`}}/repositories-with-scanned-branches-report.json\",
          \"entityType\": \"CortexAsset\", \"persistState\": \"Partial\", \"scanType\":
          \"PERIODIC\", \"reportIdentifier\": \"Repository\"}]"'
      - name: branches-chunks-path
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/branches-to-scan-chunks
      - name: branches-amount
        valueFrom:
          path: /tmp/branches_amount.txt
      - name: branches-chunk-size
        value: '1000'
      - name: sync-branches-summary-path
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-branches-summary.json
      - name: sync-branches-errors
        value: scans/{{`{{workflow.parameters.scan-id}}`}}/sync-branches-errors.json
      - name: customer-modules
        valueFrom:
          path: /tmp/customer-modules.json
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'''
      limit: 4
    serviceAccountName: {{.Values.serviceAccount.name}}
    timeout: 6h
    volumes:
    - name: api-certs-cas-volume
      secret:
        optional: true
        secretName: api-certs-cas
  - name: main
    outputs:
      parameters:
      - name: customer-modules
        valueFrom:
          parameter: '{{`{{steps.sync-branches.outputs.parameters.customer-modules}}`}}'
      - name: organization-report
        valueFrom:
          parameter: '{{`{{steps.sync-repositories.outputs.parameters.organization-report}}`}}'
      - name: repositories-report
        valueFrom:
          parameter: '{{`{{steps.sync-branches.outputs.parameters.repositories-report}}`}}'
      - name: branches-chunk-size
        valueFrom:
          parameter: '{{`{{steps.sync-branches.outputs.parameters.branches-chunk-size}}`}}'
      - name: branches-chunks-path
        valueFrom:
          parameter: '{{`{{steps.sync-branches.outputs.parameters.branches-chunks-path}}`}}'
      - name: branches-amount
        valueFrom:
          parameter: '{{`{{steps.sync-branches.outputs.parameters.branches-amount}}`}}'
      - name: external-projects
        valueFrom:
          parameter: '{{`{{steps.sync-external-projects.outputs.parameters.projects}}`}}'
    steps:
    - - name: sync-repositories
        template: sync-customer
    - - arguments:
          parameters:
          - name: scan-id
            value: '{{`{{workflow.parameters.scan-id}}`}}'
        name: sync-customer-assets
        template: sync-customer-assets
    - - arguments:
          parameters:
          - name: repositories-path
            value: '{{`{{steps.sync-repositories.outputs.parameters.repositories-path}}`}}'
          - name: repositories-report-path
            value: '{{`{{steps.sync-repositories.outputs.parameters.repositories-report}}`}}'
          - name: sync-repositories-output-path
            value: '{{`{{steps.sync-repositories.outputs.parameters.sync-repositories-output-path}}`}}'
        name: sync-branches
        template: sync-branches
    - - name: sync-external-projects
        template: sync-external-project
  workflowMetadata:
    labelsFrom:
      name:
        expression: workflow.name
      uid:
        expression: workflow.uid
      integration-id:
        expression: sprig.trimAll('.-_', sprig.trunc(63, sprig.regexReplaceAll('[^A-Za-z0-9_.-]',
          '-', '{{`{{workflow.parameters.integration-id}}`}}')))
