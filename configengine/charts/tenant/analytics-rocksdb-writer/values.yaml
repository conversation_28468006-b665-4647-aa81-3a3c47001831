service: {}

revisionHistoryLimit: 0
podManagementPolicy: Parallel
automountServiceAccountToken: false
terminationGracePeriodSeconds: 3600
priorityClassName: "high-priority-deployment"

configmap:
  nameOverride: ""
commonConfig: {}
config: {}

persistentVolumeClaimRetentionPolicy:
  whenDeleted: Retain
  whenScaled: Retain

persistence:
  enabled: true
  namespace: default
  size: 8Gi
  labels: {}
  annotations: {}
  storageClassName: ssd-storage-no-replication
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem

serviceAccount:
  create: true
  automountServiceAccountToken: false
  name: rocksdb-writer

replicas: 1

statefulset:
  updateStrategy:
    type: RollingUpdate

statefulsetAnnotations:
  reloader.stakater.com/auto: "true"
podAnnotations:
  prometheus.io/port: "8899"
  prometheus.io/scrape: "true"

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /ping
    port: 8899
    scheme: HTTP
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  httpGet:
    path: /ping
    port: 8899
    scheme: HTTP
  periodSeconds: 10
  successThreshold: 1

podSecurityContext:
  fsGroup: 888
  runAsNonRoot: false

deploymentLabels:
  group: dp
  team: analytics
podLabels:
  group: dp
  team: analytics

vsg:
  enabled: false
  pollingMinutes: 3
  project: ""
  oomScaling: {}
  verticalScaling: {}
  zeroScaling: {}
  pvcScaling: {}
