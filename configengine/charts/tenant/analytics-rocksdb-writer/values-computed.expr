_component_name: '"analytics-rocksdb-writer"'

fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'
namespaceOverride: "globals.st_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

_rocksdb_logic: license.pro_agents_for_scaling + (200 * license.monthly_tb_licenses)
_rocksdb_writer_cpu: |-
  infra_ff.is_enable_rocksdb_standalone ? 14 : 
  local._rocksdb_logic <= 1000 ? 0.5 : 
  (local._rocksdb_logic > 1000 && local._rocksdb_logic <= 2000) ? 2 :
  (local._rocksdb_logic > 2000 && local._rocksdb_logic <= 10000) ? 4 : 7
_rocksdb_writer_memory: |-
  infra_ff.is_enable_rocksdb_standalone ? "50Gi" : 
  local._rocksdb_logic <= 1000 ? "4Gi" : 
  (local._rocksdb_logic > 1000 && local._rocksdb_logic <= 2000) ? "6Gi" : 
  (local._rocksdb_logic > 2000 && local._rocksdb_logic <= 10000) ? "7Gi" : 
  "14Gi"

configmap:
  nameOverride: 'tenant.lcaas_id + "-configmap-rocksdb-writer"'

config:
  GONZO_LOGGER_TRANSPORT: '"console:0,slack:2"'
  GONZO_REDIS_CONNECTION_STRING: "globals.gonzo_redis_connection_string"
  ANALYTICS_OAKENSHIELDDB_MAX_CONCURRENT: |-
    !(infra_ff.is_enable_rocksdb_cluster_mode || infra_ff.enable_rocksdb_blue_green_mode) ?
    8 : nil
  ANALYTICS_OAKENSHIELDDB_ENV_NAME: 'infra_ff.enable_rocksdb_blue_green_mode ? "blue" : nil'
  ANALYTICS_OAKENSHIELDDB_IS_MASTER: "infra_ff.is_enable_rocksdb_cluster_mode ? true : nil"

persistence:
  size: 'infra_ff.is_enable_rocksdb_cluster_mode ? "300Gi" : "50Gi"'

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"rocksdb-writer" + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

nodeSelector:
  xdr-pool: 'infra_ff.is_enable_rocksdb_standalone ? "rocksdb" : "wi-dynamic"'

tolerations:
  - key: 'infra_ff.is_enable_rocksdb_standalone ? "xdr-pool" : ""'
    operator: 'infra_ff.is_enable_rocksdb_standalone ? "Equal" : "Exists"'
    value: 'infra_ff.is_enable_rocksdb_standalone ? "rocksdb" : ""'

isLivenessProbe: "!infra_ff.is_enable_rocksdb_cluster_mode"

readinessProbe:
  failureThreshold: "infra_ff.is_enable_rocksdb_cluster_mode ? 180 : 3"
  timeoutSeconds: "infra_ff.is_enable_rocksdb_cluster_mode ? 5 : 1"

resources:
  limits:
    cpu: |-
      infra_ff.enable_rocksdb_blue_green_mode || infra_ff.is_enable_rocksdb_cluster_mode ?
      globals.rocksdb_cpu : local._rocksdb_writer_cpu
    memory: |-
      infra_ff.enable_rocksdb_blue_green_mode || infra_ff.is_enable_rocksdb_cluster_mode ?
      globals.rocksdb_memory : local._rocksdb_writer_memory
  requests:
    cpu: |-
      infra_ff.enable_rocksdb_blue_green_mode || infra_ff.is_enable_rocksdb_cluster_mode ?
      globals.rocksdb_cpu : local._rocksdb_writer_cpu
    memory: |-
      infra_ff.enable_rocksdb_blue_green_mode || infra_ff.is_enable_rocksdb_cluster_mode ? globals.rocksdb_memory : local._rocksdb_writer_memory

env:
  ANALYTICS_OAKENSHIELDDB_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: '"analytics_redis_password"'
        name: globals.tenant_secrets
        optional: false
  GONZO_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: '"redis_password"'
        name: globals.tenant_secrets
        optional: false
  GONZO_SHUTDOWN_TIMEOUT_SEC:
    value: '"1h"'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-rocksdb-common"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-rocksdb-writer"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-rocksdb-writer-feature-flags"'
      optional: false


_vsgPvcScaling:
  create: infra_ff.enable_pipeline
  enabled: true
  project: tenant.project_id
  targetKind: "'statefulset'"
  pvcScaling:
    enabled: true
    diskUsagePercentThreshold: 85
    diskConstantIncrease: "'200Gi'"
    diskCapacityMax: "'3000Gi'"
    targetEndpointMetricSource:
      port: 6677
      route: "'/disk-usage'"


vsg: "infra_ff.is_enable_rocksdb_cluster_mode ? local._vsgPvcScaling : nil"
