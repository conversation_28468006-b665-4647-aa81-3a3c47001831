{{- if and .Values.vsg.create .Values.vsg.enabled (or .Values.vsg.verticalScaling.enabled .Values.vsg.oomScaling.enabled) -}}
  {{- $lookupChecksumResources := ((include "common.sts.lookupChecksumResources" .) | fromYaml) -}}
  {{- $_ := set .Values "resources" $lookupChecksumResources.resources -}}
  {{- $_ := set .Values "statefulsetAnnotations" (merge .Values.statefulsetAnnotations $lookupChecksumResources.annotations) -}}
{{- end -}}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  {{- with .Values.statefulsetAnnotations }}
  annotations: {{ toYaml . | nindent 4 }}
    {{- include "common.sre.panw.annotations" $ | nindent 4 }}
  {{- end }}
  labels:
    {{- include "common.sre.panw.labels" $ | nindent 4 }}
    {{- include "chart.labels" . | nindent 4 }}
    {{- with .Values.statefulsetLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "chart.fullname" . }}
  namespace: {{ include "common.namespace" . }}
spec:
  {{- with (include "common.deployment.replicas" .) }}
  replicas: {{ . }}
  {{- end }}
  selector:
    matchLabels: {{ include "chart.selectorLabels" . | nindent 6 }}
  {{- with .Values.statefulset.updateStrategy }}
  updateStrategy: {{ toYaml . | nindent 4 }}
  {{- end }}
  serviceName: analytics-rocksdb
  {{- with .Values.podManagementPolicy }}
  podManagementPolicy: {{ . }}
  {{- end }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit | int }}
  {{- with .Values.persistentVolumeClaimRetentionPolicy }}
  persistentVolumeClaimRetentionPolicy: {{ toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations: {{ toYaml . | nindent 8 }}
        {{- include "common.sre.panw.annotations" $ | nindent 8 }}
      {{- end }}
      labels:
        {{- include "common.sre.panw.labels" $ | nindent 8 }}
        {{- include "chart.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      containers:
        - name: {{ include "chart.fullname" . }}
          command:
            - /go/bin/analytics-rocksdb
          image: {{ printf "%s/%s:%s" .Values.image.registry .Values.image.repository .Values.image.tag }}
          imagePullPolicy: {{ default "IfNotPresent" .Values.image.pullPolicy }}
          ports:
            - containerPort: 8899
              protocol: TCP
            - containerPort: 7780
              protocol: TCP
          {{- with .Values.env }}
          env: {{ (include "common.renderEnv" .) | nindent 12 }}
          {{- end }}
          {{- with .Values.envFrom }}
          envFrom: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.startupProbe }}
          startupProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- if .Values.isLivenessProbe }}
          {{- with .Values.livenessProbe }}
          livenessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.containerSecurityContext }}
          securityContext: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.resources }}
          resources: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- if or .Values.persistence.enabled .Values.extraVolumeMounts }}
          volumeMounts:
            {{- if .Values.persistence.enabled }}
            - name: {{ include "rocksdb-writer.pvc.claimName" . }}
              mountPath: /data
              mountPropagation: None
            {{- end }}
            {{- with .Values.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
        - {{ toYaml $constraint | nindent 10 | trim }}
          {{- if not $constraint.labelSelector }}
          labelSelector:
            matchLabels: {{ include "chart.selectorLabels" $ | nindent 14 }}
          {{- end }}
        {{- end }}
      {{- end }}
      {{- with .Values.restartPolicy }}
      restartPolicy: {{ . }}
      {{- end }}
      {{- with .Values.podSecurityContext }}
      securityContext: {{ toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      {{- with .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . | int }}
      {{- end }}
      {{- with .Values.extraVolumes }}
      volumes: {{ toYaml . | nindent 8 }}
      {{- end }}
  {{- if .Values.persistence.enabled }}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: {{ include "rocksdb-writer.pvc.claimName" . }}
        {{- with .Values.persistence.namespace }}
        namespace: {{ . }}
        {{- end }}
        {{- with .Values.persistence.annotations }}
        annotations: {{ toYaml . | nindent 10 }}
          {{- include "common.sre.panw.annotations" $ | nindent 10 }}
        {{- end }}
        {{- with .Values.persistence.labels }}
        labels: {{ toYaml . | nindent 10 }}
          {{- include "common.sre.panw.labels" $ | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
          {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
          {{- end }}
        resources:
          requests:
            storage: {{ (include "rocksdb-writer.pvc.size" .) | quote }}
        {{- with (include "rocksdb-writer.pvc.storageClassName" .) }}
        storageClassName: {{ . }}
        {{- end }}
        {{- with .Values.persistence.volumeMode }}
        volumeMode: {{ . }}
        {{- end }}
        {{- with .Values.persistence.selectorLabels }}
        selector:
          matchLabels:
          {{- toYaml . | nindent 6 }}
        {{- end }}
  {{- end }}
