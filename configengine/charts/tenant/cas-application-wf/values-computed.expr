_component_application_job: "'application-job'"
_component_name_repository_urgency_runtime_metrics_job: "'repository-urgency-runtime-metrics-workflow'"

namespaceOverride: "globals.cas_namespace"

_images:
  application_job:
    component: "get(images.components, local._component_application_job)"
    family: "get(images.families, local._images.application_job.component.family)"

  repository_urgency_runtime_metrics_job:
    component: "get(images.components, local._component_name_repository_urgency_runtime_metrics_job)"
    family: "get(images.families, local._images.repository_urgency_runtime_metrics_job.component.family)"


applicationJob:
  image:
    registry: "globals.gcr"
    tag: "local._images.application_job.component.tag ?? local._images.application_job.family.tag"
    repository: "local._images.application_job.component.repository ?? local._images.application_job.family.repository"

repositoryUrgencyRuntimeMetricsCollectorWorkflow:
  image:
    registry: "globals.gcr"
    tag: "local._images.repository_urgency_runtime_metrics_job.component.tag ?? local._images.repository_urgency_runtime_metrics_job.family.tag"
    repository: "local._images.repository_urgency_runtime_metrics_job.component.repository ?? local._images.repository_urgency_runtime_metrics_job.family.repository"
    

waitAssetsSavingCompletionDuration: 'region.is_dev ? "10" : "900"'

environmentType: 'region.is_dev ? "dev" : "prod"'

mongo:
  password:
    secretName: "'infra-secrets'"
    secretKey: '"mongodb_password"'
  userName: "'root'"
  databaseName: '"cas"'



refreshApplicationsAssets:
  cronConfig:
    suspend: 'region.is_dev ? "true" : "false"'

refreshCriteriaApplications:
  cronConfig:
    suspend: 'region.is_dev ? "true" : "false"'

syncApplications:
  cronConfig:
    suspend: 'region.is_dev ? "true" : "false"'