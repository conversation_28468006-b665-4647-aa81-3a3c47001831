apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  labels:
    type: cron-refresh-applications-assets
  name: cron-refresh-applications-assets
  namespace: {{.Values.namespaceOverride}}
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 9999
  schedule: {{.Values.refreshApplicationsAssets.cronConfig.schedule}}
  successfulJobsHistoryLimit: 9999
  suspend: {{.Values.refreshApplicationsAssets.cronConfig.suspend}}
  workflowSpec:
    arguments:
      parameters:
      - name: trace-id
        value: '{{`{{workflow.uid}}`}}'
      - name: scan-id
        value: '{{`{{workflow.uid}}`}}'
    entrypoint: main
    metrics:
      prometheus:
      - help: Workflow duration by name
        histogram:
          buckets:
          - 5.0
          - 30.0
          - 60.0
          - 120.0
          - 300.0
          - 600.0
          - 1800.0
          - 3600.0
          value: '{{`{{workflow.duration}}`}}'
        labels:
        - key: name
          value: cron_refresh_applications_assets
        - key: namespace
          value: {{.Values.namespaceOverride}}
        - key: status
          value: '{{`{{workflow.status}}`}}'
        name: workflow_duration_secs
    templates:
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: getBulkApplications
        - name: APPLICATIONS_FILE_NAMES_FOLDER
          value: /tmp/applications_output
        - name: APPLICATIONS_FILE_NAME
          value: applications_file_names.json
        - name: APPLICATIONS_FILES_FOLDER
          value: /tmp/applications_output/files
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: get_bulk_applications
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      inputs:
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: get_bulk_applications
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: get-bulk-applications
      outputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: application/{{`{{workflow.uid}}`}}/application_ids_files
          name: application_files
          optional: true
          path: /tmp/applications_output/files
        parameters:
        - name: applications_file_names
          valueFrom:
            path: /tmp/applications_output/applications_file_names.json
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: discoverApplicationsAssets
        - name: BATCH_ID
          value: '{{`{{inputs.parameters.batch-id}}`}}'
        - name: BATCH_SIZE
          value: {{.Values.discoverApplicationsAssets.batchSize}}
        - name: MAX_CONCURRENCY
          value: {{.Values.discoverApplicationsAssets.maxConcurrency}}
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: INPUT_FILE_PATHS
          value: '{{`{{inputs.parameters.application-ids-disk-path}}`}}'
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: discover_applications_assets
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      inputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: '{{`{{inputs.parameters.application-ids-remote-path}}`}}'
          mode: 644
          name: application-ids
          path: '{{`{{inputs.parameters.application-ids-disk-path}}`}}'
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
        - name: application-ids-disk-path
        - name: application-ids-remote-path
        - name: batch-id
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: discover_applications_assets
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: discover-applications-assets
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - inputs:
        parameters:
        - name: application-ids-file-names
        - name: application-files-bucket-path
      name: loop-discover-applications-assets
      parallelism: 2
      steps:
      - - arguments:
            parameters:
            - name: application-ids-remote-path
              value: '{{`{{inputs.parameters.application-files-bucket-path}}`}}/{{`{{item}}`}}'
            - name: application-ids-disk-path
              value: /tmp/{{`{{item}}`}}
            - name: batch-id
              value: '{{`{{workflow.uid}}`}}_{{`{{item}}`}}'
          name: discover-applications-assets
          template: discover-applications-assets
          withParam: '{{`{{inputs.parameters.application-ids-file-names}}`}}'
    - name: main
      steps:
      - - name: get-bulk-applications
          template: get-bulk-applications
      - - arguments:
            parameters:
            - name: application-ids-file-names
              value: '{{`{{steps.get-bulk-applications.outputs.parameters.applications_file_names}}`}}'
            - name: application-files-bucket-path
              value: application/{{`{{workflow.uid}}`}}/application_ids_files
          name: discover-applications-assets-loop
          template: loop-discover-applications-assets
          when: '{{`{{=steps[''get-bulk-applications''].outputs.parameters[''applications_file_names'']
            != ''[]''}}`}}'
    workflowMetadata:
      labelsFrom:
        name:
          expression: workflow.name
        uid:
          expression: workflow.uid
        scan-id:
          expression: workflow.uid
        trace-id:
          expression: workflow.uid
