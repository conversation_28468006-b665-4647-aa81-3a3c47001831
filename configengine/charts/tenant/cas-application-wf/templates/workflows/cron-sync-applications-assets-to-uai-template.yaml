apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  labels:
    type: cron-sync-applications-assets-to-uai-template
  name: cron-sync-applications-assets-to-uai-template
  namespace: {{.Values.namespaceOverride}}
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 9999
  schedule: {{.Values.syncApplications.cronConfig.schedule}}
  successfulJobsHistoryLimit: 9999
  suspend: {{.Values.syncApplications.cronConfig.suspend}}
  workflowSpec:
    arguments:
      parameters:
      - name: trace-id
        value: '{{`{{workflow.uid}}`}}'
      - name: scan-id
        value: '{{`{{workflow.uid}}`}}'
    entrypoint: main
    metrics:
      prometheus:
      - help: Workflow duration by name
        histogram:
          buckets:
          - 5.0
          - 30.0
          - 60.0
          - 120.0
          - 300.0
          - 600.0
          - 1800.0
          - 3600.0
          value: '{{`{{workflow.duration}}`}}'
        labels:
        - key: name
          value: cron_sync_applications_assets_to_uai_template
        - key: namespace
          value: {{.Values.namespaceOverride}}
        - key: status
          value: '{{`{{workflow.status}}`}}'
        name: workflow_duration_secs
    templates:
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: deleteMarkedCriteriaApplicationJobRunner
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: delete_marked_criteria_application
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: delete_marked_criteria_application
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: delete-marked-criteria-application
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: syncApplicationsAssetsToUAI
        - name: EFFECTED_APPLICATION_RESULTS_OUTPUT_FOLDER_PATH
          value: /tmp/effected_application_ids
        - name: EFFECTED_APPLICATION_IDS_OUTPUT_FILE_NAME
          value: application_ids.json
        - name: IS_EFFECTED_APPLICATION_IDS_EXIST_OUTPUT_FILE_NAME
          value: is_application_ids_exist.txt
        - name: RUN_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: BATCH_SIZE
          value: {{.Values.syncApplicationsAssets.batchSize}}
        - name: MAX_CONCURRENCY
          value: {{.Values.syncApplicationsAssets.maxConcurrency}}
        - name: MAX_CONCURRENCY_PUB_SUB
          value: {{.Values.syncApplicationsAssets.maxConcurrencyPubSub}}
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: get_applications_assets_to_sync
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
        resources:
          limits:
            cpu: '2'
            memory: 1Gi
          requests:
            cpu: '1'
            memory: 512Mi
      inputs:
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: get_applications_assets_to_sync
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: get-applications-assets-to-sync
      outputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: application/sync-applications/{{`{{workflow.uid}}`}}/effected_application_ids
          name: effected_application_ids
          optional: true
          path: /tmp/effected_application_ids/application_ids.json
        parameters:
        - name: is_effected_applications_ids_exist
          valueFrom:
            path: /tmp/effected_application_ids/is_application_ids_exist.txt
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: calculateRiskScore
        - name: APPLICATION_IDS_FILE_PATH
          value: /tmp/application_ids
        - name: RUN_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: BATCH_SIZE
          value: {{.Values.calculateRiskScore.batchSize}}
        - name: MAX_CONCURRENCY
          value: {{.Values.calculateRiskScore.maxConcurrency}}
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: calculate_risk_score_step
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      inputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: application/sync-applications/{{`{{workflow.uid}}`}}/effected_application_ids
          mode: 644
          name: application-ids
          optional: true
          path: /tmp/application_ids
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: calculate_risk_score_step
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: calculate-risk-score-step
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: calculateCriteriaCounters
        - name: APPLICATION_IDS_FILE_PATH
          value: /tmp/application_ids
        - name: RUN_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: BATCH_SIZE
          value: {{.Values.calculateCriteriaCounters.batchSize}}
        - name: MAX_CONCURRENCY
          value: {{.Values.calculateCriteriaCounters.maxConcurrency}}
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: calculate_criteria_counters_step
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      inputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: application/sync-applications/{{`{{workflow.uid}}`}}/effected_application_ids
          mode: 644
          name: application-ids
          optional: true
          path: /tmp/application_ids
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: calculate_criteria_counters_step
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: calculate-criteria-counters-step
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: calculateApplicationsCounters
        - name: APPLICATION_IDS_FILE_PATH
          value: /tmp/application_ids
        - name: RUN_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: BATCH_SIZE
          value: {{.Values.calculateApplicationsCounters.batchSize}}
        - name: MAX_CONCURRENCY
          value: {{.Values.calculateApplicationsCounters.maxConcurrency}}
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: calculate_applications_counters_step
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      inputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: application/sync-applications/{{`{{workflow.uid}}`}}/effected_application_ids
          mode: 644
          name: application-ids
          optional: true
          path: /tmp/application_ids
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: calculate_applications_counters_step
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: calculate-applications-counters-step
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - container:
        command:
        - node
        - apps/application/dist/application-job.js
        env:
        - name: MONGO_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: {{.Values.mongo.password.secretKey}}
              name: {{.Values.mongo.password.secretName}}
        - name: MONGO_DB_HOST_NAME
          valueFrom:
            configMapKeyRef:
              key: MONGO_DB_HOST_NAME
              name: cas-configmap
        - name: MONGO_DB_USERNAME
          value: {{.Values.mongo.userName}}
        - name: MONGO_DB_DATABASE_NAME
          value: {{.Values.mongo.databaseName}}
        - name: LCAAS_ID
          valueFrom:
            configMapKeyRef:
              key: LCAAS_ID
              name: cas-configmap
        - name: PROJECT_ID
          valueFrom:
            configMapKeyRef:
              key: GCPCONF_PROJECT_ID
              name: cas-configmap
        - name: CORTEX_PLATFORM_URL
          valueFrom:
            configMapKeyRef:
              key: CORTEX_PLATFORM_URL
              name: cas-configmap
        - name: ENV
          value: {{.Values.environmentType}}
        - name: METRICS_NAMESPACE
          value: applications_job
        - name: FLOW_TO_EXECUTE
          value: deleteIdenticalApplications
        - name: APPLICATION_IDS_FILE_PATH
          value: /tmp/application_ids
        - name: RUN_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: BATCH_SIZE
          value: {{.Values.deleteIdenticalApplications.batchSize}}
        - name: MAX_CONCURRENCY
          value: {{.Values.deleteIdenticalApplications.maxConcurrency}}
        - name: TRACE_ID
          value: '{{`{{workflow.parameters.trace-id}}`}}'
        - name: WORKFLOW_ID
          value: '{{`{{workflow.uid}}`}}'
        - name: JOB_NAME
          value: delete_identical_applications_step
        - name: WORKFLOW_NAME
          value: '{{`{{workflow.name}}`}}'
        - name: MIN_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              key: MIN_LOG_LEVEL
              name: cas-configmap
        - name: METRICS_RECEIVER_URL
          valueFrom:
            configMapKeyRef:
              key: METRICS_RECEIVER_URL
              name: cas-configmap
              optional: true
        envFrom:
        - configMapRef:
            name: cas-configmap-feature-flags-defaults
        - configMapRef:
            name: cas-configmap-feature-flags
        image: '{{.Values.applicationJob.image.registry}}/{{.Values.applicationJob.image.repository}}:{{
          .Values.applicationJob.image.tag}}'
        imagePullPolicy: {{.Values.applicationJob.image.pullPolicy}}
      inputs:
        artifacts:
        - archive:
            none: {}
          gcs:
            bucket: '{{`{{inputs.parameters.bucket-name}}`}}'
            key: application/sync-applications/{{`{{workflow.uid}}`}}/effected_application_ids
          mode: 644
          name: application-ids
          optional: true
          path: /tmp/application_ids
        parameters:
        - name: bucket-name
          valueFrom:
            configMapKeyRef:
              key: WORKFLOWS_ARTIFACTS_BUCKET
              name: cas-configmap
      metrics:
        prometheus:
        - help: job duration by name
          histogram:
            buckets:
            - 30.0
            - 60.0
            - 300.0
            value: '{{`{{duration}}`}}'
          labels:
          - key: job_name
            value: delete_identical_applications_step
          - key: status
            value: '{{`{{status}}`}}'
          name: job_duration_seconds
      name: delete-identical-applications-step
      retryStrategy:
        affinity:
          nodeAntiAffinity: {}
        backoff:
          duration: '10'
          factor: 2
        expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1
          or indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
          ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
          code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
          low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not
          find default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
          local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
          == ''OnError'''
        limit: 4
      serviceAccountName: {{.Values.serviceAccount.name}}
      timeout: 6h
    - name: main
      steps:
      - - name: sync-deleted-applications-from-mongo
          template: delete-marked-criteria-application
      - - name: sync-applications-assets
          template: get-applications-assets-to-sync
      - - name: calculate-risk-score
          template: calculate-risk-score-step
          when: '{{`{{=steps[''sync-applications-assets''].outputs.parameters[''is_effected_applications_ids_exist'']
            == ''true''}}`}}'
        - name: calculate-criteria-counters
          template: calculate-criteria-counters-step
          when: '{{`{{=steps[''sync-applications-assets''].outputs.parameters[''is_effected_applications_ids_exist'']
            == ''true''}}`}}'
        - name: calculate-applications-counters
          template: calculate-applications-counters-step
          when: '{{`{{=steps[''sync-applications-assets''].outputs.parameters[''is_effected_applications_ids_exist'']
            == ''true''}}`}}'
        - name: delete-identical-applications
          template: delete-identical-applications-step
          when: '{{`{{=steps[''sync-applications-assets''].outputs.parameters[''is_effected_applications_ids_exist'']
            == ''true''}}`}}'
    workflowMetadata:
      labelsFrom:
        name:
          expression: workflow.name
        uid:
          expression: workflow.uid
        scan-id:
          expression: workflow.uid
        trace-id:
          expression: workflow.uid
