nodeSelector: "" # must be empty so we can schedule pods in local cluster
namespace: cas

serviceAccount:
  name: default

applicationJob:
  image: # replace with {} after the migration to Cortex new platform
    registry: localhost:5001
    repository: application-application-job
    pullPolicy: Always
    tag: latest


repositoryUrgencyRuntimeMetricsCollectorWorkflow:
  image:  # replace with {} after the migration to Cortex new platform
    registry: localhost:5001
    repository: application-repository-urgency-runtime-metrics-collector
    pullPolicy: Always
    tag: latest