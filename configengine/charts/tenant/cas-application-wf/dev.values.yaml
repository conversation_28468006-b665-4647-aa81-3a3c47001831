environmentType: dev # remove after migrating to dev-new-platform (dynamic value in values-computed.expr)

applicationJob:
  image: # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-dev-01
    repository: cas-docker/application-application-job
    pullPolicy: Always
    tag: dev-v0.0.0-latest

repositoryUrgencyRuntimeMetricsCollectorWorkflow:
  image:  # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-dev-01
    repository: cas-docker/application-repository-urgency-runtime-metrics-collector
    pullPolicy: Always
    tag: dev-v0.0.0-latest

METRICS_RECEIVER_URL:
  valueFrom:
    configMapKeyRef:
      name: cas-configmap
      key: METRICS_RECEIVER_URL
      optional: false