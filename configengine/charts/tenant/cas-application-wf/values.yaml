persistenceWorkflowTemplateName: multi-persist-template
waitAssetsSavingCompletionDuration: 900
environmentType: prod # remove after migrating to dev-new-platform (dynamic value in values-computed.expr)


applicationJob:
  image: # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/application-application-job
    tag: "" # will be replaced on demand
    pullPolicy: IfNotPresent

repositoryUrgencyRuntimeMetricsCollectorWorkflow:
  serviceAccount:
    automountServiceAccountToken: true
    name: aspm-issue-urgency
  cronConfig:
    suspend: false
    schedule: 0 */8 * * * # trigger every 8 hours
  image: # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/application-repository-urgency-runtime-metrics-collector
    tag: "" # will be replaced on demand
    pullPolicy: IfNotPresent
  mongo:
    collectionName: repository_urgency_runtime_metrics

refreshApplicationsAssets:
  cronConfig:
    suspend: false
    schedule: 0 */4 * * * # trigger every 4 hours


refreshCriteriaApplications:
  cronConfig:
    suspend: false
    schedule: 0 */12 * * * # trigger every 12 hours

syncApplications:
  cronConfig:
    suspend: false
    schedule: 0 */1 * * * # trigger every 1 hours

cronConfig:
  suspend: true
  schedule: 0 */4 * * * # trigger every 4 hours

# -- secret to use for image pulling
imagePullSecrets: []

namespace: cas
namespaceOverride: cas # remove after the migration to Cortex new platform

serviceAccount:
  # -- # Set to false to prevent automatic mounting of the service account token into the pod
  automountServiceAccountToken: true
  # -- The name of the service account to use.
  # cas service account would be created using cas-service-accounts chart
  name: "application-flow-cas"

concurrency: 1

securityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1001

workflowLabels:
  group: cas
  team: aspm-app-stream
podLabels:
  group: cas
  team: aspm-app-stream

automountServiceAccountToken: true

mongo:
  password:
    secretName: infra-secrets
    secretKey: mongodb_password
  userName: root
  databaseName: cas

parallelism:
  deleteApplicationFromAssets: 1
  syncApplicationAssets: 1

discoverApplicationsAssets:
  batchSize: "'500'"
  maxConcurrency: "'2'"

calculateApplicationsCounters:
  batchSize: "'500'"
  maxConcurrency: "'2'"

calculateCriteriaCounters:
  batchSize: "'1000'"
  maxConcurrency: "'2'"

calculateRiskScore:
  batchSize: "'500'"
  maxConcurrency: "'2'"

deleteIdenticalApplications:
  batchSize: "'500'"
  maxConcurrency: "'2'"

syncApplicationsAssets:
  batchSize: "'1000'"
  maxConcurrency: "'1'"
  maxConcurrencyPubSub: "'40'"
