env: {}
envFrom: []
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}
terminationGracePeriodSeconds: ""
automountServiceAccountToken: true

cronjob:
  annotations: {}
  labels: {}
  backoffLimit: 0
  schedule: "0 * * * *"
  concurrencyPolicy: Forbid
  activeDeadlineSeconds: ""
  successfulJobsHistoryLimit: 4
  ttlSecondsAfterFinished: 1800
  parallelism: 1
  completions: 1
  suspend: false

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: cwp
  team: core
podLabels:
  group: cwp
  team: core
podAnnotations: {}

nodeSelector:
  xdr-pool: "wi-dynamic"

serviceAccount:
  create: true
  automountServiceAccountToken: false

restartPolicy: "OnFailure"

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2
  initialDelaySeconds: 30

podSecurityContext: {}

image: {}
