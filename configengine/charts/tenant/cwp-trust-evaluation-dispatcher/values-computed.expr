_component_name: "'cwp-trust-dispatcher'"

fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'
namespaceOverride: "globals.cwp_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  CWP_FPP_ASSET_TOPIC_NAME: '"cwp-trust-evaluation-asset-" + tenant.lcaas_id'
  CWP_FPP_DISPATCHER_ASSET_EXPORTER_BUCKET: 'tenant.lcaas_id + "-cwp-trust-evaluation-assets"'
  CWP_FPP_HANDLER_TYPE: '"dispatcher"'
  CWP_FPP_DISPATCHER_ASSET_EXPORTER_TYPE: '"bq"'
  CWP_FPP_BIGQUERY_QUERY_TYPE: '"workload_runtime_image_pairs"'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
    memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
  limits:
    cpu: '"1"'
    memory: '"1Gi"'
