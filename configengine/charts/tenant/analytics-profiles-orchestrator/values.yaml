envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
priorityClassName: ""
topologySpreadConstraints: {}
terminationGracePeriodSeconds: ""
automountServiceAccountToken: true

env:
  SignedPickleConf_pod_secret:
    valueFrom:
      secretKeyRef:
        name: signed-pickle-analytics-profiles-orchestrator-secret
        key: signed_pickle_analytics_profiles_orchestrator
        optional: false
  AnalyticsConf_salt_key:
    valueFrom:
      secretKeyRef:
        name: analytics-conf-salt-key-secret
        key: analytics-conf-salt-key
        optional: false

serviceAccount:
  create: true
  automountServiceAccountToken: false
  annotations: {}

deployment:
  strategy:
    type: Recreate
deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: dp
  team: analytics
podLabels:
  group: dp
  team: analytics
podAnnotations:
  prometheus.io/port: "4982"
  prometheus.io/scrape: "true"

livenessProbe:
  failureThreshold: 20
  httpGet:
    path: "/ping/"
    port: 4982
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 5

readinessProbe: {}

startupProbe:
  httpGet:
    path: "/ping/"
    port: 4982
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5 

podSecurityContext:
  fsGroup: 888
  runAsNonRoot: false

containerSecurityContext:
  runAsUser: 888
  runAsGroup: 888
  allowPrivilegeEscalation: false   

nodeSelector:
  xdr-pool: "wi-dynamic"

vsg:
  enabled: false
  pollingMinutes: 5
  project: ""
  oomScaling: {}
  verticalScaling: {}
  zeroScaling: {}
