_component_name: "'analytics-profiles-orchestrator'"

namespaceOverride: globals.st_namespace
fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  MYSQLCONF_USER: 'tenant.is_metro_tenant ? string(tenant.metro_tenant_index) + "-profiles_orchestrator" : "analytics_profiles_orchestrator"'
  ScyllaConf_user:
    value: "'user'"
  ScyllaConf_scylla_endpoint:
    value: "globals.scylla_endpoint"
  ScyllaConf_password:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'scylla_password'"
        optional: false
  XCLOUDREDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "globals.xcloud_redisconf_password"
        optional: false
  PUBLICAPI_ENCRYPTION_256KEY:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'publicapi_encryption_256key'"
        optional: false
  MYSQLCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'mysql_password'"
        name: 'tenant.is_metro_tenant ? globals.st_resource_prefix + "-analytics-profiles-orchestrator-mysql" : globals.tenant_secrets'
        optional: false
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'redis_password'"
        name: "globals.tenant_secrets"
        optional: false
  ANALYTICSCONF_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'analytics_redis_password'"
        name: "globals.tenant_secrets"
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-feature-flags"'
      optional: false

serviceAccount:
  name: "'analytics-po'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

startupProbe:
  failureThreshold: "globals.startup_probe.failure_threshold"

resources:
  limits:
    cpu: '"500m"'
    memory: '"1.5Gi"'
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "100m"'
    memory: '"300Mi"'

vsg:
  create: infra_ff.enable_pipeline
  enabled: true
  project: tenant.project_id
  oomScaling:
    cooldownMinutes: 15
    enabled: true
    memoryConstantIncrease: "'512Mi'"
    podRequestMemoryMax: "'8Gi'"
    prometheusMetricSource:
      container: local.fullnameOverride
      threshold: 2
      windowMinutes: 360
