apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  labels:
    type: periodic-customer-scan
  name: periodic-customer-scan
  namespace: {{.Values.namespaceOverride}}
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 9999
  schedule: {{.Values.periodScans.schedule}}
  successfulJobsHistoryLimit: 9999
  suspend: {{.Values.periodScans.disabled}}
  workflowSpec:
    arguments:
      parameters:
      - name: customer-name
        valueFrom:
          configMapKeyRef:
            key: LCAAS_ID
            name: cas-configmap
      - name: trace-id
        value: '{{`{{workflow.uid}}`}}'
      - name: scan-id
        value: '{{`{{workflow.uid}}`}}'
    entrypoint: main
    metrics:
      prometheus:
      - help: Workflow duration by name
        histogram:
          buckets:
          - 5.0
          - 30.0
          - 60.0
          - 120.0
          - 300.0
          - 600.0
          - 1800.0
          - 3600.0
          value: '{{`{{workflow.duration}}`}}'
        labels:
        - key: name
          value: periodic_customer_scan
        - key: namespace
          value: {{.Values.namespaceOverride}}
        - key: status
          value: '{{`{{workflow.status}}`}}'
        name: workflow_duration_secs
    priority: 10
    templates:
    - name: main
      steps:
      - - arguments:
            parameters:
            - name: scan-id
              value: '{{`{{workflow.uid}}`}}'
            - name: customer-name
              value: '{{`{{workflow.parameters.customer-name}}`}}'
            - name: trace-id
              value: '{{`{{workflow.uid}}`}}'
          continueOn:
            error: true
            failed: true
          name: customer-scan
          templateRef:
            name: customer-scan-template
            template: main
      - - arguments:
            parameters:
            - name: scan-id
              value: '{{`{{workflow.uid}}`}}'
            - name: customer-name
              value: '{{`{{workflow.parameters.customer-name}}`}}'
            - name: trace-id
              value: '{{`{{workflow.uid}}`}}'
            - name: periodic-status
              value: '{{`{{=steps[''customer-scan''].status}}`}}'
          name: periodic-scan-end
          templateRef:
            name: periodic-scan-end
            template: main
    workflowMetadata:
      labelsFrom:
        name:
          expression: workflow.name
        uid:
          expression: workflow.uid
        scan-id:
          expression: workflow.uid
        trace-id:
          expression: workflow.uid
