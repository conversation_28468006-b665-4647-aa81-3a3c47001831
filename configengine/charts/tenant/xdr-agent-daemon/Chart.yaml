apiVersion: v2
name: xdr-agent-daemon
description: A Helm chart for Kubernetes
type: application
version: 0.1.8
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
appVersion: "0.1.1"
annotations:
  "panw.com/deploy-eval": "(tenant.upgrade_phase != 'P4' || region.is_fedramp || region.viso_env == 'dev') && !tenant.is_metro_tenant && !license.is_small_epp"
  owner.panw/group: cortex
  owner.panw/team: devops
  owner.panw/team-slack-handle: 'cortex-devops-all'
  owner.panw/people-slack-handle-owners-group: '@cortex-devops-all'
  owner.panw/people-slack-handle-team-lead: '@dpankov<PERSON>'
  owner.panw/source-code-ops-helm-chart-url: ' https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/xdr-agent-daemon'