envFrom: []
daemonset: {}
affinity: {}

restartPolicy: Always
priorityClassName: xdr-agent-priority
priorityClassValue: 90
topologySpreadConstraints: {}
terminationGracePeriodSeconds: 30
automountServiceAccountToken: false

fullnameOverride: xdr-agent-daemon
namespaceOverride: xdr-agent-daemon

serviceAccount:
  create: true
  name: xdr-agent-user
  automountServiceAccountToken: false

image:
  registry: us-central1-docker.pkg.dev/xdr-us-********
  repository: agent-docker/cortex-agent
  tag: ""

imagePullSecrets:
  - name: cortex-docker-secret

daemonsetAnnotations: {}

daemonsetLabels:
  app.kubernetes.io/part-of: cortex
  app.kubernetes.io/component: agent
  app.kubernetes.io/version: 8.7.100.136016

podSecurityContext:
  appArmorProfile:
    type: Unconfined

livenessProbe: {}
readinessProbe: {}
startupProbe: {}

containerSecurityContext:
  privileged: false
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: false
  runAsGroup: 0
  runAsNonRoot: false
  runAsUser: 0
  capabilities:
    add:
      - SYS_ADMIN
      - SYS_CHROOT
      - SYS_MODULE
      - SYS_PTRACE
      - SYS_RESOURCE
      - SYS_RAWIO
      - DAC_OVERRIDE
      - DAC_READ_SEARCH
      - NET_ADMIN
      - NET_RAW
      - IPC_LOCK
      - FOWNER
      - KILL
      - SETGID
      - SETUID

hostNetwork: true
hostPID: true
hostIPC: true

nodeSelector:
  kubernetes.io/os: linux

env:
  XDR_HOST_ROOT: /host-fs
  XDR_POD_INFO: /var/run/pod-info
  XDR_CLUSTER_NAME_URL: metadata2
  XDR_PROXY_LIST: ************:13128

resources:
  requests:
    memory: 128Mi
    cpu: 100m
  limits:
    memory: 4Gi

tolerations:
  - operator: Exists
    effect: NoSchedule
  - operator: Exists
    effect: PreferNoSchedule
  - operator: Exists
    effect: NoExecute

extraVolumeMounts:
  - name: host-fs
    mountPath: /host-fs
    readOnly: true
    mountPropagation: None
  - name: var-log
    mountPath: /var/log
    mountPropagation: None
  - name: host-km-directory
    mountPath: /lib/modules
    mountPropagation: None
  - name: pod-info
    mountPath: /var/run/pod-info
    readOnly: true
    mountPropagation: None
  - name: agent-ids
    mountPath: /etc/traps
    mountPropagation: None
  - name: deployment-secrets
    mountPath: /opt/traps/config/deployment
    readOnly: true
    mountPropagation: None

extraVolumes:
  - name: host-fs
    hostPath:
      path: /
      type: Directory
  - name: var-log
    hostPath:
      path: /var/log
      type: Directory
  - name: host-km-directory
    hostPath:
      path: /lib/modules
      type: Directory
  - name: agent-ids
    hostPath:
      path: /etc/traps
      type: DirectoryOrCreate
  - name: deployment-secrets
    secret:
      secretName: xdr-agent-deployment
      optional: false
  - downwardAPI:
      defaultMode: 420
      items:
        - path: uid
          fieldRef:
            fieldPath: metadata.uid
        - path: name
          fieldRef:
            fieldPath: metadata.name
        - path: labels
          fieldRef:
            fieldPath: metadata.labels
        - path: annotations
          fieldRef:
            fieldPath: metadata.annotations
    name: pod-info
