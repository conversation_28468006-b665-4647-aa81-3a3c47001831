_crtx_integration_url_redirect_map:
  default: '"https://integration.crtx."+ globals.region_short_code + ".paloaltonetworks.com"'
  dev: '"https://integration.crtx-qa2-uat.us.paloaltonetworks.com"'
  prod-fr: '"https://integration.crtx.federal.paloaltonetworks.com"'

casConfigMap:
  namespaceOverride: globals.cas_namespace

  configmap:
    nameOverride: '"cas-configmap"'

  config:
    CAS_REQUESTS_PAYLOAD_BUCKET: 'tenant.project_id+"-cas-requests-payload"'
    CI_BUILD_LOGS_BUCKET: 'tenant.project_id+"-ci-build-logs"'
    CODE_TO_CLOUD_BUCKET: 'tenant.project_id+"-code-to-cloud"'
    CLI_GIT_USERS_PERSISTENCE_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas_cli_git_users_persistence_sub-"+tenant.lcaas_id'
    CLI_GIT_USERS_PERSISTENCE_TOPIC: '"projects/"+tenant.project_id+"/topics/cas_cli_git_users_persistence-"+tenant.lcaas_id'
    CORTEX_ASSETS_OBSERVATION_PUB_SUB_TOPIC_NAME: '"projects/"+tenant.project_id+"/topics/dp-uai-asset-observations-"+tenant.lcaas_id'
    CORTEX_FINDINGS_BUCKET_NAME: 'tenant.project_id + "-dp-finding-emits"'
    CORTEX_SCAN_LOGS_BUCKET_NAME: 'tenant.lcaas_id + "-dp-scan-logs"'
    CORTEX_ISSUES_PERSIST_PUB_SUB_TOPIC_NAME: '"projects/"+tenant.project_id+"/topics/ap-issue-upsert-"+tenant.lcaas_id'
    CORTEX_PLATFORM_URL: 'globals.env.CORTEX_PLATFORM_URL'
    CRTX_INTEGRATION_URL: 'get(local._crtx_integration_url_redirect_map, region.viso_env) ?? local._crtx_integration_url_redirect_map["default"]'
    EGRESSPROXY_URL: globals.egress_proxy_address
    FINDING_INGESTION_ERRORS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/dp-finding-ingestion-errors-cas-"+tenant.lcaas_id+"-sub"'
    FINDING_INGESTION_ERRORS_NEW_PERSISTENCE_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/dp-finding-ingestion-errors-new-persistence-cas-"+tenant.lcaas_id+"-sub"'
    FINDING_INGESTION_ERRORS_TOPIC: '"projects/"+tenant.project_id+"/topics/dp-finding-ingestion-errors-"+tenant.lcaas_id'
    ASSET_INGESTION_ERRORS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/dp-uai-asset-ingestion-errors-cas-"+tenant.lcaas_id+"-sub"'
    ASSET_INGESTION_ERRORS_NEW_PERSISTENCE_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/dp-uai-asset-ingestion-errors-new-persistence-cas-"+tenant.lcaas_id+"-sub"'
    ASSET_INGESTION_ERRORS_TOPIC: '"projects/"+tenant.project_id+"/topics/dp-uai-asset-ingestion-errors-"+tenant.lcaas_id'
    GCPCONF_PROJECT_ID: tenant.project_id
    GCPCONF_REGION: tenant.bq_location
    GENERIC_EXTERNAL_FQDN: globals.external_fqdn
    IS_PROD: 'region.viso_env == "dev" ? "false" : "true"'
    ISSUE_INGESTION_FEEDBACK_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/ap-issue-ingest-feedback-cas-"+tenant.lcaas_id+"-sub"'
    ISSUE_INGESTION_FEEDBACK_NEW_PERSISTENCE_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/ap-issue-ingest-feedback-new-persistence-cas-"+tenant.lcaas_id+"-sub"'
    ISSUE_INGESTION_FEEDBACK_TOPIC: '"projects/"+tenant.project_id+"/topics/ap-issue-ingest-feedback-"+tenant.lcaas_id'
    ISSUES_INGESTION_ERRORS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/ap-issue-ingestion-errors-cas-"+tenant.lcaas_id+"-sub"'
    ISSUES_INGESTION_ERRORS_NEW_PERSISTENCE_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/ap-issue-ingestion-errors-new-persistence-cas-"+tenant.lcaas_id+"-sub"'
    ISSUES_INGESTION_ERRORS_TOPIC: '"projects/"+tenant.project_id+"/topics/ap-issue-ingestion-errors-"+tenant.lcaas_id'
    JENKINS_PLUGIN_BUCKET: 'region.is_dev ? "panw-jenkins-plugin-dev" : "panw-jenkins-plugin-prod-us"'
    LCAAS_ID: tenant.lcaas_id
    MIN_LOG_LEVEL: 'region.is_dev && !tenant.is_metro_tenant ? "DEBUG" : "INFO"'
    MONGO_DB_HOST_NAME: '"xdr-st-" + tenant.lcaas_id + "-cwp-mongodb.xdr-st.svc.cluster.local"'
    SBOM_REPORTS_BUCKET : 'tenant.project_id+"-sbom-reports"'
    SOURCE_CONTROL_BUCKET: 'tenant.project_id+"-source-control"'
    TRIGGER_ARGO_WF_SARIF_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-trigger-argo-wf-sarif-sub-"+tenant.lcaas_id'
    TRIGGER_ARGO_WF_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-trigger-argo-wf-"+tenant.lcaas_id'
    VCS_ENRICHMENT_CICD_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-vcs-enrichment-cicd-sub-"+tenant.lcaas_id'
    VCS_ENRICHMENT_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-vcs-enrichment-"+tenant.lcaas_id'
    PERIODIC_SCAN_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-periodic-scan-"+tenant.lcaas_id'
    PERIODIC_SCAN_SECRETS_S_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-periodic-scan-secrets-s-sub-"+tenant.lcaas_id'
    PR_SCAN_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pr-scan-"+tenant.lcaas_id'
    PR_SCAN_SECRETS_S_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pr-scan-secrets-s-sub-"+tenant.lcaas_id'
    SCANNER_TASKS_DEADLETTER_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-scanner-tasks-dl-"+tenant.lcaas_id'
    SCANNER_TASKS_DEADLETTER_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-scanner-tasks-dl-sub-"+tenant.lcaas_id'
    WEBHOOKS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-webhooks-sub-"+tenant.lcaas_id'
    WEBHOOKS_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-webhooks-"+tenant.lcaas_id'
    WEBHOOK_REQUESTS_INGESTION_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-webhook-requests-ingestion-"+tenant.lcaas_id'
    WEBHOOK_REQUESTS_INGESTION_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-webhook-requests-ingestion-sub-"+tenant.lcaas_id'
    WEBHOOK_REQUESTS_INGESTION_TOPIC_DLQ: '"projects/"+tenant.project_id+"/topics/cas-webhook-requests-ingestion-dlq-"+tenant.lcaas_id'
    WEBHOOK_REQUESTS_INGESTION_TOPIC_DLQ_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-webhook-requests-ingestion-dlq-sub-"+tenant.lcaas_id'
    POST_ENRICHMENT_SCANNER_REPORTS_PR_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-enrichment-scanner-reports-pr-"+tenant.lcaas_id'
    POST_ENRICHMENT_SCANNER_REPORTS_PERIODIC_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-enrichment-scanner-reports-periodic-"+tenant.lcaas_id'
    POST_ENRICHMENT_SCANNER_REPORTS_CLI_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-enrichment-scanner-reports-cli-"+tenant.lcaas_id'
    POST_ENRICHMENT_ACTIONS_PR_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-enrichment-actions-pr-sub-"+tenant.lcaas_id'
    POST_ENRICHMENT_ACTIONS_PERIODIC_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-enrichment-actions-periodic-sub-"+tenant.lcaas_id'
    POST_ENRICHMENT_ACTIONS_CLI_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-enrichment-actions-cli-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_SCANNER_REPORTS_PR_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pre-enrichment-scanner-reports-pr-"+tenant.lcaas_id'
    PRE_ENRICHMENT_SCANNER_REPORTS_PERIODIC_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pre-enrichment-scanner-reports-periodic-"+tenant.lcaas_id'
    PRE_ENRICHMENT_SCANNER_REPORTS_CLI_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pre-enrichment-scanner-reports-cli-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_PR_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-pr-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_PERIODIC_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-periodic-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_CLI_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-cli-sub-"+tenant.lcaas_id'
    POST_ENRICHMENT_SCANNER_REPORTS_PR_DLQ_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-enrichment-scanner-reports-pr-dlq-"+tenant.lcaas_id'
    POST_ENRICHMENT_SCANNER_REPORTS_PERIODIC_DLQ_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-enrichment-scanner-reports-periodic-dlq-"+tenant.lcaas_id'
    POST_ENRICHMENT_SCANNER_REPORTS_CLI_DLQ_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-enrichment-scanner-reports-cli-dlq-"+tenant.lcaas_id'
    POST_ENRICHMENT_ACTIONS_PR_DLQ_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-enrichment-scanner-reports-pr-dlq-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_SCANNER_REPORTS_PR_DLQ_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pre-enrichment-scanner-reports-pr-dlq-"+tenant.lcaas_id'
    PRE_ENRICHMENT_SCANNER_REPORTS_PERIODIC_DLQ_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pre-enrichment-scanner-reports-periodic-dlq-"+tenant.lcaas_id'
    PRE_ENRICHMENT_SCANNER_REPORTS_CLI_DLQ_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-pre-enrichment-scanner-reports-cli-dlq-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_PR_DLQ_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-pr-dlq-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_PERIODIC_DLQ_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-periodic-dlq-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_CLI_DLQ_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-cli-dlq-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_PR_ERRORS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-pr-errors-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_PERIODIC_ERRORS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-periodic-errors-sub-"+tenant.lcaas_id'
    PRE_ENRICHMENT_ACTIONS_CLI_ERRORS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-pre-enrichment-scanner-reports-cli-errors-sub-"+tenant.lcaas_id'
    POST_PERSISTENCE_PR_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-persistence-pr-"+tenant.lcaas_id'
    POST_PERSISTENCE_CLI_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-persistence-cli-"+tenant.lcaas_id'
    POST_PERSISTENCE_PERIODIC_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-post-persistence-periodic-"+tenant.lcaas_id'
    POST_PERSISTENCE_PR_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-persistence-pr-sub-"+tenant.lcaas_id'
    POST_PERSISTENCE_CLI_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-persistence-cli-sub-"+tenant.lcaas_id'
    POST_PERSISTENCE_PERIODIC_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-post-persistence-periodic-sub-"+tenant.lcaas_id'
    PRE_PERSISTENCE_PR_REPORTS_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-persistence-pr-"+tenant.lcaas_id'
    PR_LARGE_REPORTS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-persistence-pr-large-reports-sub-"+tenant.lcaas_id'
    PR_SMALL_REPORTS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-persistence-pr-small-reports-sub-"+tenant.lcaas_id'
    PRE_PERSISTENCE_CLI_REPORTS_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-persistence-cli-"+tenant.lcaas_id'
    CLI_LARGE_REPORTS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-persistence-cli-large-reports-sub-"+tenant.lcaas_id'
    CLI_SMALL_REPORTS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-persistence-cli-small-reports-sub-"+tenant.lcaas_id'
    PRE_PERSISTENCE_PERIODIC_REPORTS_TOPIC: '"projects/"+tenant.project_id+"/topics/cas-persistence-periodic-"+tenant.lcaas_id'
    PERIODIC_LARGE_REPORTS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-persistence-periodic-large-reports-sub-"+tenant.lcaas_id'
    PERIODIC_SMALL_REPORTS_SUBSCRIPTION: '"projects/"+tenant.project_id+"/subscriptions/cas-persistence-periodic-small-reports-sub-"+tenant.lcaas_id'
    WORKFLOWS_ARTIFACTS_BUCKET: 'tenant.project_id+"-argo-workflows-artifacts"'
    WORKFLOWS_LOGS_BUCKET: 'tenant.project_id+"-argo-workflows-logs"'
    WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET: 'tenant.project_id+"-argo-workflows-sensitive-customer-data"'
    ENRICHED_REPORTS_BUCKET: 'tenant.project_id+"-cas-enriched-reports"'
    CAS_MULTI_TENANT_PROJECT_ID: '"xdr-cas-" + region.multi_project_postfix + "-01"'
    DELETION_API_SUBSCRIPTION: '"projects/" + tenant.project_id + "/subscriptions/deletion-api-sub-" + tenant.lcaas_id'
    DELETION_API_TOPIC: '"projects/" + tenant.project_id + "/topics/deletion-api-" + tenant.lcaas_id'
    TRIGGER_ARGO_WF_JENKINS_INTEGRATION_LOGS_SUBSCRIPTION: '"projects/" + tenant.project_id + "/subscriptions/cas-jenkins-logs-trigger-argo-wf-plugin-sub-" + tenant.lcaas_id'
    TRIGGER_ARGO_WF_JENKINS_INTEGRATION_PIPELINE_SUBSCRIPTION: '"projects/" + tenant.project_id + "/subscriptions/cas-jenkins-pipelines-trigger-argo-wf-plugin-sub-" + tenant.lcaas_id'
    TRIGGER_ARGO_WF_JENKINS_INTEGRATION_STATE_SUBSCRIPTION: '"projects/" + tenant.project_id + "/subscriptions/cas-jenkins-state-trigger-argo-wf-plugin-sub-" + tenant.lcaas_id'
    CORTEXCLI_BUCKET_NAME: '"panw-cortex-cli-" + region.multi_project_postfix'
    SKIP_SSL: '"false"'
    ENABLE_PROFILING: '"false"'
    ARGO_HTTP_STEP_IMAGE: 'globals.gcr + "/golden-images/curl:8.11.1"'
    CORTEX_API_POD_URL: '"http://xdr-st-" + tenant.lcaas_id + "-api.xdr-st.svc.cluster.local:4999"'
    VERSION_CONFIG_CORTEXCLI: 'region.viso_env == "dev" ? "latest" : "0.13.0"'
    VERSION_CONFIG_APPSEC: 'region.viso_env == "dev" ? "latest" : "0.598.1"'
    VERSION_CONFIG_WAAS: '"latest"'
    VERSION_CONFIG_CWP: '"latest"'
    SECRETS_SCANNER_MAX_CHUNK_SIZE: 25
    SCA_GROUP_SIZE: 5
casConfigmapFeatureFlagsDefaults:
  namespaceOverride: globals.cas_namespace

  configmap:
    nameOverride: '"cas-configmap-feature-flags-defaults"'

  config:
    CAS_ENABLE_NEW_PR_SCAN_FLOW: '"true"'
    CAS_FF_ENABLE_NEW_WEBHOOK_SUBSCRIPTION_FLOW: 'region.is_dev ? "true" : "false"'
    NODE_OPTIONS: '"--trace-uncaught --enable-source-maps"'
    CAS_FF_ENABLE_APPCODE_ENGINE_RUN: 'region.is_dev ? "false" : "false"'
    CAS_FF_ENABLE_SECRETS_SCANNER_AS_PUBSUB: 'region.is_dev ? "false" : "false"'
    CAS_FF_ENABLE_GITHISTORY_SCANNER_AS_PUBSUB: 'region.is_dev ? "false" : "false"'
    CAS_FF_ENABLE_SCANNERS_ITERATION_GROUPS: 'region.is_dev ? "true" : "true"'
