groups:
  - name: kube.recordings
    rules:
    - record: kube:deployment:evicted
      expr: sum by(deployment, phase, namespace, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (0 * (sum by(deployment, replicaset, namespace, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (label_replace(kube_replicaset_owner{namespace!~"kube-public|kube-system|default|istio-system"}, "deployment", "$1", "owner_name", "(.*)")) + on(replicaset) group_right(deployment) sum by(pod, replicaset, namespace) (label_replace(kube_pod_owner{namespace!~"kube-public|kube-system|default|istio-system",owner_kind="ReplicaSet"}, "replicaset", "$1", "owner_name", "(.*)"))) + on(pod) group_right(deployment) (sum by(pod, phase, namespace) (kube_pod_status_phase{phase="Failed"} )))

    - record: kube:pod_memory_used:sum
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (container_memory_working_set_bytes{container!~"|POD",namespace!~"kube-public|kube-system|default|istio-system"})

    - record: kube:pod_memory_limit:sum
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_container_resource_limits{namespace!~"kube-public|kube-system|default|istio-system",resource="memory",unit="byte"})

    - record: kube:pod_memory_request:sum
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_container_resource_requests{namespace!~"kube-public|kube-system|default|istio-system",resource="memory",unit="byte"})

    - record: kube:pod_memory_used:ratio
      expr: (kube:pod_memory_used:sum/ ignoring(kubernetes_namespace, product_code) kube:pod_memory_limit:sum) * 100

    - record: kube:pod_cpu_limit:sum
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_container_resource_limits{namespace!~"kube-public|kube-system|default|istio-system",resource="cpu",unit="core"})

    - record: kube:pod_cpu_request:sum
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_container_resource_requests{namespace!~"kube-public|kube-system|default|istio-system",resource="cpu",unit="core"})

    - record: kube:pod_cpu_usage:sum
      expr: (sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (label_replace(label_replace(rate(container_cpu_usage_seconds_total{container!~"|POD",namespace!~"kube-public|kube-system|default|istio-system"}[5m]), "container", "$1", "container_name", "(.+)"), "pod", "$1", "pod_name", "(.+)")))

    - record: kube:calico_pod_cpu_usage:sum
      expr: (sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (label_replace(label_replace(rate(container_cpu_usage_seconds_total{container="calico-node",namespace="kube-system"}[5m]), "container", "$1", "container_name", "(.+)"), "pod", "$1", "pod_name", "(.+)")))

    - record: kube:pod_cpu_usage:ratio
      expr: (kube:pod_cpu_usage:sum/ ignoring(kubernetes_namespace, product_code) kube:pod_cpu_limit:sum) * 100

    - record: kube:pod_restart:sum3h
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (changes(kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"}[3h]))

    - record: kube:pod_restart:sum1h
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (changes(kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"}[1h]))

    - record: kube:pod_restart:sum30m
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (changes(kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"}[30m]))

    - record: kube:pod_restart:sum
      expr: sum by (container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"})

    - record: kube:pod_restart:sum5m
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (changes(kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"}[5m]))

    - record: kube:pod_status
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_container_status_running{namespace!~"kube-public|kube-system|default|istio-system"})

    - record: kube:storage:pvc_usage
      expr: sum by(persistentvolumeclaim, namespace, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) ((kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) * 100)

    - record: kube:storage:pvc_available_prediction
      expr: predict_linear(avg by (persistentvolumeclaim, namespace, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kubelet_volume_stats_available_bytes)[1h:], 72 * 3600)

    - record: kube:pod:unscheduled
      expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_pod_status_scheduled{namespace!~"kube-public|kube-system|default|istio-system", condition="true"}) == 0

    - record: kube:kube_job_failed:true
      expr: sum by(job_name, condition, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_job_failed{condition="true"})

    - record: kube:HPAStatus
      expr: sum by(hpa_type, namespace, hpa, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (label_replace((kube_hpa_status_condition{condition=~"true|ScalingActive",status=~"ScalingActive|true", hpa!~"dms-hpa"}), "hpa_type", "$1", "hpa", "(?:xdr-st-)?[0-9]*-(.*)"))

    - record: kube:deployment_availability
      expr: sum by (namespace, deployment, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (kube_deployment_status_replicas_available / kube_deployment_spec_replicas)

    - record: kube:kube_state_metrics:status
      expr: sum by (kubernetes_namespace, kubernetes_name, instance, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (up{kubernetes_name=~"kube-state-metrics.*",kubernetes_namespace="kube-system"})

    - record: xdr_alerts_fetcher_new_ingest:sum
      expr: sum by (record_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_alerts_fetcher_new_ingest_total)

    - record: xdr_alert_sync_tags_databases_error_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_alert_sync_tags_databases_error_total)

    - record: tenant:analytics_detection_num_of_events_got_to_matcher_total:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_events_got_to_matcher_total[5m]))
    - record: tenant:analytics_detection_num_of_events_got_by_field_total:sum
      expr: sum by (component_type, field_name, field_value, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_events_got_by_field_total[5m]))

    - record: tenant:analytics_detection_num_of_analytics_product_access_total:sum
      expr: sum by (product_type, product_name, status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_analytics_product_access_total[5m]))

    - record: tenant:analytics_detection_num_of_hits_by_detector_id_and_field_cost_total:sum
      expr: sum by (field_cost, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_hits_by_detector_id_and_field_cost_total[5m]))
    #- record: tenant:analytics_detection_num_of_hits_by_detector_id_and_field_cost_total_highcard:sum
    #  expr: sum by (detector_id, field_cost, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_hits_by_detector_id_and_field_cost_total[5m]))

    - record: tenant:analytics_detection_num_of_hits_by_matcher_and_detector_id_total:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_hits_by_matcher_and_detector_id_total[5m]))
    #- record: tenant:analytics_detection_num_of_hits_by_matcher_and_detector_id_total_highcard:sum
    #  expr: sum by (component_type, detector_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_hits_by_matcher_and_detector_id_total[5m]))

    - record: tenant:analytics_detection_num_of_detectors_analyzed_by_matcher_sum:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_detectors_analyzed_by_matcher_sum[5m]))
    - record: tenant:analytics_detection_num_of_detectors_analyzed_by_matcher_count:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_num_of_detectors_analyzed_by_matcher_count[5m]))

    - record: tenant:cc_cache_update_time_seconds:p90
      expr: histogram_quantile(0.90, sum(rate(cc_cache_update_time_seconds_bucket[5m])) by (service_name, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:cc_cache_update_time_seconds:p99
      expr: histogram_quantile(0.99, sum(rate(cc_cache_update_time_seconds_bucket[5m])) by (service_name, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:cc_cache_update_time_seconds:p50
      expr: histogram_quantile(0.5, sum(rate(cc_cache_update_time_seconds_bucket[5m])) by (service_name, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))


    - record: tenant:analytics_detection_profile_matcher_get_profile_from_db_time_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_profile_matcher_get_profile_from_db_time_sum[5m]))
    - record: tenant:analytics_detection_profile_matcher_get_profile_from_db_time_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_profile_matcher_get_profile_from_db_time_count[5m]))

    - record: tenant:analytics_detection_profile_matcher_profile_result_condition_time_sum:sum
      expr: sum (rate(analytics_detection_profile_matcher_profile_result_condition_time_sum[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:analytics_detection_profile_matcher_profile_result_condition_time_count:sum
      expr: sum (rate(analytics_detection_profile_matcher_profile_result_condition_time_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_detection_profile_matcher_get_profile_result_time_sum:sum
      expr: sum (rate(analytics_detection_profile_matcher_get_profile_result_time_sum[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:analytics_detection_profile_matcher_get_profile_result_time_count:sum
      expr: sum (rate(analytics_detection_profile_matcher_get_profile_result_time_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_detection_state_populator_ingestion_rows_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_state_populator_ingestion_rows_total[5m]))
    #- record: tenant:analytics_detection_state_populator_ingestion_rows_total_highcard:sum
    #  expr: sum by (table_name, product_code) (rate(analytics_detection_state_populator_ingestion_rows_total[5m]))

    - record: tenant:analytics_detection_state_populator_ingestion_time_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_state_populator_ingestion_time_sum[5m]))
    - record: tenant:analytics_detection_state_populator_ingestion_time_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_state_populator_ingestion_time_count[5m]))

    - record: tenant:analytics_detection_hit_publish_time_sum:sum
      expr: sum by (component, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_hit_publish_time_sum[5m]))
    - record: tenant:analytics_detection_hit_publish_time_count:sum
      expr: sum by (component, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_hit_publish_time_count[5m]))

    - record: tenant:analytics_detection_emitted_alerts_sum:sum
      expr: sum by (detector_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_emitted_alerts_sum[5m]))
    - record: tenant:analytics_detection_emitted_alerts_count:sum
      expr: sum by (detector_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_emitted_alerts_count[5m]))

    - record: tenant:analytics_detection_component_process_time_sum_by_component_type:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_component_process_time_sum[5m]))
    - record: tenant:analytics_detection_component_process_time_count:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_component_process_time_count[5m]))

    - record: tenant:analytics_detection_engine_events_processed_sum:sum
      expr: sum (rate(analytics_detection_engine_events_processed_sum[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:analytics_detection_engine_events_processed_count:sum
      expr: sum (rate(analytics_detection_engine_events_processed_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:analytics_detection_engine_consumer_nacks_total:sum
      expr: sum (rate(analytics_detection_engine_consumer_nacks_toal[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_detection_udf_execution_time_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_udf_execution_time_sum[5m]))
    #- record: tenant:analytics_detection_udf_execution_time_sum_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_udf_execution_time_sum[5m]))
    - record: tenant:analytics_detection_udf_execution_time_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_udf_execution_time_count[5m]))
    #- record: tenant:analytics_detection_udf_execution_time_count_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_udf_execution_time_count[5m]))

    - record: tenant:analytics_detection_outer_udf_execution_time_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_outer_udf_execution_time_sum[5m]))
    #- record: tenant:analytics_detection_outer_udf_execution_time_sum_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_outer_udf_execution_time_sum[5m]))
    - record: tenant:analytics_detection_outer_udf_execution_time_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_detection_outer_udf_execution_time_count[5m]))
    #- record: tenant:analytics_detection_outer_udf_execution_time_count_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_outer_udf_execution_time_count[5m]))

    - record: tenant:analytics_detection_corrupted_events_total:sum
      expr: sum (rate(analytics_detection_corrupted_events_total[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_detection_final_hit_total:sum
      expr: sum(rate(analytics_detection_final_hit_total[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_de_v2_profile_engine_api_keys_count_sum_by_endpoint:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_profile_engine_api_keys_count_sum[5m]))

    - record: tenant:analytics_unsuccessful_multi_events_enrichments_total:sum
      expr: sum (rate(analytics_unsuccessful_multi_events_enrichments_total[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_dynamic_profile_updater_time_sum:sum
      expr: sum by (profile_updater_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_dynamic_profile_updater_time_sum[5m]))
    - record: tenant:analytics_dynamic_profile_updater_time_count:sum
      expr: sum by (profile_updater_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_dynamic_profile_updater_time_count[5m]))

    - record: tenant:analytics_single_event_detection_time_sum:sum
      expr: sum (rate(analytics_single_event_detection_time_sum[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:analytics_single_event_detection_time_count:sum
      expr: sum (rate(analytics_single_event_detection_time_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_de_v2__batches_processed_wall_time_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2__batches_processed_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2__batches_processed_wall_time_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2__batches_processed_wall_time_count[5m]))

    - record: tenant:analytics_de_v2__events_processed_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2__events_processed_count[5m]))
    - record: tenant:analytics_de_v2__events_processed_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2__events_processed_sum[5m]))
    - record: tenant:analytics_de_v2__events_processed_sum:count
      expr: count by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2__events_processed_sum[5m]))
    - record: tenant:analytics_de_v2__events_processed_per_type_sum_by_event_type:sum
      expr: sum by (event_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2__events_processed_per_type_sum[5m]))

    - record: tenant:analytics_de_v2_detection_component_wall_time_sum_by_component_type:sum
      expr: sum by (component_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_detection_component_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2_detection_processing_part_wall_time_sum_by_part_type:sum
      expr: sum by (part_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_detection_processing_part_wall_time_sum[5m]))

    - record: tenant:analytics_de_v2_internal_queue_size_by_queue_name:avg
      expr: avg by (queue_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_de_v2_internal_queue_size)

    - record: tenant:analytics_de_v2_rocks_keys_count_count_by_endpoint:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_rocks_keys_count_count[5m]))
    - record: tenant:analytics_de_v2_rocks_keys_count_sum_by_endpoint:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_rocks_keys_count_sum[5m]))
    - record: tenant:analytics_de_v2_rocks_request_time_count_by_endpoint:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_rocks_request_time_count[5m]))
    - record: tenant:analytics_de_v2_rocks_request_time_sum_by_endpoint:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_rocks_request_time_sum[5m]))

    - record: tenant:analytics_de_v2_profile_engine_api_request_time_sum_by_endpoint:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_profile_engine_api_request_time_sum[5m]))

    - record: tenant:analytics_de_v2_vectorized_matcher_wall_time_sum_by_event_type:sum
      expr: sum by (event_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_vectorized_matcher_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2_vectorized_matcher_layer_wall_time_sum_by_layer_cost:sum
      expr: sum by (layer_cost, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_vectorized_matcher_layer_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2_vectorized_matcher_compile_detectors_sum_by_component:avg
      expr: avg by (component, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(analytics_de_v2_vectorized_matcher_compile_detectors_sum[5m]))

    - record: tenant:analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load:sum
      expr: sum by (status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load_total)

    - record: tenant:analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time:max
      expr: max by (lcaas_id, app, product_code) (last_over_time(analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time[12h]))

    - record: tenant:analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time:max
      expr: max by (lcaas_id, app, product_code) (last_over_time(analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time[12h]))

    - record: tenant:analytics__count_events_that_were_fetched_from_bq_total:sum
      expr: sum(analytics__count_events_that_were_fetched_from_bq_total) by(client, source_by_metaline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics__count_events_that_were_fetched_from_gcs_total:sum
      expr: sum(analytics__count_events_that_were_fetched_from_gcs_total) by(client, source_by_metaline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total:sum
      expr: sum(analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total) by(client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total:sum
      expr: sum(analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total) by(client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:asset_mgmt_diff_maker_last_exec_time_sec:max
      expr: max by (source_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_diff_maker_last_exec_time_sec)

    - record: tenant:asset_mgmt_assoc_engine_acquire_lock_count_total:sum
      expr: sum by (status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_assoc_engine_acquire_lock_count_total)

    - record: tenant:asset_mgmt_assoc_engine_association_conflicts_count_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_assoc_engine_association_conflicts_count_total)

    - record: tenant:asset_mgmt_assoc_engine_association_process_time_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_assoc_engine_association_process_time_count)

    - record: tenant:asset_mgmt_assoc_engine_association_process_time_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_assoc_engine_association_process_time_sum)

    - record: tenant:tenant_log_count_by_application:sum_over_time10m
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, log, exported_app, exported_team, exported_group, product_code) (sum_over_time(tenant_log_count_by_application[10m:1m]))

    - record: tenant:xdr_calc_last_association_replication_time:max
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_calc_last_association_replication_time)
    - record: tenant:xdr_clcs_multi_region_enabled:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_clcs_multi_region_enabled)

    - record: tenant:xdr_clcs_multi_salesforce_enabled:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_clcs_multi_salesforce_enabled)

    - record: tenant:xdr_clcs_multi_csp_connected:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_clcs_multi_csp_connected)

    - record: tenant:xdr_clcs_multi_region_connected_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_clcs_multi_region_connected_count)

    - record: tenant:xdr_scouter_to_group_calculation_count:rate5m
      expr: sum(rate(xdr_scouter_to_group_calculation_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_scouter_to_group_calculation_duration:rate5m
      expr: sum(rate(xdr_scouter_to_group_calculation_duration[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_preprocessed_data_batcher_oldest_object:max
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_preprocessed_data_batcher_oldest_object)

    - record: tenant:asset_mgmt_assoc_engine_association_process_time_sum:max
      expr: max(asset_mgmt_assoc_engine_association_process_time_sum)

    - record: tenant:asset_mgmt_ingester_assets_processed_count_total:sum
      expr: sum by (diff_type, source_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_ingester_assets_processed_count_total)

    - record: tenant:asset_mgmt_reducer_last_exec_time_sec:max
      expr: max by (status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_reducer_last_exec_time_sec)

    - record: tenant:asset_mgmt_snapshot_mgr_acquire_lock_total:sum
      expr: sum by (status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_snapshot_mgr_acquire_lock_total)

    - record: tenant:asset_mgmt_general_total_assets_count:sum
      expr: sum by (asset_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_general_total_assets_count)

    - record: tenant:asset_mgmt_general_total_assets_count:last
      expr: last_over_time(asset_mgmt_general_total_assets_count[12h])

    - record: tenant:asset_mgmt_general_assets_count_per_source:sum
      expr: sum by (source_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_general_assets_count_per_source)

    - record: tenant:asset_mgmt_general_assets_count_per_cloud_provider:sum
      expr: sum by (cloud_provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (asset_mgmt_general_assets_count_per_cloud_provider)

    - record: xdr:xdr_wildfire_submit_url_res_status_total:rate1h
      expr: sum by(status_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xdr_wildfire_submit_url_res_status_total[1h]))

    - record: instance:node_filesystem_used:ratio
      expr: sum by(instance, mountpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (100 - (node_filesystem_avail_bytes{device!~"rootfs"} / node_filesystem_size_bytes{device!~"rootfs"} * 100))

    - record: instance:node_memory_used:ratio
      expr: sum by (instance, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (100 - ((node_memory_MemAvailable_bytes * 100) / node_memory_MemTotal_bytes))

    - record: instance:node_cpu_used:ratio
      expr: 100 - avg by(instance, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (irate(node_cpu_seconds_total{mode="idle"}[5m])* 100)

    - record: instance:proxy_services:changes1h
      expr: sum by (name, instance, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (changes(node_systemd_unit_state{state="active"}[1h]))

    - record: instance:proxy_services:active
      expr: sum by (name, instance, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (node_systemd_unit_state{state="active"})

    - record: tenant:xdr_matching_service_detection_queue_depth:trend
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (tenant:xdr_matching_service_detection_queue_depth:sum - tenant:xdr_matching_service_detection_queue_depth:sum offset 2m)

    - record: tenant:xdr_matching_service_detection_queue_depth:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_matching_service_detection_queue_depth)

    - record: tenant:xdr_scheduler_wlm_working:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_scheduler_wlm_working)

    - record: tenant:xdr_mailing_queue_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_mailing_queue_count)

    - record: tenant:xdr_alerts_loader_not_running:max
      expr: max (xdr_alerts_loader_not_running) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_case_dispatch_monitor_zombie_alerts:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_case_dispatch_monitor_zombie_alerts)

    - record: tenant:xdr_missing_alerts_in_bq:sum
      expr: sum (xdr_missing_alerts_in_bq) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_notifcation_mail_queue_count:sum
      expr: sum by(status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_notifcation_mail_queue_count)

    - record: tenant:analytics_product_consecutive_stage_failures:max
      expr: max by(product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_product_consecutive_stage_failures)

    - record: tenant:analytics_product_last_successful_api_table_creation_timestamp:max
      expr: max by(product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_product_last_successful_api_table_creation_timestamp)

    - record: tenant:analytics_delta_time_from_last_successful_decider_calculation:max
      expr: max by(detector, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_delta_time_from_last_successful_decider_calculation)

    - record: tenant:analytics_content_delta_time_from_insertion:max
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_content_delta_time_from_insertion)

    - record: tenant:analytics_content_sync_succeeded:max
      expr: max (analytics_content_sync_succeeded) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_loader_provider_data_updater_last_update_time:min
      expr: min (analytics_content_loader_data_loader_provider_data_updater_last_update_time) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_loader_provider_last_update_time:max
      expr: max (analytics_content_loader_data_loader_provider_last_update_time) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_updater_failed_update_provider:sum
      expr: sum (analytics_content_loader_data_updater_failed_update_provider) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_updater_fatal_error:sum
      expr: sum (analytics_content_loader_data_updater_fatal_error) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_updater_provider_failed_update_entity:sum
      expr: sum (analytics_content_loader_data_updater_provider_failed_update_entity) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_updater_provider_last_update_time:max
      expr: max (analytics_content_loader_data_updater_provider_last_update_time) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_content_loader_data_updater_provider_total_time:max
      expr: max (analytics_content_loader_data_updater_provider_total_time) by (provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_dss_last_updated_on_last_change:max
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_dss_last_updated_on_last_change)

    - record: tenant:analytics_dss_sync_times_difference:max
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_dss_sync_times_difference)

    - record: tenant:xdr_license_fetch_failures:rate5m
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xdr_license_fetch_failures[5m]))

    - record: tenant:xdr_invalidate_user_role_failure:sum
      expr: sum (xdr_invalidate_user_role_failure) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_alert_source_delay_time:max
      expr: max by (alert_source, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_alert_source_delay_time)

    - record: tenant:xdr_case_dispatch_queue:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_case_dispatch_queue)

    - record: tenant:nginx_connections_active:sum
      expr: sum by (job, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (nginx_connections_active)

    - record: tenant:proxy_vm_nginx_request_status_code_total
      expr: sum by (job, kubernetes_namespace, tenant_type, upstream, product_type, product_tier, xdr_id, namespace, lcaas_id, nginx, status_code, product_code) (st_nginx_request_status_code_total)

    - record: tenant:proxy_vm_nginx_http_request_duration_seconds_sum
      expr: sum by (job, kubernetes_namespace, tenant_type, upstream, product_type, product_tier, xdr_id, namespace, lcaas_id, nginx, status_code, product_code) (st_nginx_http_request_duration_seconds_sum)

    - record: tenant:proxy_vm_nginx_http_request_duration_seconds_count
      expr: sum by (job, kubernetes_namespace, tenant_type, upstream, product_type, product_tier, xdr_id, namespace, lcaas_id, nginx, status_code, product_code) (st_nginx_http_request_duration_seconds_count)

    - record: tenant:docker_nginx_up
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (nginx_connections_active)

    - record: tenant:proxy_node_exporter:status
      expr: up{job="proxy-vm"}

    - record: tenant:xdr_mysql_connection_status:sum
      expr: sum by(app, kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_mysql_connection_status{app=~"xdr-st-.*-api"})

    - record: tenant:xdr_edr_license_count:max
      expr: max by(app, kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_edr_license_count)

    # "endpoint" is high cardinality field that eventually needs to be limited (see zmeidav and ibieler)
    - record: tenant:xdr_request_processing_seconds_count:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_request_processing_seconds_count)
    - record: tenant:xdr_request_processing_seconds_sum:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_request_processing_seconds_sum)
    - record: tenant:xdr_request_response_size_count:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_request_response_size_count)
    - record: tenant:xdr_request_response_size_sum:sum
      expr: sum by (endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_request_response_size_sum)

    - record: tenant:http_server_duration_milliseconds_count:sum
      expr: sum by (http_status_code, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (http_server_duration_milliseconds_count)

    - record: tenant:http_server_duration_milliseconds_sum:sum
      expr: sum by (http_status_code, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (http_server_duration_milliseconds_sum)

    - record: tenant:http_server_response_size_bytes_sum:sum
      expr: sum by (http_status_code, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (http_server_response_size_bytes_sum)

    - record: tenant:http_server_response_size_bytes_count:sum
      expr: sum by (http_status_code, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (http_server_response_size_bytes_count)

    - record: xdr:xdr_redis_errors_counter_total:rate5m
      expr: sum by (app, error_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xdr_redis_errors_counter_total[5m]))

    - record: dms_controller_integrator_events:rate:5m
      expr: sum by (method, event_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(dms_controller_integrator_events[5m]))

    - record: kube:scylla_reactor_utilization:avg
      expr: avg by (kubernetes_namespace, kubernetes_pod_name, shard, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (scylla_reactor_utilization)

    - record: kube:scylla_storage_proxy_coordinator_read_latency_bucket:sum
      expr: sum by (kubernetes_namespace, le, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(scylla_storage_proxy_coordinator_read_latency_bucket[5m]))

    - record: kube:scylla_storage_proxy_coordinator_write_latency_bucket:sum
      expr: sum by (kubernetes_namespace, le, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(scylla_storage_proxy_coordinator_write_latency_bucket[5m]))

    - record: tenant:edr_pipeline_ingestion_raw_size_bytes:sum
      expr: sum by (vendor, product, agent_os, event_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (edr_pipeline_ingestion_raw_size_bytes{product!="", vendor!=""})

    - record: tenant:GnzEdrPipeline_edr_events_count_total:sum
      expr: sum by (agent_os, event_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzEdrPipeline_edr_events_count_total)

    - record: tenant:GnzGlobal_DynamicConfig_get_config_failures_total:increase5m:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(GnzGlobal_DynamicConfig_get_config_failures_total[5m]))

    - record: tenant:GnzGlobal_pithos_client_stream_commit_latency_seconds_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzGlobal_pithos_client_stream_commit_latency_seconds_count)

    - record: tenant:GnzGlobal_pithos_client_stream_commit_latency_seconds_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzGlobal_pithos_client_stream_commit_latency_seconds_sum)

    - record: tenant:GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket:p50
      expr: histogram_quantile(0.5, sum(rate(GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket[5m])) by (le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket:p99
      expr: histogram_quantile(0.99, sum(rate(GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket[5m])) by (le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_pithos_active_streams:sum
      expr: sum by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzGlobal_pithos_active_streams)

    - record: tenant:GnzGlobal_pithos_aggregated_bytes_total:sum:rate5m
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(GnzGlobal_pithos_aggregated_bytes_total[5m]))

    - record: tenant:GnzGlobal_pithos_aggregated_objects_total:sum:rate5m
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(GnzGlobal_pithos_aggregated_objects_total[5m]))

    - record: tenant:GnzGlobal_pithos_aggregation_duration_seconds:avg
      expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(GnzGlobal_pithos_aggregation_duration_seconds)

    - record: tenant:GnzGlobal_pithos_committed_objects_total:sum:rate5m
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(GnzGlobal_pithos_committed_objects_total[5m]))

    - record: tenant:GnzGlobal_pithos_dataset_aggregators:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(GnzGlobal_pithos_dataset_aggregators)

    - record: tenant:GnzGlobal_pithos_streamed_bytes_total:sum:rate5m
      expr: sum by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(GnzGlobal_pithos_streamed_bytes_total[5m]))


    # Very high cardinality metric, for MT going to separate long retention prometheus
    - record: tenant:xql_ingestion_raw_size_bytes:sum
      expr: sum by (vendor, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xql_ingestion_raw_size_bytes{product!="", vendor!=""})

    - record: tenant:xql_ingestion_raw_size_bytes:rate5m
      expr: sum by (vendor, product, log_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xql_ingestion_raw_size_bytes{product!="", vendor!=""}[5m]))

    - record: tenant:xql_ingestion_raw_size_bytes:rate10m
      expr: sum by (vendor, product, log_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xql_ingestion_raw_size_bytes{product!="", vendor!=""}[10m]))

    - record: tenant:xql_logs_iterator_parser_panics_total:sum
      expr: sum(xql_logs_iterator_parser_panics_total) by(kubernetes_namespace, tenant_type, product_type, product_tier, namespace, lcaas_id, format, error, product_code)

    - record: tenant:cwp_dangling_resources_total:sum
      expr: sum by (cloud_provider, scan_region, dynamic_resource_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (cwp_dangling_resources_total)

    - record: tenant:partyzaurus_controller_element_status:rate:5m
      expr: sum by (status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(partyzaurus_controller_element_status{stage="pz_memsql_ingester", status="CompletionTypeAcked"}[5m]))

    - record: tenant:partyzaurus_fusion_rate_limit:sum
      expr: sum by (log_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (partyzaurus_fusion_rate_limit)

    - record: tenant:partyzaurus_controller_element_duration_seconds_sum:sum
      expr: sum(partyzaurus_controller_element_duration_seconds_sum) by (stage, pipeline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:partyzaurus_controller_element_duration_seconds_count:sum
      expr: sum(partyzaurus_controller_element_duration_seconds_count) by (stage, pipeline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:edr_controller_element_duration_seconds_sum:sum
      expr: sum(edr_controller_element_duration_seconds_sum) by (stage, pipeline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:edr_controller_element_duration_seconds_count:sum
      expr: sum(edr_controller_element_duration_seconds_count) by (stage, pipeline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:xql_controller_element_duration_seconds_sum:sum
      expr: sum(xql_controller_element_duration_seconds_sum) by (stage, pipeline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:xql_controller_element_duration_seconds_count:sum
      expr: sum(xql_controller_element_duration_seconds_count) by (stage, pipeline, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xql_controller_element_status:rate5m
      expr: sum by (pipeline, status, stage, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xql_controller_element_status[5m]))

    - record: tenant:xql_controller_element_status:rate10m
      expr: sum by (pipeline, status, stage, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xql_controller_element_status[10m]))

    - record: tenant:edr_controller_element_status:rate:5m
      expr: sum by (status, stage, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(edr_controller_element_status[5m]))

    - record: tenant:partyzaurus_scylla_elements:sum:5m
      expr: sum by (action, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(partyzaurus_scylla_elements[5m]))
    - record: tenant:edr_scylla_elements:sum:5m
      expr: sum by (action, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(edr_scylla_elements[5m]))
    - record: tenant:dms_scylla_elements:sum:5m
      expr: sum by (action, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(dms_scylla_elements[5m]))
    - record: tenant:storybuilder_scylla_elements:sum:5m
      expr: sum by (action, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(storybuilder_scylla_elements[5m]))

    - record: tenant:storybuilder_quantum_lag_sec:sum
      expr: sum by (kubernetes_pod_name, quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_quantum_lag_sec)

    - record: tenant:storybuilder_oversized_key_count_by_pod:sum:5m
      expr: sum by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(storybuilder_oversized_key_count[5m]))

    - record: tenant:storybuilder_events_count_by_pod:sum:5m
      expr: sum by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(storybuilder_events_count{xdr_panw_app="storybuilder"}[5m]))
    - record: tenant:storybuilder_events_count:avg:5m
      expr: avg by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(storybuilder_events_count{xdr_panw_app="storybuilder"}[5m]))

    - record: tenant:storybuilder_stories_total_by_operation:sum:10m
      expr: sum by (operation, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(storybuilder_stories_total[10m]))

    - record: tenant:storybuilder_consumer_buffer_size_by_pod:sum
      expr: sum by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_consumer_buffer_size{xdr_panw_app="storybuilder"})

    - record: tenant:storybuilder_story_write_time_sec_by_pod:avg
      expr: avg by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_story_write_time_sec > 0)
    - record: tenant:storybuilder_story_lookup_time_sec_by_pod:avg
      expr: avg by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_story_lookup_time_sec > 0)

    - record: tenant:storybuilder_scylla_latency_histogram_bucket:sum
      expr: sum by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(storybuilder_scylla_latency_histogram_bucket[5m]))
    - record: tenant:storybuilder_scylla_latency_histogram_sum:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_scylla_latency_histogram_sum)
    - record: tenant:storybuilder_scylla_latency_histogram_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_scylla_latency_histogram_count)

    - record: tenant:storybuilder_scylla_latency_summary_sum_by_pod:sum
      expr: sum by (kubernetes_pod_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_scylla_latency_summary_sum{xdr_panw_app="storybuilder"})
    - record: tenant:storybuilder_scylla_latency_summary:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_scylla_latency_summary)
    - record: tenant:storybuilder_scylla_latency_summary_count:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_scylla_latency_summary_count)

    - record: tenant:storybuilder_filtered_events_total:rate:10m
      expr: (rate(storybuilder_filtered_events_total{xdr_panw_app="storybuilder"}[10m]))

    # not clear if the rest of these storybuilder are being used yet but here just in case
    - record: tenant:storybuilder_events_in_story:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_events_in_story)

    - record: tenant:storybuilder_in_flight_story_timer_wait_duration_seconds:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(storybuilder_in_flight_story_timer_wait_duration_seconds_bucket[5m])) by (le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:storybuilder_in_flight_story_timer_wait_duration_seconds:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(storybuilder_in_flight_story_timer_wait_duration_seconds_bucket[5m])) by (le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:storybuilder_in_flight_story_timer_wait_duration_seconds:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(storybuilder_in_flight_story_timer_wait_duration_seconds_bucket[5m])) by (le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:storybuilder_published_stories:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_published_stories)
    - record: tenant:storybuilder_message_processing_time_sec:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_message_processing_time_sec)
    - record: tenant:storybuilder_stories_in_batch:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (storybuilder_stories_in_batch)

    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_log_entry_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_log_entry_severity_total:sum
      expr: sum by (severity, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count:rate5m
      expr: sum by (container_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count[5m]))

    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_byte_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (stackdriver_k_8_s_container_logging_googleapis_com_byte_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_byte_severity_total:sum
      expr: sum by (severity, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (stackdriver_k_8_s_container_logging_googleapis_com_byte_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_byte_count:rate5m
      expr: sum by (container_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(stackdriver_k_8_s_container_logging_googleapis_com_byte_count[5m]))
    - record: tenant:agent_group_calculations_count:rate5m
      expr: sum(rate(agent_to_group_calculation_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:agent_group_calculations_duration:rate5m
      expr: sum(rate(agent_to_group_calculation_duration[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)


  - name: tenant
    rules:
    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_sum:rate5m
      expr: sum(rate(contextual_search_graph_neo4j_query_execution_time_millis_sum[5m])) by (depth,response_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_created:sum
      expr: sum by (depth, response_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (contextual_search_graph_neo4j_query_execution_time_millis_created)

    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_count:sum
      expr: sum by (depth, response_type,kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (contextual_search_graph_neo4j_query_execution_time_millis_count)

    - record: tenant:email_relay_attachment_verdict_total:increase10m
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, is_known_hash, verdict, product_code) (increase(email_relay_attachment_verdict_total[10m]))

    - record: tenant:xdr_email_relay_attachment_verdict_total:sum_over_time10m
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, is_known_hash, verdict, product_code) (sum_over_time(email_relay_attachment_verdict_total[10m]))

    - record: tenant:email_relay_pubsub_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, subscription, action, product_code) (email_relay_pubsub_total)

    - record: tenant:email_relay_scheduler_task_executions_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, subscription, task, product_code) (email_relay_scheduler_task_executions_total)

    - record: tenant:xdr_email_to_issue_latency_bucket:p90
      expr: histogram_quantile(0.9, sum(rate(xdr_email_to_issue_latency_bucket[5m])) by (le, email_issue_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xdr_email_to_issue_latency_bucket:p100
      expr: histogram_quantile(1, sum(rate(xdr_email_to_issue_latency_bucket[5m])) by (le, email_issue_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xdr_email_to_issue_latency_count:sum
      expr: sum(xdr_email_to_issue_latency_count) by (email_issue_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_email_to_issue_latency_sum:sum
      expr: sum(xdr_email_to_issue_latency_sum) by (email_issue_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_unknown_email_issue_type_total:sum
      expr: sum(xdr_unknown_email_issue_type_total) by (unknown_email_issue_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(contextual_search_graph_neo4j_query_execution_time_millis_bucket[1h])) by (le, depth, response_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(contextual_search_graph_neo4j_query_execution_time_millis_bucket[1h])) by (le, depth, response_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(contextual_search_graph_neo4j_query_execution_time_millis_bucket[1h])) by (le, depth, response_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:contextual_search_graph_neo4j_query_execution_time_millis_bucket:p75
      expr: >-
        histogram_quantile(0.75, sum(rate(contextual_search_graph_neo4j_query_execution_time_millis_bucket[1h])) by (le, depth, response_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))


    - record: tenant:data_ingestion_health_ingestion_alerts_total:sum
      expr: sum by (product, vendor, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (data_ingestion_health_ingestion_alerts_total)

    - record: tenant:xdm_invalid_mapping_views:max
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdm_invalid_mapping_views)

    - record: tenant:xdm_calculate_view_hash_total:sum1h
      expr:  sum by (app, comparison_result, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xdm_calculate_view_hash_total{kubernetes_pod_name=~".*-api-.*",kubernetes_pod_name!~".*-agent-api-.*"}[10m]))

    - record: tenant:GnzGlobal_quantum_last_insert_time_sec:max
      expr: max by (quantum, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzGlobal_Quantum_last_insert_time_seconds)

    - record: tenant:GnzGlobal_json_stream_bigquery_written_bytes:sum
      expr: sum(rate(GnzGlobal_json_stream_bigquery_written_bytes[5m])) by (table, transport, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:GnzGlobal_storage_write_api_status_total:sum
      expr: sum(rate(GnzGlobal_storage_write_api_status_total[5m])) by (dataset, status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:GnzGlobal_Throttler_throttled_total:sum
      expr: sum(rate(GnzGlobal_Throttler_throttled_total[5m])) by (app, bucket, tenant_type, product_type, product_tier, xdr_id, kubernetes_namespace, lcaas_id, throttled, product_code)

    - record: tenant:GnzGlobal_Throttler_bucket_tokens:avg
      expr: avg(GnzGlobal_Throttler_bucket_tokens) by (app, bucket, tenant_type, product_type, product_tier, xdr_id, kubernetes_namespace, lcaas_id, product_code)

    - record: tenant:GnzGlobal_storage_write_api_append_rows_duration:avg
      expr: |
        sum(rate(GnzGlobal_storage_write_api_append_rows_duration_ms_sum[5m])) by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
        /
        sum(rate(GnzGlobal_storage_write_api_append_rows_duration_ms_count[5m]))  by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:GnzGlobal_DAO_operation_latency_seconds_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(GnzGlobal_DAO_operation_latency_seconds_bucket[5m])) by (le, app, op, dao, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_DAO_operation_latency_seconds_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(GnzGlobal_DAO_operation_latency_seconds_bucket[5m])) by (le, app, op, dao, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_DAO_operation_latency_seconds_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(GnzGlobal_DAO_operation_latency_seconds_bucket[5m])) by (le, app, op, dao, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket[5m])) by (le, app, origin, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket[5m])) by (le, app, origin, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket[5m])) by (le, app, origin, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket[5m])) by (le, app, origin, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket[5m])) by (le, app, origin, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket[5m])) by (le, app, origin, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:http_server_request_duration_seconds_count:sum
      expr: sum (http_server_request_duration_seconds_count) by (service_name, http_response_status_code, http_status_code, http_route, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:http_server_request_duration_seconds_sum:sum
      expr: sum (http_server_request_duration_seconds_sum) by (service_name, http_response_status_code, http_status_code, http_route, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:http_server_request_duration_seconds_bucket:p99
      expr: histogram_quantile(0.99, sum(rate(http_server_request_duration_seconds_bucket[5m])) by (le, service_name, http_response_status_code, http_status_code, http_route, http_target, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:http_server_request_duration_seconds_bucket:p90
      expr: histogram_quantile(0.90, sum(rate(http_server_request_duration_seconds_bucket[5m])) by (le, service_name, http_response_status_code, http_status_code, http_route, http_target, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:http_server_request_duration_seconds_bucket:p50
      expr: histogram_quantile(0.5, sum(rate(http_server_request_duration_seconds_bucket[5m])) by (le, service_name, http_response_status_code, http_status_code, http_route, http_target, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:http_server_duration_milliseconds_count:sum
      expr: sum (http_server_duration_milliseconds_count) by (http_response_status_code, http_status_code, http_route, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:http_server_duration_milliseconds_sum:sum
      expr: sum (http_server_duration_milliseconds_sum) by (http_response_status_code, http_status_code, http_route, http_target, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:http_server_duration_milliseconds_bucket:p99
      expr: histogram_quantile(0.99, sum(rate(http_server_duration_milliseconds_bucket[5m])) by (le, service_name, http_response_status_code, http_status_code, http_route, http_target, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:http_server_duration_milliseconds_bucket:p90
      expr: histogram_quantile(0.90, sum(rate(http_server_duration_milliseconds_bucket[5m])) by (le, service_name, http_response_status_code, http_status_code, http_route, http_target, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:http_server_duration_milliseconds_bucket:p50
      expr: histogram_quantile(0.5, sum(rate(http_server_duration_milliseconds_bucket[5m])) by (le, service_name, http_response_status_code,  http_status_code, http_route, http_target, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xql_ingestion_logs_count:sum
      expr: sum(rate(xql_ingestion_logs_count[5m])) by (vendor, product, dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xql_engine_output_counter:sum
      expr: sum(rate(xql_engine_output_counter[5m])) by (vendor, product, dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xql_engine_input_counter:sum
      expr: sum(rate(xql_engine_input_counter[5m])) by (vendor, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xql_engine_dataset_columns_count:sum
      expr: max(xql_engine_dataset_columns_count) by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xql_engine_send_detect_counter:rate
      expr: sum(rate(xql_engine_send_detect_counter[5m])) by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xql_inflight_api_errors:rate5m
      expr: sum(rate(xql_inflight_api_errors_total[5m])) by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    
    - record: tenant:xql_inflight_cancel_api_response_seconds:p90
      expr: histogram_quantile(0.90, sum(rate(xql_inflight_cancel_api_response_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xql_inflight_cancel_api_response_seconds:p99
      expr: histogram_quantile(0.99, sum(rate(xql_inflight_cancel_api_response_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:vectorized_matcher_current_content_version_loading_time_in_seconds:max
      expr: max(vectorized_matcher_current_content_version_loading_time_in_seconds) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:vectorized_matcher_current_content_version_exporting_time_in_seconds:max
      expr: max(vectorized_matcher_current_content_version_exporting_time_in_seconds) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:verdict_manager_batches_completed:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_batches_completed)

    - record: tenant:verdict_manager_findings_closed:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_findings_closed)

    - record: tenant:verdict_manager_findings_created:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_findings_created)

    - record: tenant:verdict_manager_findings_publish_failed:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_findings_publish_failed)

    - record: tenant:verdict_manager_findings_publish_success:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_findings_publish_success)

    - record: tenant:verdict_manager_issues_batch_publish_failed:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_issues_batch_publish_failed)

    - record: tenant:verdict_manager_issues_batch_publish_success:sum
      expr: sum by (scanner_name, scanner_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_issues_batch_publish_success)

    - record: tenant:verdict_manager_issues_closed:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_issues_closed)

    - record: tenant:verdict_manager_issues_created:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_issues_created)

    - record: tenant:verdict_manager_issues_updated:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_issues_updated)

    - record: tenant:verdict_manager_verdicts_fetched:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_verdicts_fetched)

    - record: tenant:verdict_manager_reconcile_failed:sum
      expr: sum by (scanner_name, scanner_type,  kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (verdict_manager_reconcile_failed)

    - record: tenant:vxp_http_server_requests_count:rate5m
      expr: sum(rate(vxp_http_server_requests_count[5m])) by (method, status, uri, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:vxp_http_server_requests_sum:rate5m
      expr: sum(rate(vxp_http_server_requests_sum[5m])) by (method, status, uri, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:pz_schema_manager_pz_api_call_counter:rate
      expr: sum(rate(pz_schema_manager_pz_api_call_counter[5m])) by (route, res_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:pz_schema_manager_pz_api_call_duration_seconds:avg
      expr: (sum(rate(pz_schema_manager_pz_api_call_duration_seconds_sum[5m])) by (route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)) / sum(rate(pz_schema_manager_pz_api_call_duration_seconds_count[5m])) by (route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:xdr_pz_schema_is_dataset_max_keys_reached:max
      expr: max by (bq_table_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(xdr_pz_schema_is_dataset_max_keys_reached)

    - record: tenant:xdr_pz_schema_bq_external_raw_table_column_count:max_over_time1h
      expr: max_over_time(xdr_pz_schema_bq_external_raw_table_column_count[1h])

    - record: tenant:dms_controller_dropped_stories:sum
      expr: sum(dms_controller_dropped_stories_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_flood_stuck_alerts_total:sum
      expr: sum(analytics_flood_stuck_alerts_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_flood_tasks_total:sum
      expr: sum (analytics_flood_tasks_total) by (task_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_global_flood_total:sum
      expr: sum by (detector, variation, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (analytics_global_flood_total)

    - record: tenant:attack_path_rules:sum
      expr: sum by (service_name, telemetry_sdk_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (attack_path_rules)

    - record: tenant:attack_path_start_total:sum
      expr: sum by (service_name, telemetry_sdk_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (attack_path_start_total)

    - record: tenant:attack_path_success_total:sum
      expr: sum by (service_name, telemetry_sdk_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (attack_path_success_total)

    - record: tenant:attack_path_verdicts:sum
      expr: sum by (service_name, telemetry_sdk_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (attack_path_verdicts)

    - record: tenant:ciem_gauge_metrics_publisher_total_issues:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(ciem_gauge_metrics_publisher_total_issues)

    - record: tenant:custom_rules_counter_total:sum
      expr: sum by (app, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (custom_rules_counter_total)

    - record: tenant:attack_path_failure:sum
      expr: sum by (service_name, telemetry_sdk_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (attack_path_failure)

    - record: tenant:xsoar_msg_bus_subscriber_pull:rate5m
      expr: sum by (priority, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate (xsoar_msg_bus_subscriber_pull[5m])*300)

    - record: tenant:xsoar_msg_bus_subscriber_ack:rate5m
      expr: sum by (priority, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate (xsoar_msg_bus_subscriber_ack[5m])*300)

    - record: tenant:xsoar_msg_bus_subscriber_nack:rate5m
      expr: sum by (priority, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate (xsoar_msg_bus_subscriber_nack[5m])*300)

    - record: tenant:xsoar_msg_bus_dlq_subscriber_pull:rate5m
      expr: sum by (priority, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate (xsoar_msg_bus_dlq_subscriber_pull[5m])*300)

    - record: tenant:apisec_asset_manager_delete_asset_messages_total:increase1h:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(increase(apisec_asset_manager_delete_asset_messages_total[1h]))

    - record: tenant:xsoar_msg_bus_lock_latency_bucket:rate5m
      expr: sum by (le, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xsoar_msg_bus_lock_latency_bucket{}[5m])*300)

    - record: tenant:xsoar_msg_bus_insert_latency_bucket:rate5m
      expr: sum by (le, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xsoar_msg_bus_insert_latency_bucket[5m])*300)

    - record: tenant:xsoar_msg_bus_subscriber_pull_latency_bucket:rate5m
      expr: sum by (le, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xsoar_msg_bus_subscriber_pull_latency_bucket[5m])*300)

    - record: tenant:xsoar_msg_bus_subscriber_ack_latency_bucket:rate5m
      expr: sum by (le, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xsoar_msg_bus_subscriber_ack_latency_bucket[5m])*300)

    - record: tenant:xsoar_msg_bus_subscriber_nack_latency_bucket:rate5m
      expr: sum by (le, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xsoar_msg_bus_subscriber_nack_latency_bucket[5m])*300)

    - record: tenant:xsoar_msg_bus_fetch_metrics_latency_bucket:rate5m
      expr: sum by (le, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(xsoar_msg_bus_fetch_metrics_latency_bucket[5m])*300)

    - record: tenant:xsoar_msg_bus_publisher:rate5m
      expr: sum by (priority, topic, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate (xsoar_msg_bus_publisher[5m])*300)

    - record: tenant:xsoar_auto_extract_enrich_all_indicators_bucket:p50
      expr: >-
        histogram_quantile(0.5, avg(rate(xsoar_auto_extract_enrich_all_indicators_bucket[5m])) by (app, le, amount_of_indicators, incident_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_enrich_all_indicators_bucket:p90
      expr: >-
        histogram_quantile(0.9, avg(rate(xsoar_auto_extract_enrich_all_indicators_bucket[5m])) by (app, le, amount_of_indicators, incident_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_enrich_all_indicators_bucket:p99
      expr: >-
        histogram_quantile(0.99, avg(rate(xsoar_auto_extract_enrich_all_indicators_bucket[5m])) by (app, le, amount_of_indicators, incident_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_enrich_indicator_command_bucket:p50
      expr: >-
        histogram_quantile(0.5, avg(rate(xsoar_auto_extract_enrich_indicator_command_bucket[5m])) by (app, le, instance_name, reputation_command, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_enrich_indicator_command_bucket:p90
      expr: >-
        histogram_quantile(0.9, avg(rate(xsoar_auto_extract_enrich_indicator_command_bucket[5m])) by (app, le, instance_name, reputation_command, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_enrich_indicator_command_bucket:p99
      expr: >-
        histogram_quantile(0.99, avg(rate(xsoar_auto_extract_enrich_indicator_command_bucket[5m])) by (app, le, instance_name, reputation_command, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_entry_processing_bucket:p50
      expr: >-
        histogram_quantile(0.5, avg(rate(xsoar_auto_extract_entry_processing_bucket[5m])) by (app, le, amount_of_indicators, entry_length, incident_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_entry_processing_bucket:p90
      expr: >-
        histogram_quantile(0.9, avg(rate(xsoar_auto_extract_entry_processing_bucket[5m])) by (app, le, amount_of_indicators, entry_length, incident_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_entry_processing_bucket:p99
      expr: >-
        histogram_quantile(0.99, avg(rate(xsoar_auto_extract_entry_processing_bucket[5m])) by (app, le, amount_of_indicators, entry_length, incident_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_format_indicator_bucket:p50
      expr: >-
        histogram_quantile(0.5, avg(rate(xsoar_auto_extract_format_indicator_bucket[5m])) by (app, le, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_format_indicator_bucket:p90
      expr: >-
        histogram_quantile(0.9, avg(rate(xsoar_auto_extract_format_indicator_bucket[5m])) by (app, le, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_format_indicator_bucket:p99
      expr: >-
        histogram_quantile(0.99, avg(rate(xsoar_auto_extract_format_indicator_bucket[5m])) by (app, le, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))


    - record: tenant:vxp_pubsub_processing_latency_bucket:p95
      expr: >-
        histogram_quantile(0.95, sum(rate(vxp_pubsub_processing_latency_bucket[5m])) by (le, subscription, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))


    - record: tenant:xsoar_auto_extract_map_indicator_bucket:p50
      expr: >-
        histogram_quantile(0.5, avg(rate(xsoar_auto_extract_map_indicator_bucket[5m])) by (app, le, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_map_indicator_bucket:p90
      expr: >-
        histogram_quantile(0.9, avg(rate(xsoar_auto_extract_map_indicator_bucket[5m])) by (app, le, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_map_indicator_bucket:p99
      expr: >-
        histogram_quantile(0.99, avg(rate(xsoar_auto_extract_map_indicator_bucket[5m])) by (app, le, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_find_indicator_bucket:p50
      expr: >-
        histogram_quantile(0.5, avg(rate(xsoar_auto_extract_find_indicator_bucket[5m])) by (app, le, entry_length, incident_type, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_find_indicator_bucket:p90
      expr: >-
        histogram_quantile(0.9, avg(rate(xsoar_auto_extract_find_indicator_bucket[5m])) by (app, le, entry_length, incident_type, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:xsoar_auto_extract_find_indicator_bucket:p99
      expr: >-
        histogram_quantile(0.99, avg(rate(xsoar_auto_extract_find_indicator_bucket[5m])) by (app, le, entry_length, incident_type, reputation_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:cortex_cdl_to_clcs_migration_failed_total:sum
      expr: sum(cortex_cdl_to_clcs_migration_failed_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:cortex_cdl_to_clcs_migration_succeeded_total:sum
      expr: sum(cortex_cdl_to_clcs_migration_succeeded_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:egress_aggregator_aggregator_compression_rate:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_aggregator_compression_rate)

    - record: tenant:egress_aggregator_committed_object_size:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_committed_object_size)

    - record: tenant:egress_aggregator_compression_job_status:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_compression_job_status)

    - record: tenant:egress_aggregator_delete_objects_count:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_delete_objects_count)

    - record: tenant:egress_aggregator_processed_bytes_total:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_processed_bytes_total)

    - record: tenant:egress_aggregator_processed_objects_count:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_processed_objects_count)

    - record: tenant:egress_aggregator_raw_object_size:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_raw_object_size)

    - record: tenant:egress_aggregator_spawned_aggregators_count:sum
      expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (egress_aggregator_spawned_aggregators_count)

    - record: tenant:xdr_egress_oldest_raw_object:min
      expr: min by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_egress_oldest_raw_object)

    - record: tenant:xdr_search_index_creation_errors:sum
      expr: sum(xdr_search_index_creation_errors_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:batch_scanner_assets_scanned_milliseconds:p99
      expr: histogram_quantile(0.99, avg(rate(batch_scanner_assets_scanned_milliseconds_bucket[5m])) by (service_name, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:batch_scanner_assets:sum
      expr: sum(batch_scanner_assets) by (service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:batch_scanner_rules_processed_total:sum
      expr: sum(batch_scanner_rules_processed_total) by (service_name, job, status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:batch_scanner_verdict_generated:sum
      expr: sum(batch_scanner_verdict_generated) by (service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:wf_vs_set_upload_request_failed:sum
      expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (wf_vs_set_upload_request_failed)

    - record: tenant:wf_vs_set_upload_request_success:sum
      expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (wf_vs_set_upload_request_success)

    - record: tenant:xdr_ios_large_digest_report_total:sum
      expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_ios_large_digest_report_total)

    - record: tenant:uai_aggregated_assets_cdc_delay_seconds:max:5m
      expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (max_over_time(uai_aggregated_assets_cdc_delay_seconds[5m]))

    - record: tenant:active_cloud_connectors:sum
      expr: sum by (cloud_provider, provisioning_method, scan_mode, scope, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (active_cloud_connectors)

    - record: tenant:active_cloud_outposts:sum
      expr: sum by (cloud_provider, provisioning_method, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (active_cloud_outposts)

    - record: tenant:cloud_connectors_templates_created_total:sum
      expr: sum by (cloud_provider, derived_from, scan_mode, scope, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (cloud_connectors_templates_created_total)

    - record: tenant:cloud_onboarding_errors_total:sum
      expr: sum by (cloud_provider, entity, operation, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (cloud_onboarding_errors_total)

    - record: tenant:cloud_outposts_templates_created_total:sum
      expr: sum by (cloud_provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (cloud_outposts_templates_created_total)

    - record: tenant:number_of_cloud_accounts:sum
      expr: sum by (account_type, cloud_provider, environment, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (number_of_cloud_accounts)

    - record: tenant:dp_asset_associations_pipeline_assets_batch_size:p50
      expr: histogram_quantile(0.5, sum(rate(dp_asset_associations_pipeline_assets_batch_size_bucket[5m])) by (le, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_associations_pipeline_assets_batch_size:p90
      expr: histogram_quantile(0.9, sum(rate(dp_asset_associations_pipeline_assets_batch_size_bucket[5m])) by (le, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_associations_pipeline_assets_batch_size:p99
      expr: histogram_quantile(0.99, sum(rate(dp_asset_associations_pipeline_assets_batch_size_bucket[5m])) by (le, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_associations_pipeline_association_big_query_result_size:p50
      expr: histogram_quantile(0.5, sum(rate(dp_asset_associations_pipeline_association_big_query_result_size_bucket[5m])) by (le, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_associations_pipeline_association_big_query_result_size:p90
      expr: histogram_quantile(0.9, sum(rate(dp_asset_associations_pipeline_association_big_query_result_size_bucket[5m])) by (le, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_associations_pipeline_association_big_query_result_size:p99
      expr: histogram_quantile(0.99, sum(rate(dp_asset_associations_pipeline_association_big_query_result_size_bucket[5m])) by (le, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_pipeline_assets_total:rate5m
      expr: sum(rate(dp_asset_pipeline_assets_total[5m])) by ( provider, product, sourceId, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_assets_errors:rate5m
      expr: sum(rate(dp_asset_pipeline_assets_errors[5m])) by ( provider, product, sourceId, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_metablob_errors:rate5m
      expr: sum(rate(dp_asset_pipeline_metablob_errors[5m])) by ( product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_skip_get_deleted_assets:rate5m
      expr: sum(rate(dp_asset_pipeline_skip_get_deleted_assets[5m])) by ( product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_performance_count:rate5m
      expr: sum(rate(dp_asset_pipeline_performance_count[5m])) by ( operation_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_performance_sum:rate5m
      expr: sum(rate(dp_asset_pipeline_performance_sum[5m])) by ( operation_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_performance_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(dp_asset_pipeline_performance_bucket[5m])) by (operation_type, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_pipeline_performance_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(dp_asset_pipeline_performance_bucket[5m])) by (operation_type, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_pipeline_performance_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(dp_asset_pipeline_performance_bucket[5m])) by (operation_type, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_pipeline_rows_in_metablobs_bucket:p10
      expr: >-
        histogram_quantile(0.1, sum(rate(dp_asset_pipeline_rows_in_metablobs_bucket[5m])) by (le, app,  product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_pipeline_rows_in_metablobs_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(dp_asset_pipeline_rows_in_metablobs_bucket[5m])) by (le, app, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:dp_asset_pipeline_rows_in_metablobs_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(dp_asset_pipeline_rows_in_metablobs_bucket[5m])) by (le, app, product, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:uai_api_requests_count_total:rate
      expr: sum(rate(uai_api_requests_count_total[5m])) by ( endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:uai_api_errors_count_total:rate
      expr: sum(rate(uai_api_errors_count_total[5m])) by ( endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:uai_api_requests_count_total:increase:1h
      expr: sum(increase(uai_api_requests_count_total[1h])) by ( endpoint, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dbre_backup_succeeded:max
      expr: max(dbre_backup_succeeded) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, db_name, db_type, product_code)

    - record: tenant:uai_api_errors_count_total:increase:1h
      expr: sum(increase(uai_api_errors_count_total[1h])) by ( endpoints, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:cwp_malwaredetection_wildfire_latency_seconds_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(cwp_malwaredetection_wildfire_latency_seconds_bucket[5m])) by (le, method, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:cwp_malwaredetection_wildfire_latency_seconds_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(cwp_malwaredetection_wildfire_latency_seconds_bucket[5m])) by (le, method, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:cwp_malwaredetection_wildfire_latency_seconds_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(cwp_malwaredetection_wildfire_latency_seconds_bucket[5m])) by (le, method, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:finding_operation_total:rate5m
      expr: sum(rate(finding_operation_total[5m])) by (operation_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:dp_asset_pipeline_operation_total:rate5m
      expr: sum(rate(dp_asset_pipeline_operation_total[5m])) by (operation_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:finding_metablob_performance_count:rate5m
      expr: sum(rate(finding_metablob_performance_count[5m])) by (operation_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:finding_metablob_performance_sum:rate5m
      expr: sum(rate(finding_metablob_performance_sum[5m])) by (operation_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:finding_metablob_performance_bucket:p50
      expr: >-
        histogram_quantile(0.5, sum(rate(finding_metablob_performance_bucket[5m])) by (operation_type, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:finding_metablob_performance_bucket:p90
      expr: >-
        histogram_quantile(0.9, sum(rate(finding_metablob_performance_bucket[5m])) by (operation_type, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:finding_metablob_performance_bucket:p99
      expr: >-
        histogram_quantile(0.99, sum(rate(finding_metablob_performance_bucket[5m])) by (operation_type, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: tenant:qr_code_image_reader_call_duration_bucket:p99
      expr: histogram_quantile(0.99, sum(rate(qr_code_image_reader_call_duration_bucket[5m])) by (le,http_status_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id,lcaas_id, product_code))

    - record: tenant:qr_code_image_reader_call_duration_bucket:p90
      expr: histogram_quantile(0.90, sum(rate(qr_code_image_reader_call_duration_bucket[5m])) by (le,http_status_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id,lcaas_id, product_code))

    - record: tenant:qr_code_image_reader_call_duration_bucket:p50
      expr: histogram_quantile(0.50, sum(rate(qr_code_image_reader_call_duration_bucket[5m])) by (le,http_status_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id,lcaas_id, product_code))

    - record: tenant:cronus_last_processed_index:sum
      expr: sum by (quantum, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cronus_last_processed_index{stream="network"}) - on(quantum, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzStoryBuilder_PartitionConsumer_last_processed_index) >= 0  * (cronus_last_processed_index{stream="network"}) - on(tenant_id,quantum , kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (GnzStoryBuilder_PartitionConsumer_last_processed_index)

    - record: tenant:cronus_last_processed_index_stream:sum
      expr: (sum by (quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cronus_last_processed_index{stream="network"} - on(quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) GnzStoryBuilder_PartitionConsumer_last_processed_index) >= 0  * (cronus_last_processed_index{stream="network"} - on(quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)GnzStoryBuilder_PartitionConsumer_last_processed_index)) / on(quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(GnzStoryBuilder_PartitionConsumer_last_processed_index[5m]))

    - record: tenant:analytics_llm_requests_bucket:p50 
      expr: histogram_quantile(0.50, sum(rate(analytics_llm_requests_bucket[5m])) by (le, status, content_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id,lcaas_id, product_code))
    
    - record: tenant:analytics_llm_requests_bucket:p90 
      expr: histogram_quantile(0.90, sum(rate(analytics_llm_requests_bucket[5m])) by (le, status, content_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id,lcaas_id, product_code))
    
    - record: tenant:analytics_llm_requests_bucket:p99 
      expr: histogram_quantile(0.99, sum(rate(analytics_llm_requests_bucket[5m])) by (le, status, content_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id,lcaas_id, product_code))
    

    - record: tenant:apisec_risk_engine_dspm_classification_call_duration_in_seconds_bucket:p99
      expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_dspm_classification_call_duration_in_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

  - name: temporal.analytics
    rules:
    - record: tenant:temporal:persistence_requests:increase:10m
      expr: sum by (namespace, operation, type, product_code) (increase(persistence_requests{app_kubernetes_io_part_of="temporal"}[10m]))

    - record: tenant:temporal:workflow_success:increase:10m
      expr: sum by (namespace,operation,taskqueue) (increase(workflow_success{app_kubernetes_io_part_of="temporal"}[10m]))

    - record: tenant:temporal:workflow_failed:increase:10m
      expr: sum by (namespace,operation,taskqueue) (increase(workflow_failed{app_kubernetes_io_part_of="temporal"}[10m]))

    - record: tenant:temporal:workflow_timeout:increase:10m
      expr: sum by (namespace,operation,taskqueue) (increase(workflow_timeout{app_kubernetes_io_part_of="temporal"}[10m]))

  - name: polaris.recordings
    rules:
    - record: tenant:polaris:persistence_requests:sum:2m
      expr: sum by (collection) (rate(mongo_polaris_inserts_total[2m]))

  - name: job_evaluator.recordings
    rules:
    - record: tenant:job_evaluator:failed_requests_total:sum:increase:10m
      expr: sum(increase(failed_requests_total[10m])) by (app, status, path, method)

  - name: classification_mgmt.recordings
    rules:
    - record: tenant:classification_mgmt:classification_mgmt_dashboard_error_total:sum:increase:10m
      expr: sum(increase(classification_mgmt_dashboard_error_total[10m])) by (flow, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:classification_mgmt:classification_mgmt_content_delivery_error_total:sum:increase:10m
      expr: sum(increase(classification_mgmt_content_delivery_error_total[10m])) by (flow, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:classification_mgmt:classification_mgmt_bigquery_write_error_total:sum:increase:10m
      expr: sum(increase(classification_mgmt_bigquery_write_error_total[10m])) by (flow, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:classification_mgmt_pubsub_publish_error_total:sum:increase:10m
      expr: sum(increase(classification_mgmt_pubsub_publish_error_total[10m])) by (flow, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
    - record: tenant:classification_mgmt_dashboard_business_total:sum:increase:10m
      expr: sum(increase(classification_mgmt_dashboard_business_total[10m])) by (flow, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: inline_scanner.recordings
    rules:
    - record: tenant:inline_scanner_asset_change_event_received_total:hourly
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(inline_scanner_asset_change_event_received_total[1h]))

    - record: tenant:inline_scanner_asset_change_event_failed_total:hourly
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(inline_scanner_asset_change_event_failed_total[1h]))

    - record: tenant:inline_scanner_asset_change_event_failed_total:sum
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (inline_scanner_asset_change_event_failed_total)

    - record: tenant:inline_scanner_assets_scanned_total:hourly
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(inline_scanner_assets_scanned_total[1h]))

    - record: tenant:inline_scanner_asset_change_event_failed_total:ratio
      expr: |
        sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(inline_scanner_asset_change_event_failed_total[1h]))
        /
        sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(inline_scanner_assets_scanned_total[1h]))
        * 100

    - record: tenant:inline_scanner_findings_published_success_total:hourly
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(inline_scanner_findings_published_success_total[1h]))

    - record: tenant:inline_scanner_findings_published_failed_total:hourly
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(inline_scanner_findings_published_failed_total[1h]))

    - record: tenant:inline_scanner_findings_published_failed_total:sum
      expr: sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (inline_scanner_findings_published_failed_total)

    - record: tenant:inline_scanner_findings_published_failed_total:ratio
      expr: |
        sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(inline_scanner_findings_published_failed_total[1h]))
        / sum by (service_name, scanner_name, tenant_id, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(inline_scanner_findings_published_success_total[1h])) * 100

  - name: tenant.analytics
    rules:
    - record: tenant:analytics_de_v2_vectorized_matcher_detector_count_per_layer:sum
      expr: sum(analytics_de_v2_vectorized_matcher_detector_count_per_layer_total) by (detector_id, layer_cost, source_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    - record: tenant:analytics_de_v2_vectorized_matcher_udf_process_time:avg
      expr: |
        sum(rate(analytics_de_v2_vectorized_matcher_udf_process_time_sum[5m])) by (udf_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
        /
        sum(rate(analytics_de_v2_vectorized_matcher_udf_process_time_count[5m])) by (udf_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: story_source.recordings
    interval: 1h
    rules:
    - record: tenant:partyzaurus_fusion_mapping_source:sum:1h
      expr:  sum by (source, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(partyzaurus_fusion_mapping_source[10m]))

    - record: tenant:dms_controller_story_mapping_source:sum:1h
      expr:  sum by (source, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(dms_controller_story_mapping_source[10m]))

  - name: storybuilder.recordings
    interval: 5m
    rules:
      - record: tenant:GnzGlobal_storybuilder_quantum_lag_seconds:max
        expr: max(xdr_story_builder_max_delay) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
        labels:
          kubernetes_service: storybuilders

      - record: tenant:GnzGlobal_storybuilder_quantum_lag_seconds_v2:max
        expr: time() - min(max(GnzStoryBuilder_PartitionConsumer_consumed_insert_ts/1000) by (quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # used for alert StoryBuilderLag30m
      - record: tenant:storybuilder_quantum_lag:max
        expr: time() - min(GnzStoryBuilder_PartitionConsumer_quantum_offset_time_seconds) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: support_case
    interval: 1h
    rules:
      - record: tenant:support_case_creation_sucess:sum:1h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(support_case_number_of_support_case_successfully_created_total[1h]))*3600

      - record: tenant:support_case_creation_failed:sum:1h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(support_case_number_of_support_case_failed_to_create_total[1h]))*3600

      - record: tenant:support_case_auto_generate_tsf_request_timeout_total:sum
        expr: sum by (client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (support_case_auto_generate_tsf_request_timeout_total)

      - record: tenant:support_case_failed_auto_generate_tsf_request_total:sum
        expr: sum by (client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (support_case_failed_auto_generate_tsf_request_total)

      - record: tenant:support_case_failed_upload_files_total:sum
        expr: sum by (client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (support_case_failed_upload_files_total)

      - record: tenant:support_case_success_auto_generate_tsf_request_total:sum
        expr: sum by (client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (support_case_success_auto_generate_tsf_request_total)

      - record: tenant:support_case_success_upload_files_total:sum
        expr: sum by (client, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (support_case_success_upload_files_total)


  - name: xql_ingestion
    interval: 1h
    rules:
      - record: xdr:xql_ingestion_raw_size_bytes:rate1h
        expr: sum by (vendor, product, log_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (sum_over_time(rate(xql_ingestion_raw_size_bytes[5m])[3599s:1h]))

      - record: xdr:xql_ingestion_logs_count:rate1h
        expr: sum by (vendor, product, log_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (sum_over_time(rate(xql_ingestion_logs_count[5m])[3599s:1h]))

      - record: sb:hourly_incoming_eps
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (sum_over_time(rate(partyzaurus_scylla_elements{action="write", type="StitchableNetworkRow"}[5m])[3599s:1h])) + sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (sum_over_time(rate(edr_scylla_elements{action="write", type="StitchableNetworkRow"}[5m])[3599s:1h]))
        labels:
          send_to_bq: "true"

  - name: coldstorage.recordings
    interval: 10m
    rules:
      - record: tenant:xdr_retention_service_sync_retention_stats_failures_total:sum:10m
        expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(xdr_retention_service_sync_retention_stats_failures_total{kubernetes_pod_name=~".*-api-.*",kubernetes_pod_name!~".*-agent-api-.*"})
      - record: tenant:xdr_retention_service_enforce_retention_failures_total:sum:10m
        expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(xdr_retention_service_enforce_retention_failures_total{kubernetes_pod_name=~".*-api-.*",kubernetes_pod_name!~".*-agent-api-.*"})
      - record: tenant:cold_storage_datasets_aggregator_aggregator_compression_rate:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_aggregator_compression_rate)
      - record: tenant:cold_storage_datasets_aggregator_aggregator_process_duration:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_aggregator_process_duration)
      - record: tenant:cold_storage_datasets_aggregator_committed_object_size:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_committed_object_size)
      - record: tenant:cold_storage_datasets_aggregator_compression_job_status:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_compression_job_status)
      - record: tenant:cold_storage_datasets_aggregator_dataset_errors_count:sum
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_dataset_errors_count)
      - record: tenant:cold_storage_datasets_aggregator_delete_objects_count:rate5m
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(rate(cold_storage_datasets_aggregator_delete_objects_count[5m]))
      - record: tenant:cold_storage_datasets_aggregator_processed_bytes_total:rate5m
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(rate(cold_storage_datasets_aggregator_processed_bytes_total[5m]))
      - record: tenant:cold_storage_datasets_aggregator_processed_objects_count:rate5m
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(rate(cold_storage_datasets_aggregator_processed_objects_count[5m]))
      - record: tenant:cold_storage_datasets_aggregator_raw_object_size:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_raw_object_size)
      - record: tenant:cold_storage_datasets_aggregator_spawned_aggregators_count:sum
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_storage_datasets_aggregator_spawned_aggregators_count)

  - name: archivestorage.recordings
    interval: 5m
    rules:
      - record: tenant:archive_storage_aggregator_aggregator_compression_rate:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (archive_storage_aggregator_aggregator_compression_rate)
      - record: tenant:archive_storage_aggregator_aggregator_process_duration:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (archive_storage_aggregator_aggregator_process_duration)
      - record: tenant:archive_storage_aggregator_committed_object_size:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (archive_storage_aggregator_committed_object_size)
      - record: tenant:archive_storage_aggregator_compression_job_status:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (archive_storage_aggregator_compression_job_status)
      - record: tenant:archive_storage_aggregator_delete_objects_count:rate5m
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(archive_storage_aggregator_delete_objects_count[5m]))
      - record: tenant:archive_storage_aggregator_process_object_duration_micro_seconds_bucket:p90
        expr: histogram_quantile(0.9, sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(archive_storage_aggregator_process_object_duration_micro_seconds_bucket[5m])))
      - record: tenant:archive_storage_aggregator_parse_error_count:sum
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(archive_storage_aggregator_parse_error_count)
      - record: tenant:archive_storage_aggregator_processed_bytes_total:rate5m
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(archive_storage_aggregator_processed_bytes_total[5m]))
      - record: tenant:archive_storage_aggregator_processed_objects_count:rate5m
        expr: sum by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (rate(archive_storage_aggregator_processed_objects_count[5m]))
      - record: tenant:archive_storage_aggregator_raw_object_size:avg
        expr: avg by (dataset, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (archive_storage_aggregator_raw_object_size)

  - name: fusion.recordings
    interval: 5m
    rules:
      - record: tenant:partyzaurus_o365_mail_row_parse_error_count:sum:5m
        expr: sum (partyzaurus_o365_mail_row_parse_error_count) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: quota.recordings
    interval: 1h
    rules:
      - record: tenant:ingestion_quota_exceeded:sum:1h
        expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(ingestion_quota_exceeded{kubernetes_pod_name=~".*-api-.*",kubernetes_pod_name!~".*-agent-api-.*"})

  - name: cts.recordings
    interval: 5m
    rules:
      - record: tenant:xdr_cts_active_sessions:sum
        expr: sum by (app, cloud_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_cts_active_sessions)

      - record: tenant:xdr_cts_active_tokens:sum
        expr: sum by (app, cloud_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xdr_cts_active_tokens)

  - name: coldtables.recordings
    interval: 1h
    rules:
      - record: tenant:cold_tables_sync_job_failure_total:sum:1h
        expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_tables_sync_job_failure_total{kubernetes_pod_name=~".*-api-.*",kubernetes_pod_name!~".*-agent-api-.*"})
      - record: tenant:cold_tables_sync_dataset_failure_total:sum:1h
        expr: sum by (dataset_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cold_tables_sync_dataset_failure_total{kubernetes_pod_name=~".*-api-.*",kubernetes_pod_name!~".*-agent-api-.*"})

  - name: cronus
    interval: 5m
    rules:
      - record: tenant:cronus_requests_total:sum
        expr: sum(rate(cronus_requests_total[5m])) by (type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_log_cache_get_ops_hit:ratio
        expr: >-
          sum(rate(cronus_log_cache_get_ops{result="hit"}[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
          /
          sum(rate(cronus_log_cache_get_ops[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_log_cache_get_ops_miss:ratio
        expr: >-
          sum(rate(cronus_log_cache_get_ops{result="miss"}[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
          /
          sum(rate(cronus_log_cache_get_ops[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_hotkeys_count_duration_seconds_bucket:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_hotkeys_count_duration_seconds_bucket[5m])) by (le, kubernetes_pod_name, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_hotkeys_count_duration_seconds_bucket:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_hotkeys_count_duration_seconds_bucket[5m])) by (le, kubernetes_pod_name, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_hotkeys_count_duration_seconds_bucket:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_hotkeys_count_duration_seconds_bucket[5m])) by (le, kubernetes_pod_name, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_hotkeys_count_delay_duration_seconds_bucket:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_hotkeys_count_delay_duration_seconds_bucket[5m])) by (le, kubernetes_pod_name, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_hotkeys_count_delay_duration_seconds_bucket:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_hotkeys_count_delay_duration_seconds_bucket[5m])) by (le, kubernetes_pod_name, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_hotkeys_count_delay_duration_seconds_bucket:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_hotkeys_count_delay_duration_seconds_bucket[5m])) by (le, kubernetes_pod_name, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_request_duration_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_request_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_request_duration_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_request_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_request_duration_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_request_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_client_client_roundtrip_latency_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_client_client_roundtrip_latency_sec_bucket[5m])) by (le, type, direction, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_client_client_roundtrip_latency_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_client_client_roundtrip_latency_sec_bucket[5m])) by (le, type, direction, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_client_client_roundtrip_latency_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_client_client_roundtrip_latency_sec_bucket[5m])) by (le, type, direction, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_client_connection_wait_duration_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_client_connection_wait_duration_seconds_bucket[5m])) by (le,app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_client_connection_wait_duration_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_client_connection_wait_duration_seconds_bucket[5m])) by (le,app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_client_connection_wait_duration_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_client_connection_wait_duration_seconds_bucket[5m])) by (le,app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_request_process_duration_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_request_process_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_request_process_duration_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_request_process_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_request_process_duration_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_request_process_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_requests_queue_stream_feed_wait_duration_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(cronus_requests_queue_stream_feed_wait_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_requests_queue_stream_feed_wait_duration_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(cronus_requests_queue_stream_feed_wait_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_requests_queue_stream_feed_wait_duration_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(cronus_requests_queue_stream_feed_wait_duration_seconds_bucket[5m])) by (le, type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_active_connections_total:sum
        expr: sum(cronus_active_connections_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_db_repair_dropped_entries_total:sum
        expr: sum by (tenant_id, app, xdr_panw_app, xdr_panw_cronus_cluster, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (cronus_db_repair_dropped_entries_total)

      - record: tenant:cronus_throttled_write_requests_total:sum
        expr: sum(cronus_throttled_write_requests_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_rebalance_download_bytes_total:rate5m
        expr: sum(rate(cronus_rebalance_download_bytes_total[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_rebalance_upload_bytes_total:rate5m
        expr: sum(rate(cronus_rebalance_upload_bytes_total[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_rebalance_read_bytes_total:rate5m
        expr: sum(rate(cronus_rebalance_read_bytes_total[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: spur
    interval: 5m
    # note - all metrics here are in pairs, one for PZ (xql-engine) and one for DMS
    rules:
      # how many IPs were successfully queried from Bigtable, labeled with whether they were nil or not
      - record: tenant:spur_pz_queried:rate5m:sum
        expr: >-
          sum(rate(spur_pz_queried_count[5m])) by (type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_queried:rate5m:sum
        expr: >-
          sum(rate(spur_dms_queried_count[5m])) by (type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # how many IPs are being queried, labeled with they were skipped, cached or hit from the DB
      - record: tenant:spur_pz_request_count:rate5m:sum
        expr: sum(rate(spur_pz_request_count[5m])) by (hit_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_request_count:rate5m:sum
        expr: sum(rate(spur_dms_request_count[5m])) by (hit_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # how many Bigtable requests were made (entire batches rather than individual IPs)
      - record: tenant:spur_pz_fetch_count:rate5m:sum
        expr: sum(rate(spur_pz_fetch_duration_seconds_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_fetch_count:rate5m:sum
        expr: sum(rate(spur_dms_fetch_duration_seconds_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # Number of pods where Spur is temporarily inactive due to errors.
      - record: tenant:spur_pz_temporarily_inactive:sum
        expr: sum(spur_pz_inactive_until_unix_milli/1000 > bool time())

      - record: tenant:spur_dms_temporarily_inactive:sum
        expr: sum(spur_dms_inactive_until_unix_milli/1000 > bool time())

      # How many BT requests (batches of IPs) failed (or were rate limited)
      - record: tenant:spur_pz_fetch_errors_count:rate5m:sum
        expr: sum(rate(spur_pz_fetch_errors_count[5m])) by (flow, reason, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_fetch_errors_count:rate5m:sum
        expr: sum(rate(spur_dms_fetch_errors_count[5m])) by (flow, reason, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      #  How many individual IPs failed (or were rate limited)
      - record: tenant:spur_pz_skipped_ips_count:rate5m:sum
        expr: sum(rate(spur_pz_skipped_ips_count[5m])) by (flow, reason, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_skipped_ips_count:rate5m:sum
        expr: sum(rate(spur_dms_skipped_ips_count[5m])) by (flow, reason, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # BT requests for metadata poll that failed
      - record: tenant:spur_pz_poll_failure_count:rate5m:sum
        expr: sum(rate(spur_pz_poll_failure_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_poll_failure_count:rate5m:sum
        expr: sum(rate(spur_dms_poll_failure_count[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

    
      - record: tenant:standard_events_queue_size:max
        expr: max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (standard_events_queue_size)

      - record: tenant:standard_events_event_processed_success_counter_total:sum
        expr: sum(standard_events_event_processed_success_counter_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_failed_validation_counter_total:sum
        expr: sum(standard_events_event_failed_validation_counter_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_failed_sys_error_counter_total:sum
        expr: sum(standard_events_event_failed_sys_error_counter_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_dropped_counter_total:sum
        expr: sum(standard_events_event_dropped_counter_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_processed_success_counter_created:min
        expr: min(standard_events_event_processed_success_counter_created) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_failed_validation_counter_created:min
        expr: min(standard_events_event_failed_validation_counter_created) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_failed_sys_error_counter_created:min
        expr: min(standard_events_event_failed_sys_error_counter_created) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:standard_events_event_dropped_counter_created:min
        expr: min(standard_events_event_dropped_counter_created) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)  


      # Latency metrics, to tell how BT is performing from tenant's perspective
      - record: tenant:spur_pz_fetch_duration_seconds:rate5m:mean
        expr: >-
          sum(rate(spur_pz_fetch_duration_seconds_sum[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
          /
          sum(rate(spur_pz_fetch_duration_seconds_count[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_pz_fetch_duration_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(spur_pz_fetch_duration_seconds_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_pz_fetch_duration_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(spur_pz_fetch_duration_seconds_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_pz_fetch_duration_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(spur_pz_fetch_duration_seconds_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_dms_fetch_duration_seconds:rate5m:mean
        expr: >-
          sum(rate(spur_dms_fetch_duration_seconds_sum[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
          /
          sum(rate(spur_dms_fetch_duration_seconds_count[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
      - record: tenant:spur_dms_fetch_duration_seconds:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(spur_dms_fetch_duration_seconds_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_dms_fetch_duration_seconds:p90
        expr: >-
          histogram_quantile(0.9, sum(rate(spur_dms_fetch_duration_seconds_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_dms_fetch_duration_seconds:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(spur_dms_fetch_duration_seconds_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      # Spur batch size metrics - these can help tune the batch size and understand latency of BT requests
      - record: tenant:spur_pz_actual_batch_size:rate5m:mean
        expr: >-
          sum(rate(spur_pz_actual_batch_size_sum[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
          /
          sum(rate(spur_pz_actual_batch_size_count[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_pz_actual_batch_size:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(spur_pz_actual_batch_size_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_pz_actual_batch_size:p90
        expr: >-
          histogram_quantile(0.90, sum(rate(spur_pz_actual_batch_size_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_pz_actual_batch_size:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(spur_pz_actual_batch_size_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_dms_actual_batch_size:rate5m:mean
        expr: >-
          sum(rate(spur_dms_actual_batch_size_sum[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
          /
          sum(rate(spur_dms_actual_batch_size_count[5m])) by (flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_actual_batch_size:p99
        expr: >-
          histogram_quantile(0.99, sum(rate(spur_dms_actual_batch_size_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_dms_actual_batch_size:p90
        expr: >-
          histogram_quantile(0.90, sum(rate(spur_dms_actual_batch_size_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:spur_dms_actual_batch_size:p50
        expr: >-
          histogram_quantile(0.5, sum(rate(spur_dms_actual_batch_size_bucket[5m])) by (le, flow, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      # Cache size metrics - these help gauge the memory impact of caching
      - record: tenant:spur_pz_cache_size:avg
        expr: avg(spur_pz_cache_size) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_dms_cache_size:avg
        expr: avg(spur_dms_cache_size) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # Determine the min/max Spur DB versions (spur generation time) that were observed in the tenant
      - record: tenant:spur_dms_spur_generation_time_unix_milli:max
        expr: max(spur_dms_spur_generation_time_unix_milli) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
      - record: tenant:spur_dms_spur_generation_time_unix_milli:min
        expr: min(spur_dms_spur_generation_time_unix_milli) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:spur_pz_spur_generation_time_unix_milli:max
        expr: max(spur_pz_spur_generation_time_unix_milli) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
      - record: tenant:spur_pz_spur_generation_time_unix_milli:min
        expr: min(spur_pz_spur_generation_time_unix_milli) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      # Failure parsing IP (skips enrichment)
      - record: tenant:spur_dms_ip_parse_error:rate5m:sum
        expr: sum(rate(spur_dms_ip_parse_error[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)
      - record: tenant:spur_pz_ip_parse_error:rate5m:sum
        expr: sum(rate(spur_pz_ip_parse_error[5m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: xpanse
    interval: 5m
    rules:
      - record: tenant:xpanse_rcs_result_processor_results_processed_count_total:sum
        expr: sum by (status, error_type, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xpanse_rcs_result_processor_results_processed_count_total)

      - record: tenant:xpanse_manual_scan_requested_count_total:sum
        expr: sum(XPANSE_MANUAL_SCAN_REQUESTED_COUNT_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_manual_scan_request_failed_counter_total:sum
        expr: sum(XPANSE_MANUAL_SCAN_REQUEST_FAILED_COUNTER_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xdr_xpanse_incident_context_injection_failure_total:sum
        expr: sum(xdr_xpanse_incident_context_injection_failure_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts:sum
        expr: sum(xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts:sum
        expr: sum(xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts:sum
        expr: sum by(connector_type, status, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts)

      - record: tenant:xpanse_incident_context_injection_succeed_total:sum
        expr: sum(xpanse_incident_context_injection_succeed_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_explorer_ratings_cache_error_total:sum
        expr: sum(xpanse_explorer_ratings_cache_error_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_tags_to_mysql_sync_error_total:sum
        expr: sum(xpanse_tags_to_mysql_sync_error_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_websites_do_not_exist_error:sum
        expr: sum(XPANSE_WEBSITES_DO_NOT_EXIST_ERROR_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_website_failed_loading_item_error:sum
        expr: sum(XPANSE_WEBSITE_FAILED_LOADING_ITEM_ERROR_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_website_details_unexpected_result:sum
        expr: sum(XPANSE_WEBSITE_DETAILS_UNEXPECTED_RESULT_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:xpanse_asset_tag_rules_error_total:sum
        expr: sum(xpanse_asset_tag_rules_error_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:effective_ip_range_monitoring_overlapping_rules:sum
        expr: sum by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (effective_ip_range_monitoring_overlapping_rules)

      - record: tenant:asm_alerts_mitre_backfill_sync_error_total:increase:1h
        expr: sum(increase(asm_alerts_mitre_backfill_sync_error_total[1h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_mitre_mappings_sync_error_total:increase:1h
        expr: sum(increase(asm_mitre_mappings_sync_error_total[1h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_incidents_mitre_backfill_sync_error_total:increase:1h
        expr: sum(increase(asm_incidents_mitre_backfill_sync_error_total[1h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_alerts_mitre_backfill_sync_error_total:increase:3h
        expr: sum(increase(asm_alerts_mitre_backfill_sync_error_total[3h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_mitre_mappings_sync_error_total:increase:3h
        expr: sum(increase(asm_mitre_mappings_sync_error_total[3h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_incidents_mitre_backfill_sync_error_total:increase:3h
        expr: sum(increase(asm_incidents_mitre_backfill_sync_error_total[3h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_alerts_mitre_backfill_sync_error_total:sum
        expr: sum(asm_alerts_mitre_backfill_sync_error_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_mitre_mappings_sync_error_total:sum
        expr: sum(asm_mitre_mappings_sync_error_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_incidents_mitre_backfill_sync_error_total:sum
        expr: sum(asm_incidents_mitre_backfill_sync_error_total) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:asm_export_assets_gauge:sum
        expr: sum(asm_export_assets_gauge) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: netscan.recordings
    rules:
      - record: tenant:netscan_scans_configured:sum
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (netscan_scans_configured)

      - record: tenant:netscan_scans_scheduled:sum
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (netscan_scans_scheduled)

      - record: tenant:netscan_avg_target_size:avg
        expr:  avg by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (netscan_avg_target_size)

      - record: tenant:netscan_kms_error_total:sum
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (netscan_kms_error_total)

      - record: tenant:netscan_processor_cns_host_prior_scan_age:p50
        expr: histogram_quantile(0.5, sum(rate(netscan_processor_cns_host_prior_scan_age_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_cns_host_prior_scan_age:p90
        expr: histogram_quantile(0.9, sum(rate(netscan_processor_cns_host_prior_scan_age_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_cns_host_prior_scan_age:p99
        expr: histogram_quantile(0.99, sum(rate(netscan_processor_cns_host_prior_scan_age_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_dao_access_duration_seconds:p50
        expr: histogram_quantile(0.5, sum(rate(netscan_processor_dao_access_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_dao_access_duration_seconds:p90
        expr: histogram_quantile(0.9, sum(rate(netscan_processor_dao_access_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_dao_access_duration_seconds:p99
        expr: histogram_quantile(0.99, sum(rate(netscan_processor_dao_access_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_netscan_results_batch_process_time:p50
        expr: histogram_quantile(0.5, sum(rate(netscan_processor_netscan_results_batch_process_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_netscan_results_batch_process_time:p90
        expr: histogram_quantile(0.9, sum(rate(netscan_processor_netscan_results_batch_process_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_netscan_results_batch_process_time:p99
        expr: histogram_quantile(0.99, sum(rate(netscan_processor_netscan_results_batch_process_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_scan_results_batch_size:p50
        expr: histogram_quantile(0.5, sum(rate(netscan_processor_scan_result_batch_size_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_scan_results_batch_size:p90
        expr: histogram_quantile(0.9, sum(rate(netscan_processor_scan_result_batch_size_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_scan_results_batch_size:p99
        expr: histogram_quantile(0.99, sum(rate(netscan_processor_scan_result_batch_size_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_scan_result_message_count:p50
        expr: histogram_quantile(0.5, sum(rate(netscan_processor_scan_result_message_count_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_scan_result_message_count:p90
        expr: histogram_quantile(0.9, sum(rate(netscan_processor_scan_result_message_count_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_scan_result_message_count:p99
        expr: histogram_quantile(0.99, sum(rate(netscan_processor_scan_result_message_count_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:netscan_processor_uai_pubsub_output_error:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, topic, error, product_code)(netscan_processor_uai_pubsub_output_error)

      - record: tenant:netscan_processor_scan_result_with:sum
        expr: sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, attribute_type, product_code)(netscan_processor_scan_result_with)

      - record: tenant:netscan_processor_scan_result_match:sum
        expr: sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, match_type, product_code)(netscan_processor_scan_result_match)

      - record: tenant:netscan_processor_scan_result_incompatible:sum
        expr: sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, incompatible_type, product_code)(netscan_processor_scan_result_incompatible)

      - record: tenant:netscan_processor_scan_task_processed:sum
        expr: sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, scan_status)(netscan_processor_scan_task_processed)

  - name: application_hub.recordings
    interval: 1h
    rules:
      - record: tenant:application_hub_permanent_errors_total:sum
        expr:  sum by (app_type, operation, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(application_hub_permanent_errors_total)

      - record: tenant:application_hub_temporary_errors_total:sum
        expr:  sum by (app_type, operation, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(application_hub_temporary_errors_total)

      - record: tenant:application_hub_public_api_requests_total:sum
        expr:  sum by (api_path, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (application_hub_public_api_requests_total)

      - record: tenant:app_hub_prometheus_ingester_edr_errors_total:sum
        expr:  sum by (reason, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (app_hub_prometheus_ingester_edr_errors_total)

      - record: tenant:app_hub_prometheus_ingester_xql_errors_total
        expr:  sum by (reason, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (app_hub_prometheus_ingester_xql_errors_total)

  - name: vulnerability_assesment.recordings
    interval: 1h
    rules:
      - record: tenant:xdr_va_recalculation_not_running:max_over_time:12h
        expr:  max_over_time(xdr_va_recalculation_not_running[12h]) and on (lcaas_id, product_code) (xdr_addons_license_status{addon="host_insights"} == 1)

  - name: xsoar_rc.recordings
    rules:
      - record: tenant:xsoar_rc_counter_incidents_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_incidents_error[10m]))

      - record: tenant:xsoar_rc_counter_incidents_linkedIncidents_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_incidents_linkedIncidents_error[10m]))

      - record: tenant:xsoar_rc_counter_incidents_linkedIncidents_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_incidents_linkedIncidents_warning[10m]))

      - record: tenant:xsoar_rc_counter_incidents_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_incidents_warning[10m]))

      - record: tenant:xsoar_rc_counter_indicators_investigations_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_indicators_investigations_error[10m]))

      - record: tenant:xsoar_rc_counter_indicators_investigations_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_indicators_investigations_warning[10m]))

      - record: tenant:xsoar_rc_counter_indicators_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_indicators_warning[10m]))

      - record: tenant:xsoar_rc_counter_investigations_entries_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_entries_error[10m]))

      - record: tenant:xsoar_rc_counter_investigations_entries_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_entries_warning[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsDefaultEntries_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsDefaultEntries_error[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsDefaultEntries_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsDefaultEntries_warning[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsDefaultEntriesJob_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsDefaultEntriesJob_error[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsDefaultEntriesJob_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsDefaultEntriesJob_warning[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsNonDefaultEntries_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsNonDefaultEntries_error[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsNonDefaultEntries_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsNonDefaultEntries_warning[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsNonDefaultEntriesJob_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsNonDefaultEntriesJob_error[10m]))

      - record: tenant:xsoar_rc_counter_investigations_indicatorsNonDefaultEntriesJob_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_investigations_indicatorsNonDefaultEntriesJob_warning[10m]))

      - record: tenant:xsoar_rc_counter_TestObject_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_TestObject_error[10m]))

      - record: tenant:xsoar_rc_counter_TestObject_linkedIncidents_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_TestObject_linkedIncidents_error[10m]))

      - record: tenant:xsoar_rc_counter_TestObject_linkedIncidents_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_TestObject_linkedIncidents_warning[10m]))

      - record: tenant:xsoar_rc_counter_TestObject_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_TestObject_warning[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_incidents_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_incidents_error[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_incidents_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_incidents_warning[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_indicators_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_indicators_error[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_indicators_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_indicators_warning[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_moduleInstance_incidents_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_moduleInstance_incidents_error[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_moduleInstance_incidents_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_moduleInstance_incidents_warning[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_pyramidAPI_getAlerts_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_pyramidAPI_getAlerts_error[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_pyramidAPI_getAlerts_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_pyramidAPI_getAlerts_warning[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_TestObject_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_TestObject_error[10m]))

      - record: tenant:xsoar_rc_counter_with_time_range_TestObject_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_counter_with_time_range_TestObject_warning[10m]))

      - record: tenant:xsoar_rc_size_attachments_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_attachments_error[10m]))

      - record: tenant:xsoar_rc_size_attachments_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_attachments_warning[10m]))

      - record: tenant:xsoar_rc_size_incidents_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_incidents_error[10m]))

      - record: tenant:xsoar_rc_size_incidents_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_incidents_warning[10m]))

      - record: tenant:xsoar_rc_size_indicators_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_indicators_error[10m]))

      - record: tenant:xsoar_rc_size_indicators_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_indicators_warning[10m]))

      - record: tenant:xsoar_rc_size_TestObject_error:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_TestObject_error[10m]))

      - record: tenant:xsoar_rc_size_TestObject_warning:increase:10m
        expr:  sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(xsoar_rc_size_TestObject_warning[10m]))
  - name: log_metrics
    rules:
      - record: tenant:logback_events_total:increase:5m
        expr:  sum(increase(logback_events_total[5m])) by (app,level,team,group)

      - record: tenant:cronus_client_rate_limited_requests_total:sum
        expr: sum(rate(cronus_client_rate_limited_requests_total[5m])) by (app, xdr_panw_app, quantum, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_client_requests_total:sum
        expr: sum(rate(cronus_client_requests_total[5m])) by (stream, type, app, xdr_panw_app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cronus_rows_index_latency_seconds:p50
        expr: histogram_quantile(0.5, sum(rate(cronus_rows_index_latency_seconds_bucket[5m])) by (le, call, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_rows_index_latency_seconds:p90
        expr: histogram_quantile(0.9, sum(rate(cronus_rows_index_latency_seconds_bucket[5m])) by (le, call, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_rows_index_latency_seconds:p99
        expr: histogram_quantile(0.99, sum(rate(cronus_rows_index_latency_seconds_bucket[5m])) by (le, call, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_tree_index_compaction_duration_seconds:p50
        expr: histogram_quantile(0.5, sum(rate(cronus_tree_index_compaction_duration_seconds_bucket[5m])) by (le, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_tree_index_compaction_duration_seconds:p90
        expr: histogram_quantile(0.9, sum(rate(cronus_tree_index_compaction_duration_seconds_bucket[5m])) by (le, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:cronus_tree_index_compaction_duration_seconds:p99
        expr: histogram_quantile(0.99, sum(rate(cronus_tree_index_compaction_duration_seconds_bucket[5m])) by (le, stream, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

  - name: vsg.recordings
    rules:
    - record: vsg:reconcile_time:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:reconcile_time:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:oom_scaler_reconcile_time:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_oom_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:oom_scaler_reconcile_time:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_oom_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:oom_scaler_resolve_source_metric:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_oom_scaler_resolve_source_metric_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:oom_scaler_resolve_source_metric:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_oom_scaler_resolve_source_metric_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:vertical_scaler_reconcile_time:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_vertical_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:vertical_scaler_reconcile_time:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_vertical_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:vertical_scaler_resolve_source_metric:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_vertical_scaler_resolve_source_metric_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:vertical_scaler_resolve_source_metric:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_vertical_scaler_resolve_source_metric_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:zero_scaler_reconcile_time:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_zero_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:zero_scaler_reconcile_time:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_zero_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:pvc_scaler_reconcile_time:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_pvc_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:pvc_scaler_reconcile_time:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_pvc_scaler_reconcile_time_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:zero_scaler_resolve_source_metric:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_zero_scaler_resolve_source_metric_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:zero_scaler_resolve_source_metric:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_zero_scaler_resolve_source_metric_bucket[30m])) by (ns, target, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

    - record: vsg:pvc_scaler_resolve_source_metric:p50
      expr: histogram_quantile(0.5, sum(rate(vsg_pvc_scaler_resolve_source_metric_bucket[30m])) by (ns, target, container, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))
    - record: vsg:pvc_scaler_resolve_source_metric:p95
      expr: histogram_quantile(0.95, sum(rate(vsg_pvc_scaler_resolve_source_metric_bucket[30m])) by (ns, target, container, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

  - name: tenant.db.recordings
    interval: 1h
    rules:
      - record: tenant:mysql:kube_pod_container_restarts:sum12h
        expr: sum by(container, namespace, pod, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (changes(kube_pod_container_status_restarts_total{container=~".*-(mysql)$"}[12h]))

  - name: dspm_rules
    rules:
      - record: tenant:http_requests_total:no_health_and_metrics:increase:5m
        expr: label_replace(sum(increase(http_requests_total{handler!~"(/metrics.*|/health.*)"}[5m])) by(app,kubernetes_namespace, product_code),"namespace","$1","kubernetes_namespace","(.*)")
      - record: tenant:http_server_requests_seconds_count:no_actuator:increase:5m
        expr: label_replace(sum(increase(http_server_requests_seconds_count[5m])) by(app,kubernetes_namespace, product_code) - sum(increase(http_server_requests_seconds_count{uri=~"/actuator/.*"}[5m])) by(app,kubernetes_namespace, product_code),"namespace","$1","kubernetes_namespace","(.*)")
      - record: tenant:dspm:temporal:hpa:90:per_app
        expr: label_replace(label_replace(((((max by (app, kubernetes_namespace, product_code) (avg by (app, worker_type, task_queue, kubernetes_namespace, product_code) (temporal_worker_task_slots_used / (temporal_worker_task_slots_available + temporal_worker_task_slots_used)))) < 0.99) * on (app, kubernetes_namespace, product_code) group_left () sum by (app, kubernetes_namespace, product_code) (up) / 0.9)) or (count by (kubernetes_namespace, app, product_code) (clamp_max(sum by (kubernetes_pod_name, app, kubernetes_namespace, product_code) (temporal_worker_task_slots_available{app!=""}) + 1, 1)) * 2), "namespace", "$1", "kubernetes_namespace", "(.*)"), "deployment", "$1", "app", "(.*)")
      - record: tenant:dspm:temporal:activity_ratio
        expr: label_replace(label_replace(label_replace(sum by (namespace, task_queue, kubernetes_namespace, app, product_code) (temporal_worker_task_slots_used{worker_type="ActivityWorker"}) / (sum by (namespace, task_queue, kubernetes_namespace, app, product_code) (temporal_worker_task_slots_used{worker_type="ActivityWorker"}) + sum by (namespace, task_queue, kubernetes_namespace, app, product_code) (temporal_worker_task_slots_available{worker_type="ActivityWorker"})), "temporal_namespace", "$1", "namespace", "(.*)"), "namespace", "$1", "kubernetes_namespace", "(.*)"), "deployment", "$1", "app", "(.*)")
      - record: tenant:dspm_dt_bigquery_job_affected_rows_total:sum
        expr: sum by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_affected_rows_total)
      - record: tenant:dspm_dt_bigquery_job_duration_seconds_count:sum
        expr: sum by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_duration_seconds_count)
      - record: tenant:dspm_dt_bigquery_job_duration_seconds_max:max
        expr: max by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_duration_seconds_max)
      - record: tenant:dspm_dt_bigquery_job_duration_seconds_sum:sum
        expr: sum by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_duration_seconds_sum)
      - record: tenant:dspm_dt_bigquery_job_processed_bytes_total:sum
        expr: sum by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_processed_bytes_total)
      - record: tenant:dspm_dt_bigquery_job_processed_partitions_total:sum
        expr: sum by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_processed_partitions_total)
      - record: tenant:dspm_dt_bigquery_job_slot_millis_total:sum
        expr: sum by(query_group_id, query_group_name, query_group_owner, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (dspm_dt_bigquery_job_slot_millis_total)

      - record: tenant:dspm_dt_mac_assets_publish_total:sum
        expr: sum(dspm_dt_mac_assets_publish_total) by (action_type, configuration_name, configuration_owner, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dspm_dt_mac_findings_publish_total:sum
        expr: sum(dspm_dt_mac_findings_publish_total) by (action_type, configuration_name, configuration_owner, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dspm_dt_mac_query_execution_duration_seconds_count:sum
        expr: sum(dspm_dt_mac_query_execution_duration_seconds_count) by (action_type, configuration_name, configuration_owner, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dspm_dt_mac_query_execution_duration_seconds_sum:sum
        expr: sum(dspm_dt_mac_query_execution_duration_seconds_sum) by (action_type, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dspm_dt_mac_query_execution_success_count_total:sum
        expr: sum(dspm_dt_mac_query_execution_success_count_total) by (action_type, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dspm_dt_mac_query_execution_failure_count_total:sum
        expr: sum(dspm_dt_mac_query_execution_failure_count_total) by (action_type, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: apisec.recordings
    rules:

      - record: tenant:apisec_enricher_classification_time_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_enricher_classification_time_seconds_bucket[10m])) by (le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_enricher_classification_total:sum
        expr: sum(apisec_enricher_classification_total) by (category, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:apisec_enricher_classification_error_total:sum
        expr: sum(apisec_enricher_classification_error_total) by (app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:apisec_enricher_metablob_parsing_errors_count:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (apisec_enricher_metablob_parsing_errors_count)

      - record: tenant:apisec_inspection_content_update_check:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (apisec_inspection_content_update_check)

      - record: tenant:apisec_inspection_content_update_check_error:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (apisec_inspection_content_update_check_error)

      - record: tenant:apisec_inspection_rule_error:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, rule_name, component) (increase(apisec_inspection_rule_error[5m]))

      - record: tenant:apisec_inspection_issue_creation_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_issue_creation_error_count[5m]))

      - record: tenant:apisec_inspection_lru_error:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, cache_type, error, product_code) (increase(apisec_inspection_lru_error[5m]))

      - record: tenant:apisec_inspection_pubsub_input_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(apisec_inspection_pubsub_input_counter[5m]))

      - record: tenant:apisec_inspection_pubsub_input_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(apisec_inspection_pubsub_input_error_counter[5m]))

      - record: tenant:apisec_inspection_pubsub_output_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(apisec_inspection_pubsub_output_error_counter[5m]))

      - record: tenant:apisec_inspection_telemetry_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, error, product_code) (increase(apisec_inspection_telemetry_error_counter[5m]))

      - record: tenant:apisec_asset_manager_total_publish_asset_messages:increase1h:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_total_publish_asset_messages[1h]))

      - record: tenant:apisec_asset_manager_total_assets_ETL_timeouts:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_total_assets_ETL_timeouts[10m]))

      - record: tenant:apisec_asset_manager_pubsub_input_total_errors:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(apisec_asset_manager_pubsub_input_total_errors[10m]))

      - record: tenant:apisec_asset_manager_total_number_of_pubsub_input_messages:increase30m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(apisec_asset_manager_total_number_of_pubsub_input_messages[30m]))

      - record: tenant:apisec_asset_manager_total_UAI_asset_ingestion_errors:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_total_UAI_asset_ingestion_errors[10m]))

      - record: tenant:apisec_asset_manager_asset_manager_get_assets_total_errors:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code, flowType) (increase(apisec_asset_manager_asset_manager_get_assets_total_errors[10m]))

      - record: tenant:apisec_asset_manager_asset_manager_publish_assets_total_errors:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code, flowType) (increase(apisec_asset_manager_asset_manager_publish_assets_total_errors[10m]))

      - record: tenant:apisec_asset_manager_asset_manager_get_assets_retention_total_errors:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code, flowType) (increase(apisec_asset_manager_asset_manager_get_assets_retention_total_errors[10m]))

      - record: tenant:apisec_asset_manager_asset_manager_delete_assets_total_errors:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code, flowType) (increase(apisec_asset_manager_asset_manager_delete_assets_total_errors[10m]))

      - record: tenant:apisec_asset_manager_get_assets_status_code:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, statusCode) (increase(apisec_asset_manager_get_assets_status_code[10m]))

      - record: tenant:apisec_grouping_service_pubsub_input_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(apisec_grouping_service_pubsub_input_counter[5m]))

      - record: tenant:apisec_grouping_service_pubsub_input_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(apisec_grouping_service_pubsub_input_error_counter[5m]))

      - record: tenant:apisec_grouping_service_pubsub_output_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_pubsub_output_counter[5m]))

      - record: tenant:apisec_grouping_service_pubsub_output_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_pubsub_output_error_counter[5m]))

      - record: tenant:apisec_enricher_apigw_http_transactions_creation_error_count:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_enricher_apigw_http_transactions_creation_error_count[10m]))

      - record: tenant:apisec_enricher_find_geo_ip_errors_count:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_enricher_find_geo_ip_errors_count[10m]))

      - record: tenant:apisec_enricher_metablob_handler_not_found_count:increase10m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_enricher_metablob_handler_not_found_count[10m]))

      - record: tenant:apisec_enricher_find_geo_ip_location_time_ms:max
        expr: max by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (apisec_enricher_find_geo_ip_location_time_ms)

      - record: tenant:apisec_enricher_apigw_handle_metablob_time_ms:max
        expr: max by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (apisec_enricher_apigw_handle_metablob_time_ms)

      - record: tenant:apisec_enricher_http_handler_handle_metablob_time_ms:max
        expr: max by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (apisec_enricher_http_handler_handle_metablob_time_ms)


      - record: tenant:apisec_inspection_content_update_check_duration_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_content_update_check_duration_sum[5m]))

      - record: tenant:apisec_inspection_content_update_check_duration_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_content_update_check_duration_count[5m]))

      - record: tenant:apisec_inspection_content_pull_duration_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_content_pull_duration_sum[5m]))

      - record: tenant:apisec_inspection_content_pull_duration_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_content_pull_duration_count[5m]))

      - record: tenant:apisec_inspection_message_processing_duration_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_message_processing_duration_sum[5m]))

      - record: tenant:apisec_inspection_message_processing_duration_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_message_processing_duration_count[5m]))

      - record: tenant:apisec_asset_manager_publish_uai_flow_duration_seconds_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_publish_uai_flow_duration_seconds_sum[5m]))

      - record: tenant:apisec_asset_manager_publish_uai_flow_duration_seconds_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_publish_uai_flow_duration_seconds_count[5m]))

      - record: tenant:apisec_asset_manager_upsert_asset_flow_duration_seconds_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_upsert_asset_flow_duration_seconds_sum[5m]))

      - record: tenant:apisec_asset_manager_upsert_asset_flow_duration_seconds_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_upsert_asset_flow_duration_seconds_count[5m]))

      - record: tenant:apisec_asset_manager_assets_retention_flow_duration_seconds_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_assets_retention_flow_duration_seconds_sum[5m]))

      - record: tenant:apisec_asset_manager_assets_retention_flow_duration_seconds_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_asset_manager_assets_retention_flow_duration_seconds_count[5m]))

      - record: tenant:apisec_grouping_service_single_transaction_flow_duration_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_single_transaction_flow_duration_sum[5m]))

      - record: tenant:apisec_grouping_service_single_transaction_flow_duration_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_single_transaction_flow_duration_count[5m]))

      - record: tenant:apisec_grouping_service_regex_check_regex_duration_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_regex_check_regex_duration_sum[5m]))

      - record: tenant:apisec_grouping_service_regex_check_regex_duration_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_regex_check_regex_duration_count[5m]))

      - record: tenant:apisec_grouping_service_api_tree_check_tree_logic_duration_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_api_tree_check_tree_logic_duration_sum[5m]))

      - record: tenant:apisec_grouping_service_api_tree_check_tree_logic_duration_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_grouping_service_api_tree_check_tree_logic_duration_count[5m]))

      - record: tenant:apisec_grouping_service_mongo_operation_latency_seconds_sum:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(apisec_grouping_service_mongo_operation_latency_seconds_sum[5m]))

      - record: tenant:apisec_grouping_service_mongo_operation_latency_seconds_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(apisec_grouping_service_mongo_operation_latency_seconds_count[5m]))

      - record: tenant:apisec_inspection_content_pull_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_inspection_content_pull_duration_bucket[5m])) by (le, component, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_inspection_content_update_check_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_inspection_content_update_check_duration_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_inspection_message_processing_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_inspection_message_processing_duration_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_inspection_rule_evaluation_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_inspection_rule_evaluation_duration_bucket[5m])) by (le, component, cycle, rule_name, silent_rule, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_inspection_rule_execution_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_inspection_rule_execution_duration_bucket[5m])) by (le, conponent, cycle, rule_name, silent_rule, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_inspection_api_aggregation_duration_sum:increase5m:sum
        expr: sum by (cache_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_api_aggregation_duration_sum[5m]))

      - record: tenant:apisec_inspection_api_aggregation_duration_count:increase5m:sum
        expr: sum by (cache_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(apisec_inspection_api_aggregation_duration_count[5m]))

      - record: tenant:apisec_inspection_api_aggregation_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_inspection_api_aggregation_duration_bucket[5m])) by (le, cache_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_asset_manager_assets_retention_flow_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_asset_manager_assets_retention_flow_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_asset_manager_publish_uai_flow_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_asset_manager_publish_uai_flow_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_asset_manager_upsert_asset_flow_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_asset_manager_upsert_asset_flow_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_bff_api_latency_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_bff_api_latency_seconds_bucket[5m])) by (le, method, path, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_bff_api_request_size_bytes_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_bff_api_request_size_bytes_bucket[5m])) by (le, method, path, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_bff_api_response_size_bytes_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_bff_api_response_size_bytes_bucket[5m])) by (le, method, path, code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_grouping_service_api_tree_check_tree_logic_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_grouping_service_api_tree_check_tree_logic_duration_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_grouping_service_mongo_operation_latency_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_grouping_service_mongo_operation_latency_seconds_bucket[5m])) by (le, operation, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_grouping_service_regex_check_regex_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_grouping_service_regex_check_regex_duration_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_grouping_service_single_transaction_flow_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_grouping_service_single_transaction_flow_duration_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_findings_upsert_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_findings_upsert_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_drift_detection_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_drift_detection_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_get_speclets_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_get_speclets_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_get_spec_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_get_spec_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_issues_upsert_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_issues_upsert_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_spec_risks_detection_duration_seconds:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_spec_risks_detection_duration_seconds[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_transaction_handling_duration_in_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_transaction_handling_duration_in_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:apisec_risk_engine_transactions_risk_detection_duration_in_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_risk_engine_transactions_risk_detection_duration_in_seconds_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:findings_table_fetching_time_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(findings_table_fetching_time_seconds_bucket[5m])) by (label, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:findings_table_fetching_time_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum(rate(findings_table_fetching_time_seconds_bucket[5m])) by (label, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code))

      - record: tenant:findings_table_fetching_time_seconds_created:sum
        expr: sum by (label, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (findings_table_fetching_time_seconds_created)

      - record: tenant:findings_table_fetching_time_seconds_sum:sum
        expr: sum by (label, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (findings_table_fetching_time_seconds_sum)

      - record: tenant:apisec_risk_engine_transaction_handling_duration_in_seconds_sum:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_transaction_handling_duration_in_seconds_sum[5m]))

      - record: tenant:apisec_risk_engine_transaction_handling_duration_in_seconds_count:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_transaction_handling_duration_in_seconds_count[5m]))

      - record: tenant:apisec_risk_engine_transactions_risk_detection_duration_in_seconds_sum:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_transactions_risk_detection_duration_in_seconds_sum[5m]))

      - record: tenant:apisec_risk_engine_transactions_risk_detection_duration_in_seconds_count:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_transactions_risk_detection_duration_in_seconds_count[5m]))

      - record: tenant:apisec_risk_engine_findings_upsert_duration_seconds_sum:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_findings_upsert_duration_seconds_sum[5m]))

      - record: tenant:apisec_risk_engine_findings_upsert_duration_seconds_count:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_findings_upsert_duration_seconds_count[5m]))

      - record: tenant:apisec_risk_engine_issues_upsert_duration_seconds_count:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_issues_upsert_duration_seconds_count[5m]))

      - record: tenant:apisec_risk_engine_issues_upsert_duration_seconds_sum:rate5m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_risk_engine_issues_upsert_duration_seconds_sum[5m]))

      - record: tenant:apisec_spec_service_errors_total:sum
        expr: sum by (Component, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (apisec_spec_service_errors_total)

      - record: tenant:apisec_spec_service_mongo_duration_seconds:p95
        expr: histogram_quantile(0.95, sum by (Operation, Result, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_spec_service_mongo_duration_seconds_bucket[5m])))

      - record: tenant:apisec_spec_service_pubsub_duration_seconds:p95
        expr: histogram_quantile(0.95, sum by (Operation, Result, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (rate(apisec_spec_service_pubsub_duration_seconds_bucket[5m])))

  - name: ciem.recordings
    rules:
      - record: tenant:ciem_counter_epc_sync_page_upload_failure_counter_total:rate5m:sum
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_counter_epc_sync_page_upload_failure_counter_total[5m]))
      - record: tenant:ciem_gauge_epc_sync_permissions_calculated_gauge:rate5m:sum
        expr: sum by (CLOUD_TYPE, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_gauge_epc_sync_permissions_calculated_gauge[5m]))
      - record: tenant:ciem_timer_epc_consumer_total_time_seconds_max:sum
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_epc_consumer_total_time_seconds_max)
      - record: tenant:ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_max:sum
        expr: sum by (CLOUD_TYPE, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_max)
      - record: tenant:ciem_timer_account_organization_syncer_get_configs_time_seconds_max:sum
        expr: sum by (CLOUD_TYPE, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_account_organization_syncer_get_configs_time_seconds_max)
      - record: tenant:ciem_timer_api_controller_elapsed_time_seconds_count:sum
        expr: sum by (API_CONTROLLER_NAME, API_RETURN_CODE,outcome, tenant_type, product_type, kubernetes_namespace,product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_api_controller_elapsed_time_seconds_count)
      - record: tenant:ciem_timer_api_controller_elapsed_time_seconds_max:sum
        expr: sum by (API_CONTROLLER_NAME, API_RETURN_CODE,outcome, tenant_type, product_type, kubernetes_namespace, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_api_controller_elapsed_time_seconds_max)
      - record: tenant:ciem_counter_epc_validation_snapshot_failure_counter_total:rate5m:sum
        expr: sum by (CLOUD_TYPE, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_counter_epc_validation_snapshot_failure_counter_total[5m]))
      - record: tenant:ciem_timer_epc_sync_total_sync_time_seconds_count:rate5m:sum
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_timer_epc_sync_total_sync_time_seconds_count[5m]))
      - record: tenant:ciem_timer_sync_total_time_seconds_sum:sum
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_timer_sync_total_time_seconds_sum[5m]))
      - record: tenant:ciem_timer_sync_total_time_seconds_count:sum
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_timer_sync_total_time_seconds_count[5m]))
      - record: tenant:ciem_timer_account_health_run_elapsed_time_seconds_count:success:rate12h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(rate(ciem_timer_account_health_run_elapsed_time_seconds_count{outcome="SUCCESS"}[12h]))
      - record: tenant:ciem_timer_account_health_run_elapsed_time_seconds_sum:success:increase12h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(increase(ciem_timer_account_health_run_elapsed_time_seconds_sum{outcome="SUCCESS"}[12h]))
      - record: tenant:ciem_timer_identity_health_publish_elapsed_time_seconds_count:rate8h
        expr: sum by (outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(rate(ciem_timer_identity_health_publish_elapsed_time_seconds_count{}[8h]))
      - record: tenant:ciem_timer_epc_consumer_total_time_seconds_count:increase12h:sum
        expr: sum by (kubernetes_namespace, product_type, product_tier, xdr_id, lcaas_id, outcome)(increase(ciem_timer_epc_consumer_total_time_seconds_count[12h]))
      - record: tenant:ciem_timer_account_health_run_elapsed_time_seconds_count:success:increase12h:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(increase(ciem_timer_account_health_run_elapsed_time_seconds_count{outcome="SUCCESS"}[12h]))
      - record: tenant:ciem_timer_account_health_run_elapsed_time_seconds_sum:success:increase12h:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(increase(ciem_timer_account_health_run_elapsed_time_seconds_sum{outcome="SUCCESS"}[12h]))
      - record: tenant:ciem_timer_identity_health_publish_elapsed_time_seconds_count:rate8h:sum
        expr: sum by (outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(rate(ciem_timer_identity_health_publish_elapsed_time_seconds_count[8h]))
      - record: tenant:ciem_timer_epc_sync_total_sync_time_seconds_sum:rate12h
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_timer_epc_sync_total_sync_time_seconds_sum{outcome="SUCCESS"}[12h]))
      - record: tenant:ciem_timer_epc_sync_total_sync_time_seconds_count:rate12h
        expr: sum by (CLOUD_TYPE, outcome, kubernetes_namespace, product_type, product_tier, xdr_id, lcaas_id, product_code)(rate(ciem_timer_epc_sync_total_sync_time_seconds_count{outcome="SUCCESS"}[12h]))
      - record: tenant:stackdriver_ciem_epc_pubsub_subscription_num_undelivered_messages:avg8h
        expr: avg_over_time(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{subscription_id=~"ciem-epc-task-sub-.*"}[8h])
      - record: tenant:stackdriver_ciem_epc_pubsub_subscription_num_undelivered_messages:sum12h
        expr: sum_over_time(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{subscription_id=~"ciem-epc-task-sub-.*"}[12h])
      - record: tenant:stackdriver_xql_engine_pubsub_subscription_num_undelivered_messages:avg12h
        expr: avg_over_time(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{subscription_id=~"xql-ext-logs-.*-sub"}[12h])
      - record: tenant:ciem_timer_epc_phase_total_time_seconds_count:sum
        expr: sum by (CLOUD_TYPE, epc_phase, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_epc_phase_total_time_seconds_count)
      - record: tenant:ciem_timer_epc_phase_total_time_seconds_max:max
        expr: max by (CLOUD_TYPE, epc_phase, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_epc_phase_total_time_seconds_max)
      - record: tenant:ciem_timer_epc_phase_total_time_seconds_sum:sum
        expr: sum by (CLOUD_TYPE, epc_phase, outcome, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(ciem_timer_epc_phase_total_time_seconds_sum)
      - record: tenant:ciem_counter_health_check_execution_failure_sum:increase10m
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (increase(ciem_counter_health_check_execution_total{app=~".*account-manager.*", outcome="FAILURE"}[10m]))
      - record: tenant:ciem_gauge_epc_validation_passed_since_last_snapshot:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (ciem_gauge_epc_validation_time_passed_since_last_snapshot)
      - record: tenant:ciem_gauge_epc_snapshot_status_account_monitor:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, SNAPSHOP_STATUS, CLOUD_TYPE, product_code) (ciem_gauge_epc_snapshot_status_account_monitor)
      - record: tenant:ciem_gauge_epc_status_account_monitor:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, EPC_STATUS, CLOUD_TYPE, product_code) (ciem_gauge_epc_status_account_monitor)

      - record: tenant:issue_publisher_dlq_oldest_unacked_message_age:max_over_time1h
        expr: |
            max(label_replace(
                  max_over_time(
                    stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{
                      subscription_id=~"ap-issue-ingestion-errors-aispm-.*-sub|ap-issue-ingestion-errors-ciem-.*-sub|ap-issue-ingestion-errors-dspm-.*-sub"
                    }[1h]
                  ) / 3600,
                  "ap_product_type", "$1", "subscription_id", "ap-issue-ingestion-errors-(.*)-.*-sub"
                )) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, subscription_id, ap_product_type)

      - record: tenant:ciem_timer_rule_scanner_orchestrator_run_seconds_count:increase1h
        expr: |
          max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            increase(
              ciem_timer_rule_scanner_orchestrator_run_seconds_count{
                outcome="SUCCESS",
              }[1h]
            )
          ) > 0

      - record: tenant:ciem_timer_rule_scanner_orchestrator_run_seconds_max:max_over_time1h
        expr: |
          max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            max_over_time(
              ciem_timer_rule_scanner_orchestrator_run_seconds_max{
              }[1h]
            )
          )

      - record: tenant:ciem_timer_rule_scanner_run_seconds_count:increase1h
        expr: |
          max by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE, RULE_ID) (
            increase(
              ciem_timer_rule_scanner_run_seconds_count{
                outcome="SUCCESS",
              }[1h]
            )
          )


      - record: tenant:ciem_timer_graph_controller_calculate_graph_seconds:rate1h:avg
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_graph_controller_calculate_graph_seconds_sum[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_graph_controller_calculate_graph_seconds_count[1h])
          )

      - record: tenant:ciem_timer_graph_controller_calculate_graph_seconds_count:failure:rate1h
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_graph_controller_calculate_graph_seconds_count{outcome="FAILURE"}[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_graph_controller_calculate_graph_seconds_count[1h])
          )

      - record: tenant:ciem_timer_table_controller_get_data_seconds:rate1h:avg
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_data_seconds_sum[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_data_seconds_count[1h])
          )

      - record: tenant:ciem_timer_table_controller_get_data_seconds_count:failure:rate1h
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_data_seconds_count{outcome="FAILURE"}[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_data_seconds_count[1h])
          )

      - record: tenant:ciem_timer_table_controller_get_view_def_seconds:rate1h:avg
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_view_def_seconds_sum[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_view_def_seconds_count[1h])
          )

      - record: tenant:ciem_timer_table_controller_get_view_def_seconds_count:failure:rate1h
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_view_def_seconds_count{outcome="FAILURE"}[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome, MODULE) (
            rate(ciem_timer_table_controller_get_view_def_seconds_count[1h])
          )

      - record: tenant:ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds:rate1h:avg
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count[1h])
          )

      - record: tenant:ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count:failure:rate1h
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count{outcome="FAILURE"}[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count[1h])
          )

      - record: tenant:ciem_timer_least_privileged_access_controller_metadata_seconds:rate1h:avg
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_metadata_seconds_sum[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_metadata_seconds_count[1h])
          )

      - record: tenant:ciem_timer_least_privileged_access_controller_metadata_seconds_count:failure:rate1h
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_metadata_seconds_count{outcome="FAILURE"}[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_metadata_seconds_count[1h])
          )

      - record: tenant:ciem_timer_least_privileged_access_controller_optimize_seconds:rate1h:avg
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_optimize_seconds_sum[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_optimize_seconds_count[1h])
          )

      - record: tenant:ciem_timer_least_privileged_access_controller_optimize_seconds_count:failure:rate1h
        expr: |
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_optimize_seconds_count{outcome="FAILURE"}[1h])
          )
          /
          sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code, outcome) (
            rate(ciem_timer_least_privileged_access_controller_optimize_seconds_count[1h])
          )

      - record: tenant:ciem_timer_graph_controller_calculate_graph_seconds_sum:rate12h
        expr: rate(ciem_timer_graph_controller_calculate_graph_seconds_sum)[12h]

      - record: tenant:ciem_timer_graph_controller_calculate_graph_seconds_count:rate12h
        expr: rate(ciem_timer_graph_controller_calculate_graph_seconds_count)[12h]

      - record: tenant:ciem_timer_table_controller_get_data_seconds_sum:rate12h
        expr: rate(ciem_timer_table_controller_get_data_seconds_sum)[12h]

      - record: tenant:ciem_timer_table_controller_get_data_seconds_count:rate12h
        expr: rate(ciem_timer_table_controller_get_data_seconds_count)[12h]

      - record: tenant:ciem_timer_table_controller_get_view_def_seconds_sum:rate12h
        expr: rate(ciem_timer_table_controller_get_view_def_seconds_sum)[12h]

      - record: tenant:ciem_timer_table_controller_get_view_def_seconds_count:rate12h
        expr: rate(ciem_timer_table_controller_get_view_def_seconds_count)[12h]

      - record: tenant:ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum:rate12h
        expr: rate(ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum)[12h]

      - record: tenant:ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum:rate12h
        expr: rate(ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count)[12h]

      - record: tenant:ciem_timer_least_privileged_access_controller_metadata_seconds_sum:rate12h
        expr: rate(ciem_timer_least_privileged_access_controller_metadata_seconds_sum)[12h]

      - record: tenant:ciem_timer_least_privileged_access_controller_metadata_seconds_count:rate12h
        expr: rate(ciem_timer_least_privileged_access_controller_metadata_seconds_count)[12h]

      - record: tenant:ciem_timer_least_privileged_access_controller_optimize_seconds_sum:rate12h
        expr: rate(ciem_timer_least_privileged_access_controller_optimize_seconds_sum)[12h]

      - record: tenant:ciem_timer_least_privileged_access_controller_optimize_seconds_count:rate12h
        expr: rate(ciem_timer_least_privileged_access_controller_optimize_seconds_count)[12h]

  - name: itdr.recordings
    rules:
      - record: tenant:itdr_risk_processor_risk_cron_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_risk_processor_risk_cron_duration_seconds_sum[5m]))

      - record: tenant:itdr_risk_processor_risk_cron_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_risk_processor_risk_cron_duration_seconds_count[5m]))

      - record: tenant:itdr_risk_processor_risk_cron_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_risk_processor_risk_cron_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_risk_processor_dao_access_duration_seconds_sum:increase5m:sum
        expr: sum by (dao_type, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_risk_processor_dao_access_duration_seconds_sum[5m]))

      - record: tenant:itdr_risk_processor_dao_access_duration_seconds_count:increase5m:sum
        expr: sum by (dao_type, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_risk_processor_dao_access_duration_seconds_count[5m]))

      - record: tenant:itdr_risk_processor_dao_access_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_risk_processor_dao_access_duration_seconds_bucket[5m])) by (le, dao_type, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_risk_processor_pubsub_input_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(itdr_risk_processor_pubsub_input_error_counter[5m]))

      - record: tenant:itdr_risk_processor_pubsub_output_error_counter:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, topic, error, product_code) (increase(itdr_risk_processor_pubsub_output_error_counter[5m]))

      - record: tenant:itdr_data_pipeline_asset_enrichers_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_asset_enrichers_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code))

      - record: tenant:itdr_data_pipeline_asset_enrichers_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_asset_enrichers_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_asset_enrichers_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_asset_enrichers_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_assetless_finding_syncer_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_assetless_finding_syncer_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code))

      - record: tenant:itdr_data_pipeline_assetless_finding_syncer_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_assetless_finding_syncer_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_assetless_finding_syncer_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_assetless_finding_syncer_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_single_enricher_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_single_enricher_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code, enricher_type))

      - record: tenant:itdr_data_pipeline_single_enricher_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code, enricher_type) (increase(itdr_data_pipeline_single_enricher_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_single_enricher_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code, enricher_type) (increase(itdr_data_pipeline_single_enricher_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_cdc_syncer_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_cdc_syncer_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code))

      - record: tenant:itdr_data_pipeline_cdc_syncer_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_cdc_syncer_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_cdc_syncer_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_cdc_syncer_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_assets_relations_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_assets_relations_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code))

      - record: tenant:itdr_data_pipeline_assets_relations_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_assets_relations_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_assets_relations_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_assets_relations_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_asset_updates_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_asset_updates_count_total[5m]))

      - record: tenant:itdr_data_pipeline_asset_updates_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_asset_updates_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code))

      - record: tenant:itdr_data_pipeline_asset_updates_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_asset_updates_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_asset_updates_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_asset_updates_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_asset_updates_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, operation, product_code) (increase(itdr_data_pipeline_asset_updates_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_asset_updates_manager_input_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_asset_updates_manager_input_count_total[5m]))

      - record: tenant:itdr_data_pipeline_asset_updates_manager_input_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(itdr_data_pipeline_asset_updates_manager_input_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_dss_sync_input_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(itdr_data_pipeline_dss_sync_input_error_count[5m]))

      - record: tenant:itdr_data_pipeline_dss_sync_input_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(itdr_data_pipeline_dss_sync_input_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_cie_row_errors_processed:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_cie_row_errors_processed[5m]))

      - record: tenant:itdr_data_pipeline_cie_row_errors_processed_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_cie_row_errors_processed_total[5m]))

      - record: tenant:itdr_data_pipeline_asset_metadata_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(itdr_data_pipeline_asset_metadata_error_count[5m]))

      - record: tenant:itdr_data_pipeline_asset_metadata_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(itdr_data_pipeline_asset_metadata_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_cdc_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(itdr_data_pipeline_cdc_error_count[5m]))

      - record: tenant:itdr_data_pipeline_cdc_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(itdr_data_pipeline_cdc_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_risk_handler_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(itdr_data_pipeline_risk_handler_error_count[5m]))

      - record: tenant:itdr_data_pipeline_cie_total_rows_process_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cie_total_rows_process_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_cie_total_rows_process_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cie_total_rows_process_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_cie_total_rows_process_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_cie_total_rows_process_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_baseline_total_rows_process_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_baseline_total_rows_process_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_baseline_total_rows_process_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_baseline_total_rows_process_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_baseline_total_rows_process_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_baseline_total_rows_process_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_cap_data_repository_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cap_data_repository_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_cap_data_repository_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cap_data_repository_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_cap_data_repository_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_cap_data_repository_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_cap_data_upload_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cap_data_upload_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_cap_data_upload_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cap_data_upload_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_cap_data_upload_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_cap_data_upload_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_realtime_updater_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_realtime_updater_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_realtime_updater_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_realtime_updater_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_realtime_updater_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_realtime_updater_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_cie_rows_processed:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_cie_rows_processed[5m]))

      - record: tenant:itdr_data_pipeline_cie_rows_processed_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_cie_rows_processed_total[5m]))

      - record: tenant:itdr_data_pipeline_deleted_assets_total_rows_process_duration_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_deleted_assets_total_rows_process_duration_sum[5m]))

      - record: tenant:itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_deleted_assets_total_rows_process_duration_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_deleted_assets_total_rows_process_duration_count[5m]))

      - record: tenant:itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_deleted_assets_total_rows_process_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_deleted_assets_total_rows_process_duration_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_asset_metadata_read_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_asset_metadata_read_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_asset_metadata_read_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_asset_metadata_read_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_asset_metadata_read_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_asset_metadata_read_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_asset_metadata_write_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_asset_metadata_write_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_asset_metadata_write_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_asset_metadata_write_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_asset_metadata_write_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_asset_metadata_write_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_cdc_write_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cdc_write_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_cdc_write_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_cdc_write_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_cdc_write_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_cdc_write_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_risk_handler_read_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_risk_handler_read_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_risk_handler_read_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_risk_handler_read_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_risk_handler_read_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_risk_handler_read_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_assets_retention_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_assets_retention_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_assets_retention_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_assets_retention_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_assets_retention_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_assets_retention_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_baseline_metadata_error_counter_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code, operation) (increase(itdr_data_pipeline_baseline_metadata_error_counter_total[5m]))

      - record: tenant:itdr_data_pipeline_password_analyzer_input_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_password_analyzer_input_count[5m]))

      - record: tenant:itdr_data_pipeline_password_analyzer_input_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(itdr_data_pipeline_password_analyzer_input_error_count[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_process_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_pwd_analyzer_process_duration_seconds_sum[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_process_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_pwd_analyzer_process_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_process_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_pwd_analyzer_process_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_password_analyzer_error:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, analyze_type, product_code) (increase(itdr_data_pipeline_pwd_analyzer_password_analyzer_error[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_handle_open_findings_error:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, analyze_type, product_code) (increase(itdr_data_pipeline_pwd_analyzer_handle_open_findings_error[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_handle_close_findings_error:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, analyze_type, product_code) (increase(itdr_data_pipeline_pwd_analyzer_handle_close_findings_error[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_dao_read_error:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, analyze_type, product_code) (increase(itdr_data_pipeline_pwd_analyzer_dao_read_error[5m]))

      - record: tenant:itdr_data_pipeline_pwd_analyzer_matches:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, analyze_type, product_code) (increase(itdr_data_pipeline_pwd_analyzer_matches[5m]))

      - record: tenant:itdr_data_pipeline_cie_enricher_error_count:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, enricher_type, product_code) (increase(itdr_data_pipeline_cie_enricher_error_count[5m]))

      - record: tenant:itdr_data_pipeline_cie_enricher_duration_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, enricher_type, product_code) (increase(itdr_data_pipeline_cie_enricher_duration_sum[5m]))

      - record: tenant:itdr_data_pipeline_cie_enricher_duration_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, enricher_type, product_code) (increase(itdr_data_pipeline_cie_enricher_duration_count[5m]))

      - record: tenant:itdr_data_pipeline_cie_enricher_duration_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_cie_enricher_duration_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, enricher_type, product_code))

      - record: tenant:itdr_data_pipeline_dp_bus_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, operation, product_code) (increase(itdr_data_pipeline_dp_bus_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_ad_hygiene_input_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, product_code) (increase(itdr_data_pipeline_ad_hygiene_input_count_total[5m]))

      - record: tenant:itdr_data_pipeline_ad_hygiene_input_error_count_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, source, error, result, product_code) (increase(itdr_data_pipeline_ad_hygiene_input_error_count_total[5m]))

      - record: tenant:itdr_data_pipeline_ad_hygiene_dtos_processed_total:increase5m:sum
        expr: sum by (container, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product, product_code) (increase(itdr_data_pipeline_ad_hygiene_dtos_processed_total[5m]))

      - record: tenant:itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_bucket[5m])) by (le, kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code))

      - record: tenant:itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_count:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_count[5m]))

      - record: tenant:itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_sum:increase5m:sum
        expr: sum by (kubernetes_namespace, namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code) (increase(itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_sum[5m]))

  - name: cwp_core.recordings
    rules:
      - record: tenant:cwp_ci_analyzer_api_latency_seconds_bucket:p95
        expr: histogram_quantile(0.95, sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, method, path, tenant_id, product_code)(rate(cwp_api_latency_seconds_bucket{app=~".*cwp-ci-analyzer.*", path="/public_api/cwp/ci/analyze-image"}[5m])))
      - record: tenant:cwp_api_service_api_latency_seconds_bucket:p95
        expr: histogram_quantile(0.95, sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, method, path, tenant_id, product_code)(rate(cwp_api_latency_seconds_bucket{app=~".*cwp-api.*"}[5m])))
      - record: tenant:cwp_rules_management_api_latency_seconds_bucket:p95
        expr: histogram_quantile(0.95, sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, method, path, tenant_id, product_code)(rate(cwp_api_latency_seconds_bucket{app=~".*cwp-rules-management.*", path=~"/api/v1/policies|/api/v1/policies/disable|/api/v1/policies/enable|/api/v1/rules"}[5m])))

  - name: cloud_asset_collection.recordings
    rules:
      - record: tenant:cloud_assets_collection_csp_api_request_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum by (cloud_provider, le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, api_status_code, product_code)(cloud_assets_collection_csp_api_request_duration_seconds_bucket))

      - record: tenant:cloud_assets_collection_csp_api_request_duration_seconds_count:sum
        expr: sum by (cloud_provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, api_status_code, product_code)(cloud_assets_collection_csp_api_request_duration_seconds_count)

      - record: tenant:cloud_assets_collection_csp_api_request_duration_seconds_sum:sum
        expr: sum by (cloud_provider, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, api_status_code, product_code)(cloud_assets_collection_csp_api_request_duration_seconds_sum)

      - record: tenant:cloud_assets_collection_message_processing_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, message_priority, product_code)(cloud_assets_collection_message_processing_duration_seconds_bucket))

      - record: tenant:cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, message_priority, product_code)(cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket))

      - record: tenant:cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, message_priority, product_code)(cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket))

      - record: tenant:cloud_assets_collection_platform_api_request_duration_seconds_bucket:p99
        expr:  histogram_quantile(0.99, sum by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, source, api_status_code, product_code)(cloud_assets_collection_platform_api_request_duration_seconds_bucket))

      - record: tenant:cloud_assets_collection_num_failed_tasks_total:sum
        expr:  sum by (source, cloud_provider, rate_limit_group, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(cloud_assets_collection_num_failed_tasks_total)

      - record: tenant:cloud_assets_collection_num_successful_tasks_total:sum
        expr:  sum by (source, cloud_provider, rate_limit_group, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, product_code)(cloud_assets_collection_num_successful_tasks_total)

  - name: command_center.recordings
    rules:
      - record: tenant:cc_cache_update_time_seconds_sum:sum
        expr: sum (cc_cache_update_time_seconds_sum) by (http_response_status_code, http_route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cc_cache_update_time_seconds_count:sum
        expr: sum (cc_cache_update_time_seconds_count) by (http_response_status_code, http_route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:cc_cache_update_key_failure_total:sum
        expr: sum (cc_cache_update_key_failure_total) by (http_response_status_code, http_route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dashboard_api_4xx_failure_total:sum
        expr: sum (dashboard_api_4xx_failure_total) by (http_response_status_code, http_route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

      - record: tenant:dashboard_api_5xx_failure_total:sum
        expr: sum (dashboard_api_5xx_failure_total) by (http_response_status_code, http_route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)


  - name: search_and_investigate.recordings
    rules:
      - record: tenant:auto_suggest_failure_total:sum
        expr: sum (auto_suggest_failure_total) by (service_name, http_response_status_code, http_route, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)

  - name: cwp_ads.recordings
    rules:
      - record: tenant:cwp_ads_missed_sla_instances:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, instance_state, product_code)(cwp_ads_missed_sla_instances)

      - record: tenant:cwp_ads_instances_new_or_final_state:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, instance_state, product_code)(cwp_ads_instances)

      - record: tenant:cwp_ads_instances:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, status_code, product_code)(cwp_ads_instances)

      - record: tenant:cwp_prioritization_best_effort_prioritized_scanned_assets:rate12h
        expr: rate(cwp_prioritization_best_effort_prioritized_scanned_assets_counter_total[12h])

      - record: tenant:cwp_prioritization_best_effort_free_capacity_gauge_ratio:avg12h:avg
        expr: avg by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(avg_over_time(cwp_prioritization_best_effort_free_capacity_gauge_ratio[12h]))

      - record: tenant:cwp_snapshot_lifetime_seconds_sum:avg12h
        expr: avg_over_time(cwp_snapshot_lifetime_seconds_sum[12h])

      - record: tenant:cwp_snapshot_lifetime_seconds_count:avg12h
        expr: avg_over_time(cwp_snapshot_lifetime_seconds_count[12h])

      - record: tenant:cwp_snapshot_free_capacity_ratio:avg12h:avg
        expr: avg by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(avg_over_time(cwp_snapshot_free_capacity_ratio[12h]))

      - record: tenant:cwp_operation_duration_seconds_sum:rate12h:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, cloud_provider, phase, product_code)(rate(cwp_operation_duration_seconds_sum[12h]))

      - record: tenant:cwp_operation_duration_seconds_count:rate12h:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, cloud_provider, phase, product_code)(rate(cwp_operation_duration_seconds_count[12h]))

      - record: tenant:cwp_ads_instances_internal_errors:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code=~"Timeout|Internal Error|Provider Error"})

      - record: tenant:cwp_ads_instances_unsupported:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code=~"Unsupported OS|Unsupported Architecture|Azure Ephemeral OS Disk|Azure ADE Encryption|Azure Unmanaged Disk|Unsupported AWS Volume Type|Encryption Customer Key"})

      - record: tenant:cwp_ads_instances_missing_included_tags_excluded_tags:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code=~"Missing Included Tags|Excluded Tags"})

      - record: tenant:cwp_ads_instances_permissions_error:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code=~"Encryption Missing Permission|Permission Error"})

      - record: tenant:cwp_ads_instances_marketplace_error:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code=~"Marketplace Error"})

      - record: tenant:cwp_ads_instances_invalid_status_codes:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code!~"Pending|Scanned|Missing Included Tags|Excluded Tags|Unsupported Image Owner"})

      - record: tenant:cwp_ads_instances_all_scanned_instances:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(cwp_ads_instances{status_code!~"Pending"})

  - name: cas.recordings
    rules:
      - record: tenant:cas_dashboards_api_service_duration_seconds_count:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_dashboards_api_service_duration_seconds_count[10m]))

      - record: tenant:cas_dashboards_api_service_duration_seconds_sum:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_dashboards_api_service_duration_seconds_sum[10m]))

      - record: tenant:cas_dashboards_api_service_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(cas_dashboards_api_service_duration_seconds_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code))

      - record: tenant:cas_dashboards_api_service_duration_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum(rate(cas_dashboards_api_service_duration_seconds_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code))

      - record: tenant:cas_dashboards_api_service_duration_seconds_bucket:p50
        expr: histogram_quantile(0.5, sum(rate(cas_dashboards_api_service_duration_seconds_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code))

      - record: tenant:cas_applications_job_application_functions_seconds_count:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_applications_job_application_functions_seconds_count[10m]))

      - record: tenant:cas_applications_job_application_functions_seconds_sum:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_applications_job_application_functions_seconds_sum[10m]))

      - record: tenant:cas_applications_job_application_functions_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(cas_applications_job_application_functions_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code))

      - record: tenant:cas_applications_job_application_functions_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum(rate(cas_applications_job_application_functions_seconds_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code))

      - record: tenant:cas_applications_job_application_functions_seconds_bucket:p50
        expr: histogram_quantile(0.5, sum(rate(cas_applications_job_application_functions_seconds_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code))
      - record: tenant:cas_product_analytics_send_to_cortex_seconds_count:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_product_analytics_send_to_cortex_seconds_count[10m]))

      - record: tenant:cas_unified_cli_command_count_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_unified_cli_command_count_total[10m]))

      - record: tenant:cas_unified_cli_os_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_unified_cli_os_total[10m]))

      - record: tenant:cas_unified_cli_scan_result_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, service_name, status, product_code)(increase(cas_unified_cli_scan_result_total[10m]))

      - record: tenant:scanners_rules_cli_api_end_scan_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, product_code) (rate(cas_http_request_duration_seconds_bucket{path="/public_api/cas/v1/cli/scan/end"}[5m])))

      - record: tenant:scanners_rules_cli_api_start_scan_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, product_code) (rate(cas_http_request_duration_seconds_bucket{path="/public_api/cas/v1/cli/scan/start"}[5m])))

      - record: tenant:scanners_rules_iac_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, product_code) (rate(argo_workflows_cas_scanners_scanner_duration_secs_bucket{scanner_type="IAC"}[5m])))

      - record: tenant:scanners_rules_sca_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, product_code) (rate(argo_workflows_cas_scanners_scanner_duration_secs_bucket{scanner_type="SCA"}[5m])))

      - record: tenant:scanners_rules_secrets_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, product_code) (rate(argo_workflows_cas_scanners_scanner_duration_secs_bucket{scanner_type="SECRETS"}[5m])))

      - record: tenant:scanners_rules_pre_scan_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, scanner_type, le, product_code) (rate(argo_workflows_cas_scanners_pre_scan_duration_secs_bucket[5m])))

      - record: tenant:scanners_rules_post_scan_latency_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum by(kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, scanner_type, le, product_code) (rate(argo_workflows_cas_scanners_post_scan_duration_secs_bucket[5m])))

      - record: tenant:cas_persistence_app_genesis_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_app_genesis_errors_total[10m]))

      - record: tenant:cas_persistence_app_lens_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_app_lens_errors_total[10m]))

      - record: tenant:cas_persistence_app_stream_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_app_stream_errors_total[10m]))

      - record: tenant:cas_persistence_app_code_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_app_code_errors_total[10m]))

      - record: tenant:cas_persistence_scai_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_scai_errors_total[10m]))

      - record: tenant:cas_persistence_scan_ops_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_scan_ops_errors_total[10m]))

      - record: tenant:cas_persistence_core_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_core_errors_total[10m]))

      - record: tenant:cas_persistence_flow_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_flow_errors_total[10m]))

      - record: tenant:cas_persistence_issues_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_issues_errors_total[10m]))

      - record: tenant:cas_persistence_lilo_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_lilo_errors_total[10m]))

      - record: tenant:cas_persistence_stitch_errors_total:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, severity, error_code, product_code )(increase(cas_persistence_stitch_errors_total[10m]))

      - record: tenant:cas_source_control_source_control_pr_scan_until_status_duration_seconds_count:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, files_count_classify, product_code)(increase(cas_source_control_source_control_pr_scan_until_status_duration_seconds_count[10m]))

      - record: tenant:cas_source_control_source_control_pr_scan_until_status_duration_seconds_sum:increase10m:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, files_count_classify, product_code)(increase(cas_source_control_source_control_pr_scan_until_status_duration_seconds_sum[10m]))

      - record: tenant:cas_source_control_source_control_pr_scan_until_status_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(cas_source_control_source_control_pr_scan_until_status_duration_seconds_bucket[10m])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, lcaas_id, files_count_classify, product_code))
  - name: flux.recordings
    interval: 5m
    rules:
    - record: tenant:flux_resource_info:sum
      expr: sum by (customresource_kind, ready, suspended, exported_namespace, name, chart_name, chart_version, chart_app_version, chart_source_name, kubernetes_name, revision, product_code)(flux_resource_info)
      labels:
        workspace: "tenant"
  - name: api_spec_recordings
    rules:
      - record: tenant:apisec_spec_service_file_size_bytes_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_spec_service_file_size_bytes_bucket[5m])) by (Component, FileType, kubernetes_cronjob, le, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

      - record: tenant:apisec_spec_service_file_size_bytes_bucket:p90
        expr: histogram_quantile(0.90, sum(rate(apisec_spec_service_file_size_bytes_bucket[5m])) by (Component, FileType, kubernetes_cronjob, le, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

      - record: tenant:apisec_spec_service_file_size_bytes_bucket:p50
        expr: histogram_quantile(0.50, sum(rate(apisec_spec_service_file_size_bytes_bucket[5m])) by (Component, FileType, kubernetes_cronjob, le, service_name, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

      - record: tenant:apisec_spec_service_cron_spec_gate_duration_seconds_bucket:p99
        expr: histogram_quantile(0.99, sum(rate(apisec_spec_service_cron_spec_gate_duration_seconds_bucket[5m])) by (Operation, Result, service_name, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

      - record: tenant:apisec_spec_service_cron_spec_gate_duration_seconds_bucket:p90
        expr: histogram_quantile(0.90, sum(rate(apisec_spec_service_cron_spec_gate_duration_seconds_bucket[5m])) by (Operation, Result, service_name, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

      - record: tenant:apisec_spec_service_cron_spec_gate_duration_seconds_bucket:p50
        expr: histogram_quantile(0.50, sum(rate(apisec_spec_service_cron_spec_gate_duration_seconds_bucket[5m])) by (Operation, Result, service_name, le, app, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

      
  - name: cwp_sp_snapshot.recordings
    rules:
      - record: tenant:cwp_sp_snapshot_csp_requests:sum:increase12h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, successful, operation, product_code)(increase(cwp_sp_snapshot_csp_requests_total[12h]))

      - record: tenant:cwp_sp_snapshot_requests_missed_sla:max_over_time1h
        expr: max by (kubernetes_namespac, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code) (max_over_time(cwp_sp_snapshot_requests_missed_sla[1h]))

      - record: tenant:cwp_sp_snapshot_dangling:sum:increase12h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(increase(cwp_sp_snapshot_dangling_total[12h]))

      - record: tenant:cwp_sp_snapshot_auto_deleted:sum:increase12h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, asset_state, product_code)(increase(cwp_sp_snapshot_auto_deleted_total[12h]))

      - record: tenant:cwp_sp_snapshot_operation_duration_seconds:p99:12h
        expr: histogram_quantile(0.99, sum(rate(cwp_sp_snapshot_operation_duration_seconds_bucket[12h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, operation, le, product_code))

      - record: tenant:cwp_sp_snapshot_api_latency_seconds:p99:1h
        expr: histogram_quantile(0.99, sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, product_code)(rate(cwp_api_latency_seconds_bucket{app=~".*cwp-sp-snapshot.*"}[1h])))

      - record: tenant:cwp_sp_snapshot_api:sum:increase1h
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, code, product_code)(increase(cwp_api_requests_total{app=~".*cwp-sp-snapshot.*"}[1h]))

      - record: tenant:cwp_sp_cwp_sp_snapshot_lifetime:p95:1h
        expr: histogram_quantile(0.95, sum(rate(cwp_sp_snapshot_lifetime_seconds_bucket[1h])) by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, le, snapshot_type, product_code))

      - record: tenant:xql_email_errors_total:sum:max_over_time1h
        expr: sum by (error_type, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(max_over_time(xql_email_errors_total[12h]))

      - record: tenant:migration_time_consumed:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, type, product_code)(migration_time_consumed)

      - record: tenant:rule_migration_result:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, type, product_code)(rule_migration_result)

      - record: tenant:rule_migration_errors_total:sum
        expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, type, service_name, product_code)(rule_migration_errors_total)

      - record: tenant:xpanse_global_lookup_request_errors_total:sum:increase1h
        expr: sum by (path, source, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(increase(xpanse_global_lookup_request_errors_total[1h]))

      - record: tenant:xpanse_global_lookup_request_time_sum:sum
        expr: sum by (path, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(xpanse_global_lookup_request_time_sum)

      - record: tenant:xpanse_global_lookup_request_time_count:sum
        expr: sum by (path, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, product_code)(xpanse_global_lookup_request_time_count)
