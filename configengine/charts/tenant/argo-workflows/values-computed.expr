_component_name: "'argo-workflows'"

fullnameOverride: '"argo-workflows"'
namespaceOverride: "globals.cas_namespace"

_argo:
  argo_workflows_controller_default_request_time: '"4s"'
  workflow_retention: '"7d"'
  argo_registry: globals.gcr + "/golden-images"

_pghost: 'globals.st_resource_prefix + "-postgres"'
_argo_database_name: '"argo"'
_argo_database_username: '"argo"'

argo_workflows_create_db_and_user:
  name: 'local.fullnameOverride + "-create-db-and-user-hook"'
  namespace: globals.st_namespace
  containerName: '"psql"'
  command:
    - '"psql"'
  args:
    - '"-c"'
    - '"CREATE DATABASE $(ARGO_DATABASE_NAME);"'
    - '"-c"'
    - |-
      "CREATE USER $(ARGO_DATABASE_USERNAME) WITH ENCRYPTED PASSWORD '$(ARGO_DATABASE_PASSWORD)';"
    - '"-c"'
    - '"GRANT ALL PRIVILEGES ON DATABASE $(ARGO_DATABASE_NAME) TO $(ARGO_DATABASE_USERNAME);"'
    - '"-c"'
    - '"GRANT ALL ON DATABASE $(ARGO_DATABASE_NAME) to $(ARGO_DATABASE_USERNAME);"'
    - '"-c"'
    - '"ALTER DATABASE $(ARGO_DATABASE_NAME) owner to $(ARGO_DATABASE_USERNAME);"'
  backoffLimit: '"0"'
  jobAnnotations:
    helm.sh/hook: '"pre-install,pre-upgrade"'
    helm.sh/hook-weight: '"-100"'
    helm.sh/hook-delete-policy: '"before-hook-creation,hook-succeeded"'
  image:
    fullName: 'globals.gcr + "/golden-images/postgres:16.2"'
  restartPolicy: '"Never"'
  automountServiceAccountToken: true
  env:
    PGUSER: '"root"'
    PGHOST: local._pghost
    PGPASSWORD:
      valueFrom:
        secretKeyRef:
          key: "'postgres_password'"
          name: "globals.tenant_secrets"
          optional: false
    ARGO_DATABASE_NAME: local._argo_database_name

images:
  pullPolicy: '"IfNotPresent"'

workflow:
  serviceAccount:
    create: "false"
  rbac:
    serviceAccounts:
      - name: '"argo-controller"'
        namespace: "globals.cas_namespace"
      - name: '"persistence-flow-cas"'
        namespace: "globals.cas_namespace"
      - name: '"source-control-cas"'
        namespace: "globals.cas_namespace"
      - name: '"deppy-scanner-cas"'
        namespace: "globals.cas_namespace"
      - name: '"secret-verifier-cas"'
        namespace: "globals.cas_namespace"
      - name: '"scanner-remediation-cas"'
        namespace: "globals.cas_namespace"
      - name: '"sca-scanner-cas"'
        namespace: "globals.cas_namespace"
      - name: '"application-flow-cas"'
        namespace: "globals.cas_namespace"
      - name: '"aspm-issue-urgency"'
        namespace: "globals.cas_namespace"
      - name: '"scanner-orchestration-cas"'
        namespace: "globals.cas_namespace"
      - name: '"iac-scanner-cas"'
        namespace: "globals.cas_namespace"
      - name: '"cicd-cas"'
        namespace: "globals.cas_namespace"
      - name: '"billing-cas"'
        namespace: "globals.cas_namespace"
      - name: '"product-analytics-cas"'
        namespace: "globals.cas_namespace"
      - name: '"code-to-cloud-workflow-cas"'
        namespace: "globals.cas_namespace"
      - name: '"collectors-cas"'
        namespace: "globals.cas_namespace"
artifactRepository:
  archiveLogs: "true"
  gcs:
    bucket: 'tenant.project_id+"-argo-workflows-logs"'
server:
  enabled: true
  nodeSelector:
    xdr-pool: '"wi-dynamic"'
  autoscaling:
    enabled: true
    minReplicas: 1
  pdb:
    enabled: true
    minAvailable: 0
  resources:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "100m" : "500m"'
      memory: '"512Mi"'
    limits:
      cpu: '"4"'
      memory: '"4Gi"'
  serviceAccount:
    create: true
    name: '"argo-server"'
    annotations:
      iam.gke.io/gcp-service-account: '"argo-server" + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
  image:
    registry: "local._argo.argo_registry"
    repository: '"argo/argocli"'
    tag: 'region.is_fedramp ? "v3.6.10" : "v3.6.2"'
mainContainer:
  resources:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "150m" : "250m"'
      memory: '"256Mi"'
      ephemeral-storage: '"50Mi"'
    limits:
      cpu: '"500m"'
      memory: '"512Mi"'
      ephemeral-storage: '"500Mi"'

executor:
  image:
    registry: "local._argo.argo_registry"
    repository: '"argo/argoexec"'
    tag: 'region.is_fedramp ? "v3.6.10" : "v3.6.2"'
  resources:
    requests:
      cpu: '"100m"'
      memory: '"64Mi"'
      ephemeral-storage: '"50Mi"'
    limits:
      cpu: '"500m"'
      memory: '"512Mi"'
      ephemeral-storage: '"500Mi"'
  env:
    - name: '"GCE_METADATA_HOST"'
      value: '"***************"'

controller:
  links:
    - name: '"Children"'
      scope: '"workflow"'
      url: '"http://localhost:2746/workflows/cas?&label=parent-workflow%3D${workflow.metadata.labels.name}"'
    - name: '"Parent"'
      scope: '"workflow"'
      url: '"http://localhost:2746/workflows/cas/${workflow.metadata.labels.parent-workflow}"'
    - name: '"gcpLogs"'
      scope: '"workflow"'
      url: '"https://console.cloud.google.com/logs/query;query=%22${workflow.metadata.labels.name}%22;customDuration=today?project=" + tenant.project_id'
  extraEnv:
    - name: '"DEFAULT_REQUEUE_TIME"'
      value: "local._argo.argo_workflows_controller_default_request_time"
  workflowWorkers: "50"
  workflowTTLWorkers: "40"
  podCleanupWorkers: "10"
  extraArgs:
    - '"--qps"'
    - '"40"'
    - '"--burst"'
    - '"50"'
  nodeSelector:
    xdr-pool: '"wi-static"'
  tolerations:
  - key: '"xdr-pool"'
    operator: '"Equal"'
    value: '"wi-static"'
  resources:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "250m" : "500m"'
      memory: '"512Mi"'
    limits:
      cpu: '"8"'
      memory: '"4Gi"'
  workflowDefaults:
    spec:
      activeDeadlineSeconds: 172800
      nodeSelector:
        xdr-pool: '"argo-nodepool"'
      tolerations:
      - key: '"xdr-pool"'
        value: '"argo-nodepool"'
        operator: '"Equal"'
        effect: '"NoSchedule"'
      serviceAccountName: '"argo-controller"'
      ttlStrategy:
        secondsAfterCompletion: 900
      podGC:
        strategy: '"OnPodCompletion"'
      artifactGC:
        strategy: '"Never"'
  podAnnotations:
    prometheus.io/scrape: '"true"'
  pdb:
    enabled: true
    minAvailable: 0
  replicas: 1
  metricsConfig:
    enabled: true
    port: 8080
  serviceAccount:
    create: true
    name: '"argo-controller"'
    annotations:
      iam.gke.io/gcp-service-account: '"argo-controller" + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
  image:
    registry: "local._argo.argo_registry"
    repository: '"argo/workflow-controller"'
    tag: 'region.is_fedramp ? "v3.6.10" : "v3.6.2"'
  persistence:
    archiveTTL: "local._argo.workflow_retention"
    connectionPool:
      maxIdleConns: 100
      maxOpenConns: 500
    # save the entire workflow into etcd and DB
    nodeStatusOffLoad: "true"
    # enable archiving of old workflows
    archive: "true"
    postgresql:
      host: '"xdr-st-" + tenant.lcaas_id + "-postgres." + globals.st_namespace'
      port: 5432
      database: '"argo"'
      tableName: '"argo_workflows"'
      # the database secrets must be in the same namespace of the controller
      userNameSecret:
        name: '"argo-workflows-postgress"'
        key: '"username"'
      passwordSecret:
        name: '"argo-workflows-postgress"'
        key: '"password"'
      ssl: "false"
      # sslMode must be one of: disable, require, verify-ca, verify-full
      # you can find more information about those ssl options here: https://godoc.org/github.com/lib/pq
      sslMode: '"disable"'
  priorityClassName: '"high-priority-deployment"'
