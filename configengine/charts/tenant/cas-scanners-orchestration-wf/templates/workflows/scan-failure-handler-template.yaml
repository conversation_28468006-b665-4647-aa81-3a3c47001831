apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    type: scan-failure-handler-template
  name: scan-failure-handler-template
  namespace: {{.Values.namespaceOverride}}
spec:
  arguments:
    parameters:
    - name: trace-id
      value: missing-trace-id
    - name: scan-id
      value: '{{`{{workflow.uid}}`}}'
  entrypoint: main
  metrics:
    prometheus:
    - help: Workflow duration by name
      histogram:
        buckets:
        - 5.0
        - 30.0
        - 60.0
        - 120.0
        - 300.0
        - 600.0
        - 1800.0
        - 3600.0
        value: '{{`{{workflow.duration}}`}}'
      labels:
      - key: name
        value: scan_failure_handler_template
      - key: namespace
        value: {{.Values.namespaceOverride}}
      - key: status
        value: '{{`{{workflow.status}}`}}'
      name: workflow_duration_secs
  templates:
  - container:
      args:
      - --scan-id
      - '{{`{{inputs.parameters.scan-id}}`}}'
      - --repository-id
      - '{{`{{inputs.parameters.repository-id}}`}}'
      - --branch-name
      - '{{`{{inputs.parameters.branch-name}}`}}'
      - --customer-name
      - '{{`{{inputs.parameters.customer-name}}`}}'
      - --scanner-type
      - '{{`{{inputs.parameters.scanner-type}}`}}'
      - --trigger-type
      - '{{`{{inputs.parameters.trigger-type}}`}}'
      - --integration-id
      - '{{`{{inputs.parameters.integration-id}}`}}'
      - --target-branch
      - '{{`{{inputs.parameters.target-branch}}`}}'
      - --pr-number
      - '{{`{{inputs.parameters.pr-number}}`}}'
      - --commit-hash
      - '{{`{{inputs.parameters.commit-hash}}`}}'
      - --block-pr-on-error
      - '{{`{{inputs.parameters.block-pr-on-error}}`}}'
      - --wf-start-date
      - '{{`{{inputs.parameters.wf-start-date}}`}}'
      - --scanners-plan
      - '{{`{{inputs.parameters.scanners-plan}}`}}'
      - --previous-scan-id
      - '{{`{{inputs.parameters.previous-scan-id}}`}}'
      command:
      - poetry
      - run
      - python
      - -m
      - src.entrypoint
      env:
      - name: SCANS_MANAGEMENT_URL
        value: {{.Values.deployments.scansManagement.host}}
      - name: PRE_ENRICHMENT_SCANNER_REPORTS_PR_TOPIC
        valueFrom:
          configMapKeyRef:
            key: PRE_ENRICHMENT_SCANNER_REPORTS_PR_TOPIC
            name: cas-configmap
      - name: PRE_ENRICHMENT_SCANNER_REPORTS_PERIODIC_TOPIC
        valueFrom:
          configMapKeyRef:
            key: PRE_ENRICHMENT_SCANNER_REPORTS_PERIODIC_TOPIC
            name: cas-configmap
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: scan_failure_handler
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: '{{.Values.scanFailureHandler.image.registry}}/{{.Values.scanFailureHandler.image.repository}}:{{
        .Values.scanFailureHandler.image.tag}}'
      imagePullPolicy: {{.Values.scanFailureHandler.image.pullPolicy}}
    inputs:
      parameters:
      - name: scan-id
      - name: repository-id
      - name: branch-name
      - name: customer-name
        value: empty-value
      - name: scanner-type
        value: ''
      - name: trigger-type
      - name: integration-id
      - name: target-branch
      - name: pr-number
        value: empty-value
      - name: commit-hash
      - name: block-pr-on-error
        value: 'false'
      - name: wf-start-date
      - name: scanners-plan
        value: ''
      - name: previous-scan-id
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 30.0
          - 60.0
          - 300.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: scan_failure_handler
        - key: status
          value: '{{`{{status}}`}}'
        name: job_duration_seconds
    name: scan-failure-handler
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'''
      limit: 4
    serviceAccountName: {{.Values.serviceAccount.name}}
    timeout: 6h
  workflowMetadata:
    labelsFrom:
      name:
        expression: workflow.name
      uid:
        expression: workflow.uid
