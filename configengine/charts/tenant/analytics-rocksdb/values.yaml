image: {}
envFrom: []
resources: {}
affinity: {}
tolerations: {}
nodeSelector: {}
extraVolumes: {}
extraVolumeMounts: {}
restartPolicy: ""
namespaceOverride: ""
topologySpreadConstraints: {}

revisionHistoryLimit: 0
podManagementPolicy: Parallel
automountServiceAccountToken: false
terminationGracePeriodSeconds: 3600
priorityClassName: "high-priority-deployment"

replicas: 1

configmap:
  nameOverride: ""
commonConfig: {}
config: {}

statefulset:
  updateStrategy:
    type: RollingUpdate
statefulsetAnnotations:
  reloader.stakater.com/auto: "true"
podAnnotations:
  prometheus.io/port: "8899"
  prometheus.io/scrape: "true"

env:
  GONZO_SHUTDOWN_TIMEOUT_SEC:
    value: "1h"

persistentVolumeClaimRetentionPolicy:
  whenDeleted: Retain
  whenScaled: Retain

persistence:
  enabled: true
  namespace: default
  size: 8Gi
  labels: {}
  annotations: {}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce

service:
  annotations: {}

serviceAccount:
  create: true
  automountServiceAccountToken: false
  name: rocksdb
  annotations: {}

livenessProbe: {}

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /ping
    port: 8899
    scheme: HTTP
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 36
  httpGet:
    path: /ping/
    port: 8899
    scheme: HTTP
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2

podSecurityContext:
  fsGroup: 888
  runAsNonRoot: false

vsg:
  enabled: false
  pollingMinutes: 3
  project: ""
  oomScaling: {}
  verticalScaling: {}
  zeroScaling: {}
  pvcScaling: {}

deploymentLabels:
  group: dp
  team: analytics
podLabels:
  group: dp
  team: analytics