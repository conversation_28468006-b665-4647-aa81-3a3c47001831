_component_name: "'analytics-rocksdb'"

namespaceOverride: globals.st_namespace
fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

clusterMode: infra_ff.is_enable_rocksdb_cluster_mode
blueGreenMode: infra_ff.enable_rocksdb_blue_green_mode

configmap:
  nameOverride: 'tenant.lcaas_id + "-configmap-rocksdb"'

config:
  ANALYTICS_OAKENSHIELDDB_ENV_NAME: 'infra_ff.enable_rocksdb_blue_green_mode ? "green" : nil'

commonConfig:
  ANALYTICS_OAKENSHIELDDB_REDIS_CONNECTION_STRING: 'infra_ff.enable_rocksdb_blue_green_mode || infra_ff.is_enable_rocksdb_cluster_mode ? globals.st_resource_prefix + "-analytics-redis:6379" : nil'
  ANALYTICS_OAKENSHIELDDB_BACKUP_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-rocksdb-backups"'

env:
  ANALYTICS_OAKENSHIELDDB_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'analytics_redis_password'"
        name: "globals.tenant_secrets"
        optional: false
  GONZO_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'redis_password'"
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-feature-flags"'
      optional: false
  - configMapRef:
      name: 'local.configmap.nameOverride + "-common"'
      optional: false
  - configMapRef:
      name: "local.configmap.nameOverride"
      optional: false
  - configMapRef:
      name: 'local.configmap.nameOverride + "-feature-flags"'
      optional: false

serviceAccount:
  name: '"rocksdb"'
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

_livenessProbe:
  failureThreshold: 3
  httpGet:
    path: "'/ping'"
    port: 8899
    scheme: "'HTTP'"
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

livenessProbe: "infra_ff.is_enable_rocksdb_cluster_mode ? nil : local._livenessProbe"

readinessProbe:
  failureThreshold: "infra_ff.is_enable_rocksdb_cluster_mode ? 180 : 3"
  timeoutSeconds: "infra_ff.is_enable_rocksdb_cluster_mode ? 5 : 1"

startupProbe:
  failureThreshold: "infra_ff.is_enable_rocksdb_cluster_mode ? 1500 : 36"

nodeSelector:
  xdr-pool: 'infra_ff.is_enable_rocksdb_standalone ? "rocksdb" : "wi-dynamic"'

tolerations:
  - key: 'infra_ff.is_enable_rocksdb_standalone ? "xdr-pool" : ""'
    operator: 'infra_ff.is_enable_rocksdb_standalone ? "Equal" : "Exists"'
    value: 'infra_ff.is_enable_rocksdb_standalone ? "rocksdb" : ""'

_initialDiskSize: 'tenant.is_metro_tenant ? "30Gi" : infra_ff.is_enable_rocksdb_cluster_mode ? "200Gi" : "100Gi"'

persistence:
  storageClassName: '!tenant.is_metro_tenant ? "ssd-storage" : "ssd-csi-storage"'
  size: local._initialDiskSize

resources:
  limits:
    cpu: globals.rocksdb_cpu
    memory: globals.rocksdb_memory
  requests: local.resources.limits

_vsgSingleTenantNoCluster:
  create: infra_ff.enable_pipeline
  enabled: true
  oomScaling:
    cooldownMinutes: 15
    enabled: true
    memoryStepIncrease:
      - limit: "'7Gi'"
        request: "'7Gi'"
      - limit: "'10Gi'"
        request: "'10Gi'"
      - limit: "'14Gi'"
        request: "'14Gi'"
      - limit: "'20Gi'"
        request: "'20Gi'"
      - limit: "'27Gi'"
        request: "'27Gi'"
    prometheusMetricSource:
      container: local.fullnameOverride
      threshold: 2
      windowMinutes: 60
  pvcScaling:
    enabled: true
    diskUsagePercentThreshold: 85
    diskConstantIncrease: 'tenant.is_metro_tenant ? "50Gi" : "100Gi"'
    diskCapacityMax: "'3000Gi'"
    targetEndpointMetricSource:
      port: 6677
      route: "'/disk-usage'"
  project: tenant.project_id
  targetKind: "'statefulset'"

_vsgPvcScaling:
  create: infra_ff.enable_pipeline
  enabled: true
  project: tenant.project_id
  targetKind: "'statefulset'"
  pvcScaling:
    enabled: true
    diskUsagePercentThreshold: 85
    diskConstantIncrease: 'tenant.is_metro_tenant ? "50Gi" : infra_ff.is_enable_rocksdb_cluster_mode ? "200Gi" : "100Gi"'
    diskCapacityMax: "'3000Gi'"
    targetEndpointMetricSource:
      port: 6677
      route: "'/disk-usage'"


vsg: "tenant.is_metro_tenant or infra_ff.is_enable_rocksdb_cluster_mode ? local._vsgPvcScaling : local._vsgSingleTenantNoCluster"
