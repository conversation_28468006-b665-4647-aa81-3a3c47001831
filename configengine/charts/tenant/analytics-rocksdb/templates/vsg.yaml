{{- if .Values.vsg.create }}
apiVersion: komodo.xdr.panw/v1
kind: VersatileScalingGroup
metadata:
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
  name: {{ include "chart.fullname" . }}-vsg
  namespace: {{ include "common.namespace" . }}
spec:
  enabled: {{ .Values.vsg.enabled }}
  namespace: {{ include "common.namespace" . }}
  pollingMinutes: {{ .Values.vsg.pollingMinutes }}
  project: {{ .Values.vsg.project | quote }}
  targetKind: {{ .Values.vsg.targetKind | default "deployment" }}
  targetName: {{ .Values.vsg.targetName | default (include "chart.fullname" .) }}
  {{- with .Values.vsg.oomScaling }}
  oomScaling: {{ toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.vsg.zeroScaling }}
  zeroScaling: {{ toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.vsg.verticalScaling }}
  verticalScaling: {{ toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.vsg.pvcScaling }}
  pvcScaling: {{ toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
