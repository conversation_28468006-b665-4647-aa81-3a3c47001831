apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
  name: {{ .Values.operator_name }}-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - replicasets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - autoscaling
  resources:
  - horizontalpodautoscalers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - batch
  resources:
  - cronjobs
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - cronus.xdr.panw.cronus
  resources:
  - cronusclusters
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - komodo.xdr.panw
  resources:
  - versatilescalinggroups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - komodo.xdr.panw
  resources:
  - versatilescalinggroups/finalizers
  verbs:
  - update
- apiGroups:
  - komodo.xdr.panw
  resources:
  - versatilescalinggroups/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - storybuilder.xdr.panw
  resources:
  - storybuilderclusters
  verbs:
  - get
  - list
  - patch
  - update
  - watch
