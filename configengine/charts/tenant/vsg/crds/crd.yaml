apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: versatilescalinggroups.komodo.xdr.panw
spec:
  conversion:
    strategy: Webhook
    webhook:
      clientConfig:
        service:
          name: webhook-service
          namespace: system
          path: /convert
      conversionReviewVersions:
      - v1
  group: komodo.xdr.panw
  names:
    kind: VersatileScalingGroup
    listKind: VersatileScalingGroupList
    plural: versatilescalinggroups
    shortNames:
    - vsg
    singular: versatilescalinggroup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.targetName
      name: Target
      type: string
    - jsonPath: .spec.namespace
      name: Target-Namespace
      type: string
    - jsonPath: .spec.enabled
      name: Enabled
      type: string
    - jsonPath: .status.lastScalingTime
      name: Last-Scaling-Time
      type: date
    - jsonPath: .status.lastScalingOperation
      name: Last-Scaling-Operation
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: VersatileScalingGroup is the Schema for the versatilescalinggroups
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: VersatileScalingGroupSpec defines the desired state of VersatileScalingGroup
            properties:
              container:
                description: |-
                  Deployment/StatefulSet only: Specify a container name in case more than one container exists in each pod.
                  Default behavior will be to take the first container available.
                type: string
              enabled:
                description: Specify whether the VersatileScalingGroupSpec is enabled
                  or not.
                type: boolean
              namespace:
                default: xdr-st
                description: Specify the kubernetes namespace of the target.
                minLength: 1
                type: string
              oomScaling:
                description: Specify the OOM (out of memory) scaling behavior for
                  the target.
                properties:
                  cooldownMinutes:
                    default: 15
                    description: How long to wait after a OOM scaling process has
                      been applied.
                    format: int32
                    minimum: 5
                    type: integer
                  enabled:
                    default: true
                    description: Specify whether the OOMScalingSpec is enabled or
                      not.
                    type: boolean
                  memoryConstantIncrease:
                    anyOf:
                    - type: integer
                    - type: string
                    description: The constant amount by which to increase the existing
                      defined memory resources (limits and request)
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  memoryPercentIncrease:
                    description: The percent by which to increase the existing defined
                      memory resources (limits and request)
                    format: int32
                    minimum: 1
                    type: integer
                  memoryStepIncrease:
                    description: The list of steps that will be used by the scaler
                      when deciding how much to scale up.
                    items:
                      properties:
                        limit:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        request:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                      required:
                      - limit
                      - request
                      type: object
                    minItems: 2
                    type: array
                  podRequestMemoryMax:
                    anyOf:
                    - type: integer
                    - type: string
                    description: The maximum request memory that a pod can have assigned.
                      Not relevant for MemoryStepIncrease strategy.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  prometheusMetricSource:
                    description: |-
                      Monitor prometheus metrics of [`kube_pod_container_status_restarts_total`, `kube_pod_container_status_last_terminated_reason`]
                      in order to gauge how many OOM events have occurred.
                    properties:
                      container:
                        description: |-
                          Name of the container that should be monitored. Must match the `container` label value for the metric
                          `kube_pod_container_status_restarts_total` in prometheus.
                        minLength: 1
                        type: string
                      threshold:
                        description: How many OOM events are required in the last
                          window in order to trigger an OOM scale up operation.
                        minimum: 1
                        type: integer
                      windowMinutes:
                        description: |-
                          The size of the maximum metrics window that will be used to monitor for OOM events. The scaler logic will use a window
                          size of minimum(nowTime - lastAppliedTime, windowMinutes * 60) as there is no differentiation between old/new pods
                          after an OOM scale up operation occurs and the pods are recreated.
                        maximum: 1440
                        minimum: 30
                        type: integer
                    required:
                    - container
                    - threshold
                    - windowMinutes
                    type: object
                type: object
              pollingMinutes:
                default: 15
                description: Specify the polling duration for the operator.
                format: int32
                minimum: 3
                type: integer
              project:
                description: Specify the full name of the GCP project.
                example: xdr-us-123456789
                minLength: 1
                type: string
              pvcScaling:
                description: Specify the PVC scaling behavior for the target.
                properties:
                  diskCapacityMax:
                    anyOf:
                    - type: integer
                    - type: string
                    description: The maximum disk capacity a PVC can be increased.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  diskConstantIncrease:
                    anyOf:
                    - type: integer
                    - type: string
                    description: The disk constant increase for each scaling operation.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  diskUsagePercentThreshold:
                    description: The disk usage percentage threshold should be above
                      for triggering scaling.
                    format: int32
                    maximum: 100
                    minimum: 50
                    type: integer
                  enabled:
                    default: true
                    description: Specify whether the PVCScalingSpec is enabled or
                      not.
                    type: boolean
                  targetEndpointMetricSource:
                    description: 'The endpoint exposed by the target pods to retrieve
                      disk usage metrics. (see: DiskUsageResponse)'
                    properties:
                      port:
                        description: Exposed port for metrics endpoint.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                      route:
                        description: The route path for metrics endpoint.
                        type: string
                    required:
                    - port
                    - route
                    type: object
                required:
                - diskCapacityMax
                - diskConstantIncrease
                - diskUsagePercentThreshold
                - targetEndpointMetricSource
                type: object
              targetKind:
                default: deployment
                description: Specify the kubernetes object kind to apply the versatile
                  scaling group to.
                enum:
                - deployment
                - statefulset
                - storybuildercluster
                - cronuscluster
                - cronjob
                type: string
              targetName:
                description: Specify the kubernetes target object to apply the versatile
                  scaling group to.
                minLength: 1
                type: string
              verticalScaling:
                description: Specify the vertical scaling behavior for the target.
                properties:
                  configMap:
                    description: ConfigMap on which to apply environment variable
                      changes, according to scaling step.
                    properties:
                      name:
                        type: string
                      namespace:
                        type: string
                    required:
                    - name
                    - namespace
                    type: object
                  cooldownMinutes:
                    default: 15
                    description: How long to wait after a vertical scaling process
                      has been applied.
                    format: int32
                    minimum: 5
                    type: integer
                  enabled:
                    default: true
                    type: boolean
                  gcpMetricSource:
                    description: Used to get metrics results from GCP metrics. Only
                      one source can be used as part of a configuration.
                    properties:
                      function:
                        default: max
                        description: The reduction function that will be applied to
                          the timeseries.
                        enum:
                        - max
                        - min
                        type: string
                      query:
                        description: The MQL query that will be executed. A timeseries
                          will be returned and a reduction function applied.
                        type: string
                    required:
                    - query
                    type: object
                  scaleReplicas:
                    default: false
                    description: Scale the number of replicas according to the CPU
                      request ratio between the old step and the new step.
                    type: boolean
                  steps:
                    description: |-
                      The list of steps that will be used by the scaler when deciding whether to scale up or down, and to which
                      resource configuration to scale to.
                    items:
                      properties:
                        env:
                          description: List of environment variables and their values
                            to apply for this scaling step.
                          items:
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                type: string
                            required:
                            - name
                            - value
                            type: object
                          type: array
                        resources:
                          description: The container resources configuration for this
                            scaling step.
                          properties:
                            cpu:
                              properties:
                                limit:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                request:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                              required:
                              - limit
                              - request
                              type: object
                            memory:
                              properties:
                                limit:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                request:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                              required:
                              - limit
                              - request
                              type: object
                          required:
                          - cpu
                          - memory
                          type: object
                        threshold:
                          description: The threshold configuration for this scaling
                            step. Whenever the threshold is broken, the scaling logic
                            will occur.
                          properties:
                            lower:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                If the calculated source result is lower than this float64, downscale to the previous step (if exists).
                                We use string instead of float64 due to https://github.com/kubernetes-sigs/controller-tools/issues/245.
                              x-kubernetes-int-or-string: true
                            upper:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                If the calculated source result is more than this float64, upscale to the next step (if exists).
                                We use string instead of float64 due to https://github.com/kubernetes-sigs/controller-tools/issues/245.
                              x-kubernetes-int-or-string: true
                          required:
                          - lower
                          - upper
                          type: object
                      required:
                      - resources
                      - threshold
                      type: object
                    minItems: 2
                    type: array
                  targetPropertySource:
                    description: Gets property field values from the target object.
                      Only one source can be used as part of a configuration.
                    properties:
                      property:
                        default: replicas
                        description: The property field name for the target. Currently
                          supports only "replicas".
                        enum:
                        - replicas
                        type: string
                    required:
                    - property
                    type: object
                required:
                - steps
                type: object
              zeroScaling:
                description: Specify the zero scaling behavior for the target.
                properties:
                  downscaleThreshold:
                    description: Specify the threshold that the max value of the metrics
                      timeseries (window) must be equal or less than in order to trigger
                      a downscale.
                    format: int64
                    minimum: 0
                    type: integer
                  downscaleWindowMinutes:
                    description: |-
                      Specify the window size, in minutes, of the metrics which are monitored for the downscaling operation. If the
                      current number of replicas is larger than zero, then operator will look at the metrics window and check if
                      the max value in the timeseries is equal or less than the downscale threshold.
                    format: int64
                    minimum: 1
                    type: integer
                  enabled:
                    default: true
                    description: Specify whether the ZeroScalingSpec is enabled or
                      not.
                    type: boolean
                  gcsBucketSources:
                    description: |-
                      Specify the list of Google buckets whose metrics will be monitored according to the upscale and
                      downscale window and threshold configurations.
                    items:
                      type: string
                    type: array
                  pubsubSubscriptions:
                    description: |-
                      Specify the list of Google Pubsub subscriptions whose metrics will be monitored according to the upscale and
                      downscale window and threshold configurations.
                    items:
                      type: string
                    type: array
                  replicaCutoff:
                    default: 30
                    description: |-
                      Specify the pod cutoff used as a circuit breaker for the scaler. The scaler will not trigger a downscale whenever the number of pods of the
                      target is larger than this number.
                    format: int32
                    minimum: 1
                    type: integer
                  upscaleThreshold:
                    description: Specify the threshold that the max value of the metrics
                      timeseries (window) must be equal or greater than in order to
                      trigger a upscale.
                    format: int64
                    minimum: 1
                    type: integer
                  upscaleWindowMinutes:
                    description: |-
                      Specify the window size, in minutes, of the metrics which are monitored for the upscaling operation. If the
                      current number of replicas is zero, then the operator will look at the metrics window and check if
                      the max value in the timeseries is equal or greater than the upscale threshold. Must be less than or equal to downscaleWindowMinutes.
                    format: int64
                    minimum: 1
                    type: integer
                required:
                - downscaleThreshold
                - downscaleWindowMinutes
                - upscaleThreshold
                - upscaleWindowMinutes
                type: object
            required:
            - project
            - targetName
            type: object
          status:
            description: VersatileScalingGroupStatus defines the observed state of
              VersatileScalingGroup
            properties:
              downscaledByUser:
                description: The username (other than the operator) that performed
                  a downscale on the Target.
                type: string
              lastScalingOperation:
                description: Specifies the last scaling operation applied on the Target.
                type: string
              lastScalingTime:
                description: Specifies the last time a scaling operation was applied.
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
