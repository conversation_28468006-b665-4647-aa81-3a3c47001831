apiVersion: v2
name: twistlock-defender
description: Twistlock Defender Daemon Set Helm Chart
type: application
version: 34.1.132
appVersion: "1.0"
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
annotations:
  "panw.com/deploy-eval": "region.is_fedramp"
  owner.panw/group: cortex
  owner.panw/team: devops
  owner.panw/team-slack-handle: '@cortex-devops-gv'
  owner.panw/people-slack-handle-owners-group: 'cortex-devops-us'
  owner.panw/people-slack-handle-team-lead: '@jkramer'
  owner.panw/source-code-ops-helm-chart-url: ' https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/twistlock-defender'