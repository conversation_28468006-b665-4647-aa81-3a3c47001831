groups:
  - name: kube.recordings
    rules:
    -record: kube:deployment:evicted
      expr: sum by(deployment, phase, namespace) (0 * (sum by(deployment, replicaset, namespace) (label_replace(kube_replicaset_owner{namespace!~"kube-public|kube-system|default|istio-system"}, "deployment", "$1", "owner_name", "(.*)")) + on(replicaset) group_right(deployment) sum by(pod, replicaset, namespace) (label_replace(kube_pod_owner{namespace!~"kube-public|kube-system|default|istio-system",owner_kind="ReplicaSet"}, "replicaset", "$1", "owner_name", "(.*)"))) + on(pod) group_right(deployment) (sum by(pod, phase, namespace) (kube_pod_status_phase{phase="Failed"} )))

    - record: kube:pod_memory_used:sum
      expr: sum by(container, namespace, pod) (container_memory_working_set_bytes{container!~"|POD",namespace!~"kube-public|kube-system|default|istio-system"})

    - record: kube:pod_memory_limit:sum
      expr: sum by(container, namespace, pod) (kube_pod_container_resource_limits{namespace!~ "kube-public|kube-system|default|istio-system", resource="memory", unit="byte"})

    - record: kube:pod_memory_used:ratio
      expr: (kube:pod_memory_used:sum/kube:pod_memory_limit:sum) * 100

    - record: kube:pod_cpu_limit:sum
      expr: sum by(container, namespace, pod) (kube_pod_container_resource_limits{namespace!~"kube-public|kube-system|default|istio-system",resource="cpu",unit="core"})

    - record: kube:pod_cpu_usage:sum
      expr: (sum by(container, namespace, pod) (label_replace(label_replace(rate(container_cpu_usage_seconds_total{container!~"|POD",namespace!~"kube-public|kube-system|default|istio-system"}[5m]), "container", "$1", "container_name", "(.+)"), "pod", "$1", "pod_name", "(.+)")))

    - record: kube:pod_cpu_usage:ratio
      expr: (kube:pod_cpu_usage:sum/kube:pod_cpu_limit:sum) * 100

    - record: kube:pod_restart:sum1h
      expr: sum by(container, namespace, pod) (changes(kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"}[1h]))

    - record: kube:pod_restart:sum30m
      expr: sum by(container, namespace, pod) (changes(kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"}[30m]))

    - record: kube:pod_restart:sum
      expr: sum by (container, namespace, pod) (kube_pod_container_status_restarts_total{namespace!~"kube-public|kube-system|default|istio-system"})

    - record: kube:pod_status
      expr: sum by(container, namespace, pod) (kube_pod_container_status_running{namespace!~"kube-public|kube-system|default|istio-system"})

    - record: kube:storage:pvc_usage
      expr: sum by(persistentvolumeclaim, namespace) ((kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) * 100)

    - record: kube:storage:pvc_available_prediction
      expr: predict_linear(avg by (persistentvolumeclaim, namespace) (kubelet_volume_stats_available_bytes)[1h:], 72 * 3600)

    - record: kube:pod:unscheduled
      expr: sum by(container, namespace, pod) (kube_pod_status_scheduled{namespace!~"kube-public|kube-system|default|istio-system", condition="true"}) == 0

    - record: kube:kube_job_failed:true
      expr: sum by(job_name, condition) (kube_job_failed{condition="true"})

    - record: kube:HPAStatus
      expr: sum by(hpa_type, namespace, hpa) (label_replace((kube_hpa_status_condition{condition=~"true|ScalingActive",status=~"ScalingActive|true", hpa!~"dms-hpa"}), "hpa_type", "$1", "hpa", "(?:xdr-st-)?[0-9]*-(.*)"))

    - record: kube:deployment_availability
      expr: sum by (namespace, deployment) (kube_deployment_status_replicas_available / kube_deployment_spec_replicas)

    - record: kube:kube_state_metrics:status
      expr: sum by (kubernetes_namespace, kubernetes_name, instance) (up{kubernetes_name=~"kube-state-metrics.*",kubernetes_namespace="kube-system"})

    - record: xdr_alerts_fetcher_new_ingest:sum
      expr: sum by (record_type) (xdr_alerts_fetcher_new_ingest_total)

    - record: tenant:analytics_detection_num_of_events_got_to_matcher_total:sum
      expr: sum by (component_type) (rate(analytics_detection_num_of_events_got_to_matcher_total[5m]))
    - record: tenant:analytics_detection_num_of_events_got_by_field_total:sum
      expr: sum by (component_type, field_name, field_value) (rate(analytics_detection_num_of_events_got_by_field_total[5m]))

    - record: tenant:analytics_detection_num_of_analytics_product_access_total:sum
      expr: sum by (product_type, status) (rate(analytics_detection_num_of_analytics_product_access_total[5m]))
    #- record: tenant:analytics_detection_num_of_analytics_product_access_total_highcard:sum
    #  expr: sum by (product_type, product_name, status) (rate(analytics_detection_num_of_analytics_product_access_total[5m]))

    - record: tenant:analytics_detection_num_of_hits_by_detector_id_and_field_cost_total:sum
      expr: sum by (field_cost) (rate(analytics_detection_num_of_hits_by_detector_id_and_field_cost_total[5m]))
    #- record: tenant:analytics_detection_num_of_hits_by_detector_id_and_field_cost_total_highcard:sum
    #  expr: sum by (detector_id, field_cost) (rate(analytics_detection_num_of_hits_by_detector_id_and_field_cost_total[5m]))

    - record: tenant:analytics_detection_num_of_hits_by_matcher_and_detector_id_total:sum
      expr: sum by (component_type) (rate(analytics_detection_num_of_hits_by_matcher_and_detector_id_total[5m]))
    #- record: tenant:analytics_detection_num_of_hits_by_matcher_and_detector_id_total_highcard:sum
    #  expr: sum by (component_type, detector_id) (rate(analytics_detection_num_of_hits_by_matcher_and_detector_id_total[5m]))

    - record: tenant:analytics_detection_num_of_detectors_analyzed_by_matcher_sum:sum
      expr: sum by (component_type) (rate(analytics_detection_num_of_detectors_analyzed_by_matcher_sum[5m]))
    - record: tenant:analytics_detection_num_of_detectors_analyzed_by_matcher_count:sum
      expr: sum by (component_type) (rate(analytics_detection_num_of_detectors_analyzed_by_matcher_count[5m]))

    - record: tenant:analytics_detection_profile_matcher_get_profile_from_db_time_sum:sum
      expr: sum (rate(analytics_detection_profile_matcher_get_profile_from_db_time_sum[5m]))
    - record: tenant:analytics_detection_profile_matcher_get_profile_from_db_time_count:sum
      expr: sum (rate(analytics_detection_profile_matcher_get_profile_from_db_time_count[5m]))

    - record: tenant:analytics_detection_profile_matcher_profile_result_condition_time_sum:sum
      expr: sum (rate(analytics_detection_profile_matcher_profile_result_condition_time_sum[5m]))
    - record: tenant:analytics_detection_profile_matcher_profile_result_condition_time_count:sum
      expr: sum (rate(analytics_detection_profile_matcher_profile_result_condition_time_count[5m]))

    - record: tenant:analytics_detection_profile_matcher_get_profile_result_time_sum:sum
      expr: sum (rate(analytics_detection_profile_matcher_get_profile_result_time_sum[5m]))
    - record: tenant:analytics_detection_profile_matcher_get_profile_result_time_count:sum
      expr: sum (rate(analytics_detection_profile_matcher_get_profile_result_time_count[5m]))

    - record: tenant:analytics_detection_state_populator_ingestion_rows_total:sum
      expr: sum (rate(analytics_detection_state_populator_ingestion_rows_total[5m]))
    #- record: tenant:analytics_detection_state_populator_ingestion_rows_total_highcard:sum
    #  expr: sum by (table_name) (rate(analytics_detection_state_populator_ingestion_rows_total[5m]))

    - record: tenant:analytics_detection_state_populator_ingestion_time_sum:sum
      expr: sum (rate(analytics_detection_state_populator_ingestion_time_sum[5m]))
    - record: tenant:analytics_detection_state_populator_ingestion_time_count:sum
      expr: sum (rate(analytics_detection_state_populator_ingestion_time_count[5m]))

    - record: tenant:analytics_detection_hit_publish_time_sum:sum
      expr: sum by (component) (rate(analytics_detection_hit_publish_time_sum[5m]))
    - record: tenant:analytics_detection_hit_publish_time_count:sum
      expr: sum by (component) (rate(analytics_detection_hit_publish_time_count[5m]))

    - record: tenant:analytics_detection_emitted_alerts_sum:sum
      expr: sum by (detector_type) (rate(analytics_detection_emitted_alerts_sum[5m]))
    #- record: tenant:analytics_detection_emitted_alerts_sum_highcard:sum
    #  expr: sum by (detector_type, detector_id) (rate(analytics_detection_emitted_alerts_sum[5m]))
    - record: tenant:analytics_detection_emitted_alerts_count:sum
      expr: sum by (detector_type) (rate(analytics_detection_emitted_alerts_count[5m]))
    #- record: tenant:analytics_detection_emitted_alerts_count_highcard:sum
    #  expr: sum by (detector_type, detector_id) (rate(analytics_detection_emitted_alerts_count[5m]))

    - record: tenant:analytics_detection_component_process_time_sum_by_component_type:sum
      expr: sum by (component_type) (rate(analytics_detection_component_process_time_sum[5m]))
    - record: tenant:analytics_detection_component_process_time_count:sum
      expr: sum by (component_type) (rate(analytics_detection_component_process_time_count[5m]))

    - record: tenant:analytics_detection_engine_events_processed_sum:sum
      expr: sum (rate(analytics_detection_engine_events_processed_sum[5m]))
    - record: tenant:analytics_detection_engine_events_processed_count:sum
      expr: sum (rate(analytics_detection_engine_events_processed_count[5m]))
    - record: tenant:analytics_detection_engine_consumer_nacks_total:sum
      expr: sum (rate(analytics_detection_engine_consumer_nacks_toal[5m]))

    - record: tenant:analytics_detection_udf_execution_time_sum:sum
      expr: sum (rate(analytics_detection_udf_execution_time_sum[5m]))
    #- record: tenant:analytics_detection_udf_execution_time_sum_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_udf_execution_time_sum[5m]))
    - record: tenant:analytics_detection_udf_execution_time_count:sum
      expr: sum (rate(analytics_detection_udf_execution_time_count[5m]))
    #- record: tenant:analytics_detection_udf_execution_time_count_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_udf_execution_time_count[5m]))

    - record: tenant:analytics_detection_outer_udf_execution_time_sum:sum
      expr: sum (rate(analytics_detection_outer_udf_execution_time_sum[5m]))
    #- record: tenant:analytics_detection_outer_udf_execution_time_sum_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_outer_udf_execution_time_sum[5m]))
    - record: tenant:analytics_detection_outer_udf_execution_time_count:sum
      expr: sum (rate(analytics_detection_outer_udf_execution_time_count[5m]))
    #- record: tenant:analytics_detection_outer_udf_execution_time_count_highcard:sum
    #  expr: sum by (udf_name) (rate(analytics_detection_outer_udf_execution_time_count[5m]))

    - record: tenant:analytics_detection_corrupted_events_total:sum
      expr: sum (rate(analytics_detection_corrupted_events_total[5m]))

    - record: tenant:analytics_detection_final_hit_total:sum
      expr: sum(rate(analytics_detection_final_hit_total[5m]))

    - record: tenant:analytics_unsuccessful_multi_events_enrichments_total:sum
      expr: sum (rate(analytics_unsuccessful_multi_events_enrichments_total[5m]))

    - record: tenant:analytics_dynamic_profile_updater_time_sum:sum
      expr: sum by (profile_updater_name) (rate(analytics_dynamic_profile_updater_time_sum[5m]))
    - record: tenant:analytics_dynamic_profile_updater_time_count:sum
      expr: sum by (profile_updater_name) (rate(analytics_dynamic_profile_updater_time_count[5m]))

    - record: tenant:analytics_single_event_detection_time_sum:sum
      expr: sum (rate(analytics_single_event_detection_time_sum[5m]))
    - record: tenant:analytics_single_event_detection_time_count:sum
      expr: sum (rate(analytics_single_event_detection_time_count[5m]))

    - record: tenant:analytics_de_v2__batches_processed_wall_time_sum:sum
      expr: sum(rate(analytics_de_v2__batches_processed_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2__batches_processed_wall_time_count:sum
      expr: sum(rate(analytics_de_v2__batches_processed_wall_time_count[5m]))

    - record: tenant:analytics_de_v2__events_processed_count:sum
      expr: sum(rate(analytics_de_v2__events_processed_count[5m]))
    - record: tenant:analytics_de_v2__events_processed_sum:sum
      expr: sum(rate(analytics_de_v2__events_processed_sum[5m]))
    - record: tenant:analytics_de_v2__events_processed_sum:count
      expr: count(rate(analytics_de_v2__events_processed_sum[5m]))
    - record: tenant:analytics_de_v2__events_processed_per_type_sum_by_event_type:sum
      expr: sum by (event_type) (rate(analytics_de_v2__events_processed_per_type_sum[5m]))

    - record: tenant:analytics_de_v2_detection_component_wall_time_sum_by_component_type:sum
      expr: sum by (component_type) (rate(analytics_de_v2_detection_component_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2_detection_processing_part_wall_time_sum_by_part_type:sum
      expr: sum by (part_type) (rate(analytics_de_v2_detection_processing_part_wall_time_sum[5m]))

    - record: tenant:analytics_de_v2_internal_queue_size_by_queue_name:avg
      expr: avg by (queue_name) (analytics_de_v2_internal_queue_size)

    - record: tenant:analytics_de_v2_rocks_keys_count_count_by_endpoint:sum
      expr: sum by (endpoint) (rate(analytics_de_v2_rocks_keys_count_count[5m]))
    - record: tenant:analytics_de_v2_rocks_keys_count_sum_by_endpoint:sum
      expr: sum by (endpoint) (rate(analytics_de_v2_rocks_keys_count_sum[5m]))
    - record: tenant:analytics_de_v2_rocks_request_time_count_by_endpoint:sum
      expr: sum by (endpoint) (rate(analytics_de_v2_rocks_request_time_count[5m]))
    - record: tenant:analytics_de_v2_rocks_request_time_sum_by_endpoint:sum
      expr: sum by (endpoint) (rate(analytics_de_v2_rocks_request_time_sum[5m]))

    - record: tenant:analytics_de_v2_profile_engine_api_request_time_sum_by_endpoint:sum
      expr: sum by (endpoint) (rate(analytics_de_v2_profile_engine_api_request_time_sum[5m]))

    - record: tenant:analytics_de_v2_vectorized_matcher_wall_time_sum_by_event_type:sum
      expr: sum by (event_type) (rate(analytics_de_v2_vectorized_matcher_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2_vectorized_matcher_layer_wall_time_sum_by_layer_cost:sum
      expr: sum by (layer_cost) (rate(analytics_de_v2_vectorized_matcher_layer_wall_time_sum[5m]))
    - record: tenant:analytics_de_v2_vectorized_matcher_compile_detectors_sum_by_component:avg
      expr: avg by (component) (rate(analytics_de_v2_vectorized_matcher_compile_detectors_sum[5m]))

    - record: xdr:xdr_wildfire_submit_url_res_status_total:rate1h
      expr: sum by(status_code) (rate(xdr_wildfire_submit_url_res_status_total[1h]))

    - record: instance:node_filesystem_used:ratio
      expr: sum by(instance, mountpoint) (100 - (node_filesystem_avail_bytes{device!~"rootfs"} / node_filesystem_size_bytes{device!~"rootfs"} * 100))

    - record: instance:node_memory_used:ratio
      expr: sum by (instance) (100 - ((node_memory_MemAvailable_bytes * 100) / node_memory_MemTotal_bytes))

    - record: instance:node_cpu_used:ratio
      expr: 100 - avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])* 100)

    - record: instance:proxy_services:changes1h
      expr: sum by (name, instance) (changes(node_systemd_unit_state{state="active"}[1h]))

    - record: instance:proxy_services:active
      expr: sum by (name, instance) (node_systemd_unit_state{state="active"})

    - record: tenant:xdr_matching_service_detection_queue_depth:trend
      expr: max (tenant:xdr_matching_service_detection_queue_depth:sum - tenant:xdr_matching_service_detection_queue_depth:sum offset 2m)

    - record: tenant:xdr_matching_service_detection_queue_depth:sum
      expr: sum (xdr_matching_service_detection_queue_depth)

    - record: tenant:xdr_scheduler_wlm_working:sum
      expr: sum (xdr_scheduler_wlm_working)

    - record: tenant:xdr_mailing_queue_count:sum
      expr: sum (xdr_mailing_queue_count)

    - record: tenant:xdr_alerts_loader_not_running:max
      expr: max (xdr_alerts_loader_not_running)

    - record: tenant:xdr_case_dispatch_monitor_zombie_alerts:sum
      expr: sum (xdr_case_dispatch_monitor_zombie_alerts)

    - record: tenant:xdr_missing_alerts_in_bq:sum
      expr: sum (xdr_missing_alerts_in_bq)

    - record: tenant:xdr_notifcation_mail_queue_count:sum
      expr: sum by(status) (xdr_notifcation_mail_queue_count)

    - record: tenant:analytics_product_consecutive_stage_failures:max
      expr: max by(product) (analytics_product_consecutive_stage_failures)

    - record: tenant:analytics_product_last_successful_api_table_creation_timestamp:max
      expr: max by(product) (analytics_product_last_successful_api_table_creation_timestamp)

    - record: tenant:analytics_delta_time_from_last_successful_decider_calculation:max
      expr: max by(detector) (analytics_delta_time_from_last_successful_decider_calculation)

    - record: tenant:analytics_content_delta_time_from_insertion:max
      expr: max (analytics_content_delta_time_from_insertion)

    - record: tenant:analytics_content_sync_succeeded:max
      expr: max (analytics_content_sync_succeeded)

    - record: tenant:analytics_dss_last_updated_on_last_change:max
      expr: max (analytics_dss_last_updated_on_last_change)

    - record: tenant:analytics_dss_sync_times_difference:max
      expr: max (analytics_dss_sync_times_difference)

    - record: tenant:xdr_license_fetch_failures:rate5m
      expr: sum (rate(xdr_license_fetch_failures[5m]))

    - record: tenant:xdr_case_dispatch_queue:sum
      expr: sum (xdr_case_dispatch_queue)

    - record: tenant:nginx_connections_active:sum
      expr: sum by (job) (nginx_connections_active)

    - record: tenant:docker_nginx_up
      expr: sum (nginx_connections_active)

    - record: tenant:proxy_node_exporter:status
      expr: up{job="proxy-vm"}

    - record: tenant:xdr_mysql_connection_status:sum
      expr: sum by(app, kubernetes_pod_name) (xdr_mysql_connection_status{app=~"xdr-st-.*-api"})

    - record: tenant:xdr_edr_license_count:max
      expr: max by(app, kubernetes_pod_name) (xdr_edr_license_count)

    # "endpoint" is high cardinality field that eventually needs to be limited (see zmeidav and ibieler)
    - record: tenant:xdr_request_processing_seconds_count:sum
      expr: sum by (endpoint) (xdr_request_processing_seconds_count)
    - record: tenant:xdr_request_processing_seconds_sum:sum
      expr: sum by (endpoint) (xdr_request_processing_seconds_sum)
    - record: tenant:xdr_request_response_size_count:sum
      expr: sum by (endpoint) (xdr_request_response_size_count)
    - record: tenant:xdr_request_response_size_sum:sum
      expr: sum by (endpoint) (xdr_request_response_size_sum)

    - record: xdr:xdr_redis_errors_counter_total:rate5m
      expr: sum by (app, error_code) (rate(xdr_redis_errors_counter_total[5m]))

    - record: dms_controller_integrator_events:rate:5m
      expr: sum by (method, event_type) (rate(dms_controller_integrator_events[5m]))

    - record: kube:scylla_reactor_utilization:avg
      expr: avg by (kubernetes_namespace, kubernetes_pod_name, shard) (scylla_reactor_utilization)

    - record: kube:scylla_storage_proxy_coordinator_read_latency_bucket:sum
      expr: sum by (kubernetes_namespace, le) (rate(scylla_storage_proxy_coordinator_read_latency_bucket[5m]))

    - record: kube:scylla_storage_proxy_coordinator_write_latency_bucket:sum
      expr: sum by (kubernetes_namespace, le) (rate(scylla_storage_proxy_coordinator_write_latency_bucket[5m]))

    # Very high cardinality metric, for MT going to separate long retention prometheus
    - record: tenant:xql_ingestion_raw_size_bytes:sum
      expr: sum by (vendor, product) (xql_ingestion_raw_size_bytes{product!="", vendor!=""})

    - record: tenant:partyzaurus_controller_element_status:rate:5m
      expr: sum by (status) (rate(partyzaurus_controller_element_status{stage="pz_memsql_ingester", status="CompletionTypeAcked"}[5m]))
    - record: tenant:edr_controller_element_status:rate:5m
      expr: sum by (status) (rate(edr_controller_element_status{stage="dml_memsql_edr_ingester", status="CompletionTypeAcked"}[5m]))

    - record: tenant:partyzaurus_scylla_elements:sum:5m
      expr: sum by (action, type) (rate(partyzaurus_scylla_elements[5m]))
    - record: tenant:edr_scylla_elements:sum:5m
      expr: sum by (action, type) (rate(edr_scylla_elements[5m]))
    - record: tenant:dms_scylla_elements:sum:5m
      expr: sum by (action, type) (rate(dms_scylla_elements[5m]))
    - record: tenant:storybuilder_scylla_elements:sum:5m
      expr: sum by (action, type) (rate(storybuilder_scylla_elements[5m]))

    - record: tenant:storybuilder_quantum_lag_sec:sum
      expr: sum by (kubernetes_pod_name, quantum) (storybuilder_quantum_lag_sec)

    - record: tenant:storybuilder_oversized_key_count_by_pod:sum:5m
      expr: sum by (kubernetes_pod_name) (rate(storybuilder_oversized_key_count[5m]))

    - record: tenant:storybuilder_events_count_by_pod:sum:5m
      expr: sum by (kubernetes_pod_name) (rate(storybuilder_events_count{xdr_panw_app="storybuilder"}[5m]))
    - record: tenant:storybuilder_events_count:avg:5m
      expr: avg (rate(storybuilder_events_count{xdr_panw_app="storybuilder"}[5m]))

    - record: tenant:storybuilder_stories_total_by_operation:sum:10m
      expr: sum by (operation) (rate(storybuilder_stories_total[10m]))

    - record: tenant:storybuilder_consumer_buffer_size_by_pod:sum
      expr: sum by (kubernetes_pod_name) (storybuilder_consumer_buffer_size{xdr_panw_app="storybuilder"})

    - record: tenant:storybuilder_story_write_time_sec_by_pod:avg
      expr: avg by (kubernetes_pod_name) (storybuilder_story_write_time_sec > 0)
    - record: tenant:storybuilder_story_lookup_time_sec_by_pod:avg
      expr: avg by (kubernetes_pod_name) (storybuilder_story_lookup_time_sec > 0)

    - record: tenant:storybuilder_scylla_latency_histogram_bucket:sum
      expr: sum by (le) (rate(storybuilder_scylla_latency_histogram_bucket[5m]))
    - record: tenant:storybuilder_scylla_latency_histogram_sum:sum
      expr: sum (storybuilder_scylla_latency_histogram_sum)
    - record: tenant:storybuilder_scylla_latency_histogram_count:sum
      expr: sum (storybuilder_scylla_latency_histogram_count)

    - record: tenant:storybuilder_scylla_latency_summary_sum_by_pod:sum
      expr: sum by (kubernetes_pod_name) (storybuilder_scylla_latency_summary_sum{xdr_panw_app="storybuilder"})
    - record: tenant:storybuilder_scylla_latency_summary:sum
      expr: sum (storybuilder_scylla_latency_summary)
    - record: tenant:storybuilder_scylla_latency_summary_count:sum
      expr: sum (storybuilder_scylla_latency_summary_count)

    - record: tenant:storybuilder_filtered_events_total:rate:10m
      expr: (rate(storybuilder_filtered_events_total{xdr_panw_app="storybuilder"}[10m]))

    # not clear if the rest of these storybuilder are being used yet but here just in case
    - record: tenant:storybuilder_events_in_story:sum
      expr: sum (storybuilder_events_in_story)
    - record: tenant:storybuilder_published_stories:sum
      expr: sum (storybuilder_published_stories)
    - record: tenant:storybuilder_message_processing_time_sec:sum
      expr: sum (storybuilder_message_processing_time_sec)
    - record: tenant:storybuilder_stories_in_batch:sum
      expr: sum (storybuilder_stories_in_batch)

    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_log_entry_total:sum
      expr: sum (stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_log_entry_severity_total:sum
      expr: sum by (severity) (stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count:rate5m
      expr: sum by (container_name) (rate(stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count[5m]))

    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_byte_total:sum
      expr: sum (stackdriver_k_8_s_container_logging_googleapis_com_byte_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_byte_severity_total:sum
      expr: sum by (severity) (stackdriver_k_8_s_container_logging_googleapis_com_byte_count)
    - record: sd:stackdriver_k_8_s_container_logging_googleapis_com_byte_count:rate5m
      expr: sum by (container_name) (rate(stackdriver_k_8_s_container_logging_googleapis_com_byte_count[5m]))

  - name: flux.recordings
    interval: 5m
    rules:
    - record: tenant:flux_resource_info:sum
      expr: sum by (customresource_kind, ready, suspended, exported_namespace, name, chart_name, chart_version, chart_app_version, chart_source_name, kubernetes_name, revision)(flux_resource_info)
      labels:
        workspace: "app-hub"