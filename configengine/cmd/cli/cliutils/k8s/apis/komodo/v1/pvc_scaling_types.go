package v1

import "k8s.io/apimachinery/pkg/api/resource"

type PVCScalingSpec struct {
	// Specify whether the PVCScalingSpec is enabled or not.
	// +kubebuilder:default=true
	// +optional
	Enabled bool `json:"enabled"`

	// The disk usage percentage threshold should be above for triggering scaling.
	// +kubebuilder:validation:Minimum=50
	// +kubebuilder:validation:Maximum=100
	DiskUsagePercentThreshold int32 `json:"diskUsagePercentThreshold"`

	// The disk constant increase for each scaling operation.
	DiskConstantIncrease resource.Quantity `json:"diskConstantIncrease"`

	// The maximum disk capacity a PVC can be increased.
	DiskCapacityMax resource.Quantity `json:"diskCapacityMax"`

	// The endpoint exposed by the target pods to retrieve disk usage metrics. (see: DiskUsageResponse)
	TargetEndpointMetricSource TargetEndpointMetricSource `json:"targetEndpointMetricSource"`
}

type TargetEndpointMetricSource struct {
	// Exposed port for metrics endpoint.
	// +kubebuilder:validation:Minimum=0
	// +kubebuilder:validation:Maximum=65535
	Port int32 `json:"port"`

	// The route path for metrics endpoint.
	Route string `json:"route"`
}
