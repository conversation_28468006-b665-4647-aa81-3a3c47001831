package v1

type ZeroScalingSpec struct {
	// Specify whether the ZeroScalingSpec is enabled or not.
	// +kubebuilder:default=true
	// +optional
	Enabled bool `json:"enabled"`

	// Specify the list of Google Pubsub subscriptions whose metrics will be monitored according to the upscale and
	// downscale window and threshold configurations.
	// +optional
	PubsubSubscriptions []string `json:"pubsubSubscriptions,omitempty"`

	// Specify the list of Google buckets whose metrics will be monitored according to the upscale and
	// downscale window and threshold configurations.
	// +optional
	GcsBucketSources []string `json:"gcsBucketSources,omitempty"`

	// Specify the threshold that the max value of the metrics timeseries (window) must be equal or greater than in order to trigger a upscale.
	//+kubebuilder:validation:Minimum=1
	UpscaleThreshold int64 `json:"upscaleThreshold"`

	// Specify the window size, in minutes, of the metrics which are monitored for the upscaling operation. If the
	// current number of replicas is zero, then the operator will look at the metrics window and check if
	// the max value in the timeseries is equal or greater than the upscale threshold. Must be less than or equal to downscaleWindowMinutes.
	// +kubebuilder:validation:Minimum=1
	UpscaleWindowMinutes int64 `json:"upscaleWindowMinutes"`

	// Specify the threshold that the max value of the metrics timeseries (window) must be equal or less than in order to trigger a downscale.
	// +kubebuilder:validation:Minimum=0
	DownscaleThreshold int64 `json:"downscaleThreshold"`

	// Specify the window size, in minutes, of the metrics which are monitored for the downscaling operation. If the
	// current number of replicas is larger than zero, then operator will look at the metrics window and check if
	// the max value in the timeseries is equal or less than the downscale threshold.
	// +kubebuilder:validation:Minimum=1
	DownscaleWindowMinutes int64 `json:"downscaleWindowMinutes"`

	// Specify the pod cutoff used as a circuit breaker for the scaler. The scaler will not trigger a downscale whenever the number of pods of the
	// target is larger than this number.
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:default=30
	// +optional
	ReplicaCutoff *int32 `json:"replicaCutoff,omitempty"`
}
