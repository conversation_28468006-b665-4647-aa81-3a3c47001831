/*
Copyright 2022.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// VersatileScalingGroupSpec defines the desired state of VersatileScalingGroup
type VersatileScalingGroupSpec struct {
	// Important: Run "make" to regenerate code after modifying this file

	// Specify whether the VersatileScalingGroupSpec is enabled or not.
	// +optional
	Enabled bool `json:"enabled"`

	// Specify the full name of the GCP project.
	// +kubebuilder:example=xdr-us-123456789
	// +kubebuilder:validation:MinLength=1
	Project string `json:"project"`

	// Specify the kubernetes target object to apply the versatile scaling group to.
	// +kubebuilder:validation:MinLength=1
	TargetName string `json:"targetName"`

	// Specify the kubernetes object kind to apply the versatile scaling group to.
	// +kubebuilder:validation:Enum=deployment;statefulset;storybuildercluster;cronuscluster;cronjob
	// +kubebuilder:default="deployment"
	// +optional
	TargetKind string `json:"targetKind"`

	// Specify the kubernetes namespace of the target.
	// +kubebuilder:validation:MinLength=1
	// +kubebuilder:default="xdr-st"
	// +optional
	Namespace string `json:"namespace"`

	// Deployment/StatefulSet only: Specify a container name in case more than one container exists in each pod.
	// Default behavior will be to take the first container available.
	// +optional
	Container string `json:"container,omitempty"`

	// Specify the polling duration for the operator.
	// +kubebuilder:validation:Minimum=3
	// +kubebuilder:default=15
	// +optional
	PollingMinutes int32 `json:"pollingMinutes"`

	// Specify the vertical scaling behavior for the target.
	// +optional
	VerticalScaling *VerticalScalingSpec `json:"verticalScaling,omitempty"`

	// Specify the zero scaling behavior for the target.
	// +optional
	ZeroScaling *ZeroScalingSpec `json:"zeroScaling,omitempty"`

	// Specify the OOM (out of memory) scaling behavior for the target.
	// +optional
	OOMScaling *OOMScalingSpec `json:"oomScaling,omitempty"`

	// Specify the PVC scaling behavior for the target.
	// +optional
	PVCScaling *PVCScalingSpec `json:"pvcScaling,omitempty"`
}

// VersatileScalingGroupStatus defines the observed state of VersatileScalingGroup
type VersatileScalingGroupStatus struct {
	// Important: Run "make" to regenerate code after modifying this file

	// Specifies the last time a scaling operation was applied.
	// +optional
	LastScalingTime *metav1.Time `json:"lastScalingTime,omitempty"`

	// Specifies the last scaling operation applied on the Target.
	// +optional
	LastScalingOperation string `json:"lastScalingOperation,omitempty"`

	// The username (other than the operator) that performed a downscale on the Target.
	// +optional
	DownscaledByUser string `json:"downscaledByUser,omitempty"`
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:shortName=vsg
// +kubebuilder:printcolumn:name="Target",type=string,JSONPath=`.spec.targetName`
// +kubebuilder:printcolumn:name="Target-Namespace",type=string,JSONPath=`.spec.namespace`
// +kubebuilder:printcolumn:name="Enabled",type=string,JSONPath=`.spec.enabled`
// +kubebuilder:printcolumn:name="Last-Scaling-Time",type=date,JSONPath=`.status.lastScalingTime`
// +kubebuilder:printcolumn:name="Last-Scaling-Operation",type=string,JSONPath=`.status.lastScalingOperation`

// VersatileScalingGroup is the Schema for the versatilescalinggroups API
type VersatileScalingGroup struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   VersatileScalingGroupSpec   `json:"spec,omitempty"`
	Status VersatileScalingGroupStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// VersatileScalingGroupList contains a list of VersatileScalingGroup
type VersatileScalingGroupList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []VersatileScalingGroup `json:"items"`
}

func init() {
	SchemeBuilder.Register(&VersatileScalingGroup{}, &VersatileScalingGroupList{})
}
