//go:build !ignore_autogenerated

/*
Copyright 2022.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvironmentVariable) DeepCopyInto(out *EnvironmentVariable) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvironmentVariable.
func (in *EnvironmentVariable) DeepCopy() *EnvironmentVariable {
	if in == nil {
		return nil
	}
	out := new(EnvironmentVariable)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GCPMetricSource) DeepCopyInto(out *GCPMetricSource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GCPMetricSource.
func (in *GCPMetricSource) DeepCopy() *GCPMetricSource {
	if in == nil {
		return nil
	}
	out := new(GCPMetricSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OOMScalingSpec) DeepCopyInto(out *OOMScalingSpec) {
	*out = *in
	if in.MemoryPercentIncrease != nil {
		in, out := &in.MemoryPercentIncrease, &out.MemoryPercentIncrease
		*out = new(int32)
		**out = **in
	}
	if in.MemoryConstantIncrease != nil {
		in, out := &in.MemoryConstantIncrease, &out.MemoryConstantIncrease
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.MemoryStepIncrease != nil {
		in, out := &in.MemoryStepIncrease, &out.MemoryStepIncrease
		*out = make([]OOMScalingStep, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.PodRequestMemoryMax != nil {
		in, out := &in.PodRequestMemoryMax, &out.PodRequestMemoryMax
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.PrometheusMetricSource != nil {
		in, out := &in.PrometheusMetricSource, &out.PrometheusMetricSource
		*out = new(PrometheusMetricSource)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OOMScalingSpec.
func (in *OOMScalingSpec) DeepCopy() *OOMScalingSpec {
	if in == nil {
		return nil
	}
	out := new(OOMScalingSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OOMScalingStep) DeepCopyInto(out *OOMScalingStep) {
	*out = *in
	out.Request = in.Request.DeepCopy()
	out.Limit = in.Limit.DeepCopy()
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OOMScalingStep.
func (in *OOMScalingStep) DeepCopy() *OOMScalingStep {
	if in == nil {
		return nil
	}
	out := new(OOMScalingStep)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ObjectReference) DeepCopyInto(out *ObjectReference) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ObjectReference.
func (in *ObjectReference) DeepCopy() *ObjectReference {
	if in == nil {
		return nil
	}
	out := new(ObjectReference)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PVCScalingSpec) DeepCopyInto(out *PVCScalingSpec) {
	*out = *in
	out.DiskConstantIncrease = in.DiskConstantIncrease.DeepCopy()
	out.DiskCapacityMax = in.DiskCapacityMax.DeepCopy()
	out.TargetEndpointMetricSource = in.TargetEndpointMetricSource
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PVCScalingSpec.
func (in *PVCScalingSpec) DeepCopy() *PVCScalingSpec {
	if in == nil {
		return nil
	}
	out := new(PVCScalingSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PrometheusMetricSource) DeepCopyInto(out *PrometheusMetricSource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PrometheusMetricSource.
func (in *PrometheusMetricSource) DeepCopy() *PrometheusMetricSource {
	if in == nil {
		return nil
	}
	out := new(PrometheusMetricSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RequestLimit) DeepCopyInto(out *RequestLimit) {
	*out = *in
	out.Request = in.Request.DeepCopy()
	out.Limit = in.Limit.DeepCopy()
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RequestLimit.
func (in *RequestLimit) DeepCopy() *RequestLimit {
	if in == nil {
		return nil
	}
	out := new(RequestLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TargetEndpointMetricSource) DeepCopyInto(out *TargetEndpointMetricSource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TargetEndpointMetricSource.
func (in *TargetEndpointMetricSource) DeepCopy() *TargetEndpointMetricSource {
	if in == nil {
		return nil
	}
	out := new(TargetEndpointMetricSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TargetPropertySource) DeepCopyInto(out *TargetPropertySource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TargetPropertySource.
func (in *TargetPropertySource) DeepCopy() *TargetPropertySource {
	if in == nil {
		return nil
	}
	out := new(TargetPropertySource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VersatileScalingGroup) DeepCopyInto(out *VersatileScalingGroup) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VersatileScalingGroup.
func (in *VersatileScalingGroup) DeepCopy() *VersatileScalingGroup {
	if in == nil {
		return nil
	}
	out := new(VersatileScalingGroup)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VersatileScalingGroup) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VersatileScalingGroupList) DeepCopyInto(out *VersatileScalingGroupList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]VersatileScalingGroup, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VersatileScalingGroupList.
func (in *VersatileScalingGroupList) DeepCopy() *VersatileScalingGroupList {
	if in == nil {
		return nil
	}
	out := new(VersatileScalingGroupList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VersatileScalingGroupList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VersatileScalingGroupSpec) DeepCopyInto(out *VersatileScalingGroupSpec) {
	*out = *in
	if in.VerticalScaling != nil {
		in, out := &in.VerticalScaling, &out.VerticalScaling
		*out = new(VerticalScalingSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.ZeroScaling != nil {
		in, out := &in.ZeroScaling, &out.ZeroScaling
		*out = new(ZeroScalingSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.OOMScaling != nil {
		in, out := &in.OOMScaling, &out.OOMScaling
		*out = new(OOMScalingSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.PVCScaling != nil {
		in, out := &in.PVCScaling, &out.PVCScaling
		*out = new(PVCScalingSpec)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VersatileScalingGroupSpec.
func (in *VersatileScalingGroupSpec) DeepCopy() *VersatileScalingGroupSpec {
	if in == nil {
		return nil
	}
	out := new(VersatileScalingGroupSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VersatileScalingGroupStatus) DeepCopyInto(out *VersatileScalingGroupStatus) {
	*out = *in
	if in.LastScalingTime != nil {
		in, out := &in.LastScalingTime, &out.LastScalingTime
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VersatileScalingGroupStatus.
func (in *VersatileScalingGroupStatus) DeepCopy() *VersatileScalingGroupStatus {
	if in == nil {
		return nil
	}
	out := new(VersatileScalingGroupStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VerticalScalingResource) DeepCopyInto(out *VerticalScalingResource) {
	*out = *in
	in.Cpu.DeepCopyInto(&out.Cpu)
	in.Memory.DeepCopyInto(&out.Memory)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VerticalScalingResource.
func (in *VerticalScalingResource) DeepCopy() *VerticalScalingResource {
	if in == nil {
		return nil
	}
	out := new(VerticalScalingResource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VerticalScalingSpec) DeepCopyInto(out *VerticalScalingSpec) {
	*out = *in
	if in.GCPMetricSource != nil {
		in, out := &in.GCPMetricSource, &out.GCPMetricSource
		*out = new(GCPMetricSource)
		**out = **in
	}
	if in.TargetPropertySource != nil {
		in, out := &in.TargetPropertySource, &out.TargetPropertySource
		*out = new(TargetPropertySource)
		**out = **in
	}
	if in.Steps != nil {
		in, out := &in.Steps, &out.Steps
		*out = make([]VerticalScalingStep, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ConfigMap != nil {
		in, out := &in.ConfigMap, &out.ConfigMap
		*out = new(ObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VerticalScalingSpec.
func (in *VerticalScalingSpec) DeepCopy() *VerticalScalingSpec {
	if in == nil {
		return nil
	}
	out := new(VerticalScalingSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VerticalScalingStep) DeepCopyInto(out *VerticalScalingStep) {
	*out = *in
	in.Resources.DeepCopyInto(&out.Resources)
	out.Threshold = in.Threshold
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]EnvironmentVariable, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VerticalScalingStep.
func (in *VerticalScalingStep) DeepCopy() *VerticalScalingStep {
	if in == nil {
		return nil
	}
	out := new(VerticalScalingStep)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VerticalScalingThreshold) DeepCopyInto(out *VerticalScalingThreshold) {
	*out = *in
	out.Upper = in.Upper
	out.Lower = in.Lower
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VerticalScalingThreshold.
func (in *VerticalScalingThreshold) DeepCopy() *VerticalScalingThreshold {
	if in == nil {
		return nil
	}
	out := new(VerticalScalingThreshold)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ZeroScalingSpec) DeepCopyInto(out *ZeroScalingSpec) {
	*out = *in
	if in.PubsubSubscriptions != nil {
		in, out := &in.PubsubSubscriptions, &out.PubsubSubscriptions
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.GcsBucketSources != nil {
		in, out := &in.GcsBucketSources, &out.GcsBucketSources
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ReplicaCutoff != nil {
		in, out := &in.ReplicaCutoff, &out.ReplicaCutoff
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ZeroScalingSpec.
func (in *ZeroScalingSpec) DeepCopy() *ZeroScalingSpec {
	if in == nil {
		return nil
	}
	out := new(ZeroScalingSpec)
	in.DeepCopyInto(out)
	return out
}
