---
- type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - .+/configmap/monitoring/prometheus-server-conf
    - .+/configmap/monitoring/monitoring-prom-adapter
  pathsRegex:
    - data.prometheus.rules
    - data.prometheus.yml
    - data.config.yaml
- description: Exclude all status spec
  type: ExcludeAll
  pathsRegex:
    - status.*
- description: Exclude CRDs
  type: ExcludeAll
  objectsRegex:
    - tenant/customresourcedefinition/nodeconfigs.scylla.scylladb.com
    - tenant/customresourcedefinition/scyllaclusters.scylla.scylladb.com
    - tenant/customresourcedefinition/scyllaoperatorconfigs.scylla.scylladb.com
- description: Exclude all terraform managed objects
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - .+/clusterrole(binding)*/crd-controller
    - .+/clusterrole/kube-dns.+
    - .+/clusterrolebinding/cluster-reconciler
    - .+/clusterrolebinding/kube-dns.+
    - .+/clusterrolebinding/owner-cluster-admin-binding
    - .+/configmap/xdr-st/common-confimaps-external-ips
    - .+/configmap/xdr-st/configmap-images-ids
    - .+/customresourcedefinition/.+fluxcd.io
    - .+/deployment/kube-system/kube-dns.*
    - .+/flux-system/.+
    - .+/grafana-.+
    - .+/jupyter-.+
    - .+/observability-.+
    - .+/poddisruptionbudget/kube-system/fluentd-gcp-scaler-pdb
    - .+/poddisruptionbudget/kube-system/heapster-pdb
    - .+/poddisruptionbudget/kube-system/kube-dns-autoscaler-pdb
    - .+/poddisruptionbudget/kube-system/prometheus-to-sd-pdb
    - .+/priorityclass/high-priority-deployment
    - .+/role/xdr-st/viso.+
    - .+/rolebinding/xdr-st/jobs-cleaner-role-binding
    - .+/rolebinding/xdr-st/viso.+
    - .+/secret/xdr-st/elastic-operator-webhook-cert
    - .+/serviceaccount/kube-system/kube-dns.*
    - .+/serviceaccount/xdr-st/elastic-search
    - .+/serviceaccount/xdr-st/viso.+
    - .+/storageclass/.+
    - app-hub/networkpolicy/octopus/allow-octopus-egress
    - tenant/clusterrole/xsoar-create-job-role
    - tenant/clusterrolebinding/xsoar-create-job-binding
    - tenant/deployment/xdr-st/edr-egress
    - tenant/networkpolicy/xdr-st/no-cross-talk
    - tenant/persistentvolumeclaim/xdr-st/mysql-backup-scratch
    - tenant/role/xdr-st/secrets-reader-role
    - tenant/role/xdr-st/xsoar-pod-job-creator-role
    - tenant/rolebinding/xdr-st/pb-runner-v2-scaler-rolebinding
    - tenant/rolebinding/xdr-st/secrets-creator-secrets-reader-role-binding
    - tenant/rolebinding/xdr-st/xsoar-pod-jobcreator-role-binding
- description: Allow charts new objects
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - ADDITION
  excludeElementsRegex:
    - tenant/daemonset/xdr-agent-daemon/xdr-agent-daemon
    - tenant/job/xdr-st/argo-workflows-create-db-and-user-hook
    - tenant/job/xdr-st/temporal-create-db-and-user
    - tenant/job/xdr-st/temporal-liquibase-job
    - tenant/job/xdr-st/temporal-migration
    - tenant/job/xdr-st/temporal-visibility-migration
    - tenant/role/xsoar-jobs/xsoar-pod-job-creator-role
    - tenant/rolebinding/xsoar-jobs/xsoar-pod-jobcreator-role-binding
    - tenant/secret/cas/argo-controller.service-account-token
    - tenant/secret/cas/argo-server.service-account-token
    - tenant/secret/xdr-st/elastic-operator-webhook-cert
    - tenant/secret/xdr-st/prometheus-cwp-mongodb-exporter-prometheus-mongodb-exporter
    - .+/configmap/kube-system/kube-state-metrics-config
- description: Allow charts new objects spec
  type: ExcludeAll
  objectsRegex:
    - tenant/job/xdr-st/argo-workflows-create-db-and-user-hook
    - tenant/job/xdr-st/temporal-create-db-and-user
    - tenant/job/xdr-st/temporal-liquibase-job
    - tenant/job/xdr-st/temporal-migration
    - tenant/job/xdr-st/temporal-visibility-migration
- description: Exclude all terraform orphaned objects
  type: ExcludeSliceElements
  objectsRegex: orphanedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - .+/clusterrole/flux-edit
    - .+/clusterrole/flux-view
    - .+/storageclass/.+
    - engine/configmap/xdr-st/configmap-xsoar-feature-flags-no-restart
    - engine/configmap/xdr-st/configmap-xsoar-no-restart
    - engine/configmap/xdr-st/configmap-xsoar-workers-operator-feature-flags
    - tenant/clusterrole/argo-workflows-admin
    - tenant/clusterrole/argo-workflows-edit
    - tenant/clusterrole/argo-workflows-view
    - tenant/configmap/cloudsec/cloudsec-vuln-ap-configmap
    - tenant/configmap/cortex-cts/cts-configmaps-terraform-exports
    - tenant/configmap/xdr-st/cts-configmaps-terraform-exports
    - tenant/configmap/xdr-st/common-configmaps-terraform-exports
    - tenant/configmap/xdr-st/common-confimaps-external-ips
    - tenant/configmap/xdr-st/configmap-cortex-platform-no-restart
    - tenant/configmap/xdr-st/configmap-dml-feature-flags
    - tenant/configmap/xdr-st/configmap-images-ids
    - tenant/configmap/xdr-st/configmap-metrics-aggregator
    - tenant/configmap/xdr-st/configmap-rocksdb
    - tenant/configmap/xdr-st/configmap-rocksdb-common
    - tenant/configmap/xdr-st/configmap-rocksdb-feature-flags
    - tenant/configmap/xdr-st/configmap-rocksdb-writer
    - tenant/configmap/xdr-st/configmap-rocksdb-writer-feature-flags
    - tenant/configmap/xdr-st/configmap-scortex-deployment
    - tenant/configmap/xdr-st/configmap-uvem
    - tenant/configmap/xdr-st/configmap-agentix-hub
    - tenant/configmap/xdr-st/configmap-xql
    - tenant/configmap/xdr-st/configmap-xql-feature-flags
    - tenant/configmap/xdr-st/configmap-xsoar-feature-flags
    - tenant/configmap/xdr-st/configmap-xsoar-feature-flags-no-restart
    - tenant/configmap/xdr-st/common-confimaps-external-ips
    - tenant/configmap/xdr-st/configmap-xcloud
    - tenant/configmap/xdr-st/cwp-configmaps-terraform-exports
    - tenant/persistentvolumeclaim/xdr-st/mysql-backup-scratch
    - tenant/serviceaccount/xdr-st/dbre-operator
    - tenant/serviceaccount/xdr-st/elastic-search
    - tenant/serviceaccount/xdr-st/sb-controller-manager
    - tenant/serviceaccount/xdr-st/storybuilder
    - tenant/serviceaccount/xdr-st/viso-playbook-executor
    - tenant/serviceaccount/xsoar-jobs/xsoar-job-pod
- description: Allow new annotations and labels on metadata
  type: ExcludeMapKeys
  pathsRegex: metadata
  kinds:
    - ADDITION
  excludekeys:
    - annotations
    - labels
- description: Exclude cert-manager inject of caBundle to webhooks
  type: ExcludeMapKeys
  pathsRegex:
    - webhooks.*.clientConfig
    - spec.conversion.webhook.clientConfig
  kinds:
    - REMOVAL
  excludekeys:
    - caBundle
- description: Exclude job template metadata removal
  type: ExcludeMapKeys
  pathsRegex:
    - spec.jobTemplate.metadata
  kinds:
    - REMOVAL
  excludekeys:
    - annotations
- description: Exclude prometheus spec.template annotations removal
  type: ExcludeMapKeys
  pathsRegex: spec.template.metadata
  kinds:
    - REMOVAL
  excludekeys:
    - annotations
- description: Exclude spec.template annotations
  type: ExcludeMapKeys
  pathsRegex: spec.template.metadata.annotations
  kinds:
    - REMOVAL
  excludekeys:
    - kubectl.kubernetes.io/restartedAt
    - reloader.stakater.com/last-reloaded-from
    - viso/restartedAt
    - k8slens-edit-resource-version
- description: Exclude autogenerated storage annotation
  type: ExcludeMapKeys
  pathsRegex: metadata.annotations
  kinds:
    - REMOVAL
  excludekeys:
    - volume.kubernetes.io/storage-resizer
- description: Exclude podSpec default spec
  type: ExcludeMapKeys
  pathsRegex:
    - spec.template.spec
    - spec.jobTemplate.spec.template.spec
  kinds:
    - REMOVAL
  excludekeys:
    - enableServiceLinks
    - shareProcessNamespace
- description: Exclude jobTemplate default spec
  type: ExcludeMapKeys
  pathsRegex: spec.jobTemplate.spec
  kinds:
    - REMOVAL
  excludekeys:
    - manualSelector
- description: Allow new labels or annotations on metadata
  type: ExcludeMapKeys
  pathsRegex: metadata
  kinds:
    - ADDITION
  excludekeys:
    - labels
    - annotations
- description: Allow new common labels on metadata
  type: ExcludeMapKeys
  pathsRegex:
    - metadata.labels
  kinds:
    - ADDITION
  excludekeys:
    - app
    - app.kubernetes.io/name
    - app.kubernetes.io/instance
- description: Allow new annotations on metadata
  type: ExcludeMapKeys
  pathsRegex: metadata.annotations
  kinds:
    - ADDITION
  excludekeys:
    - helm.sh/resource-policy
    - checksum/resources
- description: Exclude immutable on configmaps
  type: ExcludeMapKeys
  objectsRegex: .+/configmap/.+
  pathsRegex: ""
  kinds:
    - REMOVAL
  excludekeys:
    - immutable
- description: Exclude elastic default spec
  type: ExcludeMapKeys
  objectsRegex: tenant/elasticsearch/xdr-st/elastic
  pathsRegex:
    - spec
    - spec.nodeSets.xsoar
  kinds:
    - REMOVAL
  excludekeys:
    - auth
    - http
    - monitoring
    - podTemplate
    - transport
    - updateStrategy
- description: Exclude daemonset generation annotation
  type: ExcludeAll
  pathsRegex: metadata.annotations.deprecated.daemonset.template.generation
- description: Exclude old service account secrets
  type: ExcludeMapKeys
  objectsRegex: .+/serviceaccount/.+
  pathsRegex: ""
  kinds:
    - REMOVAL
  excludekeys:
    - secrets
- description: Exclude argo workflows service account token
  type: ExcludeAll
  objectsRegex:
    - tenant/secret/cas/argo-server.service-account-token
    - tenant/secret/cas/argo-controller.service-account-token
- description: Exclude cronus cluster members
  type: ExcludeMapKeys
  objectsRegex: .+/cronuscluster/.+
  pathsRegex: spec.deployment
  kinds:
    - REMOVAL
  excludekeys:
    - members
- description: Exclude storybuilder autoscaler annotations
  type: ExcludeMapKeys
  pathsRegex: metadata.annotations
  kinds:
    - REMOVAL
  excludekeys:
    - xdr.panw.storybuilder/autoscale-disabled
    - xdr.panw.storybuilder/autoscale-last-disabled
    - xdr.panw.storybuilder/autoscale-enabling-count
- description: Exclude ArgoWorkflows CronWorkflow annotations
  type: ExcludeMapKeys
  pathsRegex: metadata.annotations
  kinds:
    - REMOVAL
  excludekeys:
    - cronworkflows.argoproj.io/last-used-schedule
- description: Exclude default feature-flags
  type: ExcludeMapKeys
  objectsRegex: .+/configmap/.+feature-flags.*
  pathsRegex: data
  kinds:
    - ADDITION
  excludekeys:
    - IS_USING_GOOGLE_ARTIFACT_REGISTRY
    - init
    - CONTENTCONF_BW_LIMIT_OPTIMIZATION_FF_ENABLED
    - XqlGlobalConfDefault_case_sensitive
- description: Exclude secrets feature-flags
  type: ExcludeMapKeys
  objectsRegex: .+/configmap/.+feature-flags.*
  pathsRegex: data
  kinds:
    - REMOVAL
  excludekeys:
    - WILDFIRE_APIKEY
    - PANDBCONF_API_KEY
    - PANDBConf_api_key
- description: Exclude custom-metrics-stackdriver-adapter labels
  type: ExcludeMapKeys
  objectsRegex: .+/service/custom-metrics/custom-metrics-stackdriver-adapter
  pathsRegex: metadata.labels
  kinds:
    - REMOVAL
  excludekeys:
    - kubernetes.io/cluster-service
    - kubernetes.io/name
- description: Exclude scylla crd version annotation
  type: ExcludeAll
  objectsRegex: tenant/customresourcedefinition/scyllaclusters.scylla.scylladb.com
  pathsRegex: metadata.annotations.controller-gen.kubebuilder.io/version
  kinds:
    - MODIFICATION
- description: Exclude stackdriver automountServiceAccountToken addition
  type: ExcludeMapKeys
  objectsRegex: tenant/serviceaccount/custom-metrics/custom-metrics-stackdriver-adapter
  pathsRegex: ""
  kinds:
    - ADDITION
  excludekeys:
    - automountServiceAccountToken
- description: Exclude otel automountServiceAccountToken removal
  type: ExcludeMapKeys
  objectsRegex: tenant/serviceaccount/monitoring/otel-collector-agentless
  kinds:
    - REMOVAL
  excludekeys:
    - automountServiceAccountToken
- description: Exclude XSOAR jobs configs (was added after 3.13 batch4)
  type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - tenant/configmap/xdr-st/configmap-xsoar-ng
  pathsRegex:
    - data.EXDELETE_INCIDENTS_JOB_SERVICE_ACCOUNT
    - data.EXDELETE_INCIDENTS_JOB_XSOAR_COMMAND
    - data.EXDELETE_INCIDENTS_JOB_XSOAR_IMAGE
    - data.KUBERNETES_JOBS_RUNTIME_NAMESPACE
    - data.NO_PROXY
    - data.XSOARCONF_API_HOST
- description: Exclude no proxy (was modified after 3.13 batch4)
  type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - tenant/configmap/xdr-st/configmap
  pathsRegex:
    - data.no_proxy
    - data.CLOUDONBOARDING_MT_KMS_ACCOUNT
    - data.SCYLLA_NODE_COUNT
    - data.ANALYTICS_OAKENSHIELDDB_MEMORY
    - data.DSSCONF_SYNC_WITH_AD
- description: Allow configmap-xsoar-ng/configmap/configmap-xsoar-content keys addition (was modified after 3.13 batch4)
  type: ExcludeMapKeys
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/configmap/xdr-st/configmap-xsoar-ng
    - tenant/configmap/xdr-st/configmap
    - tenant/configmap/xdr-st/configmap-xsoar-content
  pathsRegex:
    - data
  excludekeys:
    - XSOAR_API_SERVICE_HOST
    - GENERIC_PRODUCT_CODE
- description: Exclude chrome-app-cron-job modification
  type: ExcludeAll
  objectsRegex: tenant/cronjob/xdr-st/chrome-app-cron-job
  pathsRegex:
    - spec.jobTemplate.spec.template.spec.containers.chrome-app.resources.limits.*
  kinds:
    - MODIFICATION
- description: Exclude chrome-app-cron-job modification
  type: ExcludeAll
  objectsRegex: tenant/cronjob/xdr-st/chrome-app-cron-job
  pathsRegex:
    - spec.jobTemplate.spec.template.spec.containers.chrome-app
  excludekeys:
    - readinessProbe
  kinds:
    - REMOVAL
- description: Exclude dp-singlestore-cluster schedulingDetails annotations and labels
  type: ExcludeMapKeys
  objectsRegex: tenant/memsqlcluster/xdr-st/dp-singlestore-cluster
  pathRegex: spec.schedulingDetails.objectMetaOverrides
  kinds:
    - ADDITION
  excludekeys:
    - annotations
    - labels
- enabled: "{{ not .infra_ff.enable_new_scylla }}"
  description: "Exclude scylla v4 objects"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex: .+/.*scylla.*
- description: Allow batch changes post upgrade 3.13
  type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - tenant/configmap/xdr-st/configmap-xsoar-ng
    - tenant/configmap/xdr-st/configmap
    - tenant/configmap/xdr-st/configmap-xsoar-content
  pathsRegex:
    - data.GENERIC_TENANT_DEPLOYMENT_BATCH
- description: Ignore addition of CM key before migration
  type: ExcludeMapKeys
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/configmap/xdr-st/configmap-feature-flags
  pathsRegex:
    - data
  excludekeys:
    - ALPHAFEATURES_PR_CLOUD_COMMAND_CENTER
- description: Ignore addition of CM key before migration
  type: ExcludeMapKeys
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/configmap/xdr-st/configmap-frontend-feature-flags-no-restart
  pathsRegex:
    - data
- description: Allow d1.conf keys addition (was modified after 3.13 batch4)
  type: RuleExcludeMultiline
  kinds:
    - MODIFICATION
  objectsRegex: engine/configmap/xdr-st/engine0-cm
  pathsRegex: data.d1.conf
  excludeInsertLinesRegex:
    - "[\\t\\s]+\"GENERIC_PRODUCT_CODE\": \".+\",\n"
- description: Exclude any pb-runner and pb-runner-priority HPA diff
  type: ExcludeAll
  objectsRegex:
    - tenant/horizontalpodautoscaler/xdr-st/pb-runner-[^v2].+
- description: ignore loader replicas due to migration manipulation
  type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - .+/deployment/xdr-st/reloader
    - .+/deployment/ops/reloader
  pathsRegex:
    - spec.replicas
- descrpition: Ignore replicas when hpa enabled
  type: ExcludeAll
  objectsRegex:
    - tenant/deployment/cas/argo-workflows-server
    - tenant/deployment/xdr-st/analytics-alerts-emitter
    - tenant/deployment/xdr-st/analytics-detection-engine
    - tenant/deployment/xdr-st/analytics-detection-engine-external-data
    - tenant/deployment/xdr-st/api
    - tenant/deployment/xdr-st/api-be
    - tenant/deployment/xdr-st/cwp-compliance-publisher
    - tenant/deployment/xdr-st/cwp-core-asset-analyzer
    - tenant/deployment/xdr-st/dp-uai-assets
    - tenant/deployment/xdr-st/dp-uai-findings
    - tenant/deployment/xdr-st/fetcher
    - tenant/deployment/xdr-st/frontend
    - tenant/deployment/xdr-st/metrics
    - tenant/deployment/xdr-st/notifier
    - tenant/deployment/xdr-st/pb-runner-v2
    - tenant/deployment/xdr-st/pipeline
    - tenant/deployment/xdr-st/risk-score-cie-processor
    - tenant/deployment/xdr-st/task-processor
    - tenant/deployment/xdr-st/uvem-vip-api
    - tenant/deployment/xdr-st/log-processor
    - tenant/deployment/xdr-st/scortex
    - tenant/deployment/xdr-st/dms
    - tenant/deployment/xdr-st/pipeline
  kinds:
    - MODIFICATION
  pathsRegex: spec.replicas
- description: removal rule for cronuscluster annotations (used for xdr.panw.cronus/suspend)
  type: ExcludeMapKeys
  kinds:
    - REMOVAL
  objectsRegex:
    - tenant/cronuscluster/xdr-st/ipl-cronus-node
    - tenant/cronuscluster/metrus/ipl-metrus-node
  pathsRegex:
    - metadata
  excludekeys:
    - annotations
- description: pithoscluster annotations ignore.
  type: ExcludeMapKeys
  kinds:
    - REMOVAL
  objectsRegex:
    - tenant/pithoscluster/pithos/ipl-pithos-node
  pathsRegex:
    - metadata.annotations
  excludekeys:
    - cluster-autoscaler.kubernetes.io/safe-to-evict
- description: Exclude elastic terraform orphaned object
  enabled: "{{ not .infra_ff.enable_xsoar_shared_components }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/customresourcedefinition/agents.agent.k8s.elastic.co
    - tenant/customresourcedefinition/apmservers.apm.k8s.elastic.co
    - tenant/customresourcedefinition/beats.beat.k8s.elastic.co
    - tenant/customresourcedefinition/elasticmapsservers.maps.k8s.elastic.co
    - tenant/customresourcedefinition/elasticsearchautoscalers.autoscaling.k8s.elastic.co
    - tenant/customresourcedefinition/elasticsearches.elasticsearch.k8s.elastic.co
    - tenant/customresourcedefinition/enterprisesearches.enterprisesearch.k8s.elastic.co
    - tenant/customresourcedefinition/kibanas.kibana.k8s.elastic.co
    - tenant/customresourcedefinition/logstashes.logstash.k8s.elastic.co
    - tenant/customresourcedefinition/stackconfigpolicies.stackconfigpolicy.k8s.elastic.co
- description: Ignore storybuilder when not enabled
  enabled: "{{ not (and .infra_ff.enable_pipeline (not .tenant.is_metro_tenant)) }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/issuer/xdr-st/sb-operator-selfsigned-issuer
    - tenant/certificate/xdr-st/sb-operator-serving-cert
- description: Ignore metro tenant orphanedObjects
  enabled: "{{ .tenant.is_metro_tenant }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/serviceaccount/xdr-st/cronus-operator-controller-manager
    - tenant/rolebinding/xdr-st/cronus-operator-proxy
    - tenant/rolebinding/xdr-st/cronus-operator-manager
    - tenant/role/xdr-st/cronus-operator-proxy
    - tenant/role/xdr-st/cronus-operator-manager
- description: Ignore XDR agent orphanedObjects
  type: ExcludeSliceElements
  objectsRegex: orphanedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - .+/priorityclass/xdr-agent-priority
- description: Ignore XDR agent managedObjects
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - .+/clusterrole/xdr-agent-role
    - .+/clusterrolebinding/xdr-agent-role-binding
    - .+/serviceaccount/xdr-agent-daemon/xdr-agent-user
- description: Exclude metro cluster wide resources
  enabled: "{{ .tenant.is_metro_tenant }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - ADDITION
  excludeElementsRegex:
    - tenant/clusterrolebinding/storybuilders-rolebinding
- description: Allow xdr-agent resources for non P4 tenants
  enabled: '{{ not (eq .tenant.upgrade_phase "P4") }}'
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - ADDITION
  excludeElementsRegex:
    - .+/clusterrole/xdr-agent-role
    - .+/clusterrolebinding/xdr-agent-role-binding
    - .+/daemonset/xdr-agent-daemon/xdr-agent-daemon
    - .+/serviceaccount/xdr-agent-daemon/xdr-agent-user
- description: Exclude storybuilder license downgrade leftovers
  enabled: "{{ not .infra_ff.enable_pipeline }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/clusterrole/storybuilders-role
    - tenant/clusterrolebinding/storybuilders-rolebinding
    - tenant/mutatingwebhookconfiguration/sb-operator-mutating-webhook-configuration
    - tenant/service/xdr-st/sb-operator-webhook-service
    - tenant/serviceaccount/xdr-st/storybuilder
- description: Exclude xsoar license downgrade leftovers
  enabled: "{{ not .infra_ff.enable_xsoar_shared_components }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/role/xsoar-jobs/xsoar-pod-job-creator-role
    - tenant/rolebinding/xsoar-jobs/xsoar-pod-jobcreator-role-binding
- description: Exclude scylla license downgrade leftovers
  enabled: "{{ not .infra_ff.enable_scylla }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/customresourcedefinition/nodeconfigs.scylla.scylladb.com
    - tenant/customresourcedefinition/scyllaclusters.scylla.scylladb.com
    - tenant/customresourcedefinition/scyllaoperatorconfigs.scylla.scylladb.com
- description: Exclude gonzo upgrade to dragonfly leftovers
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/persistentvolumeclaim/xdr-st/gonzo-redis-volume
- description: Exclude xsoar-workers-gateway ports removal
  type: ExcludeSliceElements
  kinds:
    - REMOVAL
  objectsRegex:
    - tenant/deployment/xdr-st/xsoar-workers-gateway
  excludeElements:
    - containerPort: 9090
      protocol: TCP
- description: Ignore everything related to kube-state-metrics
  type: ExcludeAll
  kinds:
    - REMOVAL
    - ADDITION
    - MODIFICATION
  objectsRegex:
    - .+/configmap/kube-system/kube-state-metrics-config
    - .+/deployment/kube-system/kube-state-metrics
    - .+/clusterrole/kube-state-metrics
- description: Ignore everything related to kube-state-metrics
  type: ExcludeAll
  kinds:
    - REMOVAL
    - ADDITION
    - MODIFICATION
  objectsRegex:
    - .+/configmap/kube-system/kube-state-metrics-config
    - .+/deployment/kube-system/kube-state-metrics
    - .+/clusterrole/kube-state-metrics
- description: Exclude mysql uvem pvc
  enabled: "{{ not .infra_ff.enable_mysql_uvem }}"
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/persistentvolumeclaim/xdr-st/mysql-volume-uvem
- description: Exclude chart labels
  type: ExcludeAll
  kinds:
    - MODIFICATION
  pathsRegex:
    - metadata.labels.helm.sh/chart
    - spec.template.metadata.annotations.checksum/config
    - spec.template.metadata.labels.helm.sh/chart
- description: Exclue temporal / mongodb exporter config chechsum
  type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - tenant/deployment/xdr-st/temporal.*
    - tenant/deployment/xdr-st/prometheus-cwp-mongodb-exporter-prometheus-mongodb-exporter
  pathsRegex:
    - metadata.labels.helm.sh/chart
    - spec.template.metadata.annotations.checksum/config
    - spec.template.metadata.labels.helm.sh/chart
- description: Exclude neo4j HELM_CHART_VERSION env
  type: ExcludeAll
  kinds:
    - MODIFICATION
  objectsRegex:
    - tenant/statefulset/xdr-st/dp-neo4j-cluster
  pathsRegex:
    - spec.template.spec.containers.neo4j.env.HELM_CHART_VERSION.value
- description: Ignore diff due to CAS chart version in Terraform hasn't been changed but template has
  type: ExcludeMapKeys
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/workflowtemplate/cas/enry-scanner-template
  pathsRegex: spec.templates.report-assets.inputs.artifacts.scan-output-assets
  excludekeys:
    - archive
- description: Ignore diff due to CAS chart version in Terraform hasn't been changed but template has
  type: ExcludeMapKeys
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/cronworkflow/cas/periodic-customer-scan
    - tenant/workflowtemplate/cas/iac-scanner-template
  pathsRegex: spec
  excludekeys:
    - concurrencyPolicy
- description: Ignore diff due to CAS chart version in Terraform hasn't been changed but template has
  type: ExcludeSliceElements
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/deployment/cas/source-control
    - tenant/deployment/cas/webhook
    - tenant/deployment/cas/vcs-ci-fetcher
  pathsRegex: spec.template.spec.containers.source-control-.+-svc.env
  excludeElements:
    - name: "RUN_ENV"
- description: Ignore diff due to CAS chart version in Terraform hasn't been changed but template has
  type: ExcludeMapKeys
  kinds:
    - ADDITION
  objectsRegex:
    - tenant/workflowtemplate/cas/iac-scanner-template
    - tenant/workflowtemplate/cas/thirdparty-scanner-template
    - tenant/workflowtemplate/cas/iac-remediations-workflow
    - tenant/workflowtemplate/cas/scanner-blueprint-workflow-template
    - tenant/workflowtemplate/cas/remediations-workflow
  pathsRegex: spec.templates..+.retryStrategy
  excludekeys:
    - expression
- description: Ignore diff due to CAS chart version in Terraform hasn't been changed but template has
  type: ExcludeMapKeys
  kinds:
    - REMOVAL
  objectsRegex:
    - tenant/workflowtemplate/cas/iac-scanner-template
    - tenant/workflowtemplate/cas/thirdparty-scanner-template
    - tenant/workflowtemplate/cas/iac-remediations-workflow
    - tenant/workflowtemplate/cas/scanner-blueprint-workflow-template
    - tenant/workflowtemplate/cas/remediations-workflow
  pathsRegex: spec.templates..+.retryStrategy
  excludekeys:
    - retryPolicy
- description: Ignore certificaterequest removal
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/certificaterequest/xdr-st/sb-operator-serving-cert-13
    - tenant/certificaterequest/xdr-st/vsg-serving-cert-10
- description: Ignore certificaterequest removal
  type: ExcludeSliceElements
  objectsRegex: managedObjects
  kinds:
    - REMOVAL
  excludeElementsRegex:
    - tenant/certificaterequest/xdr-st/sb-operator-serving-cert-13
    - tenant/certificaterequest/xdr-st/vsg-serving-cert-10
- description: Exclude storybuilder annotations
  type: ExcludeMapKeys
  pathsRegex: metadata.annotations
  kinds:
    - REMOVAL
  excludekeys:
    - foo
- description: Exclude monitoring-prometheus annotations
  type: ExcludeMapKeys
  pathsRegex: metadata.annotations
  kinds:
    - REMOVAL
  excludekeys:
    - testfake
- description: Exclude updateStrategy changes in xdr-agent-daemon
  kinds:
    - MODIFICATION
  type: ExcludeAll
  objectsRegex:
    - tenant/daemonset/xdr-agent-daemon/xdr-agent-daemon
  pathsRegex: spec.updateStrategy.rollingUpdate.maxUnavailable
- description: Exclude storybuilder tolerations
  kinds:
    - REMOVAL
  type: ExcludeMapKeys
  objectsRegex:
    - tenant/deployment/xdr-st/ipl-controller-manager
    - tenant/storybuildercluster/xdr-st/storybuilders
  pathsRegex:
    - spec
    - spec.template.spec
  excludekeys:
    - tolerations
- description: Exclude hpa metrics spec
  kinds:
    - ADDITION
    - REMOVAL
  type: ExcludeAll
  objectsRegex:
    - tenant/horizontalpodautoscaler/xdr-st/dms-hpa
    - tenant/horizontalpodautoscaler/xdr-st/pipeline-hpa
    - tenant/horizontalpodautoscaler/xdr-st/pz-schema-manager-hpa
    - tenant/horizontalpodautoscaler/xdr-st/xql-engine-hpa
  pathsRegex:
    - spec.metrics
- description: Exclude all containers env order change
  type: ExcludeAll
  kinds:
    - ORDERCHANGE
  objectsRegex:
    - .+/cronjob/.+
    - .+/deployment/.+
    - .+/cronuscluster/.+
    - .+/storybuildercluster/.+
  pathsRegex:
    - spec.env
    - spec.cronusConfig.envConfig
    - spec.template.spec.containers.*.env
    - spec.jobTemplate.spec.template.spec.containers.*.env
