include:
  - project: shared-services/ci-templates
    ref: master
    file: output/security/checkmarx.yaml
  - project: shared-services/gitlab-ci-templates
    ref: master
    file: templates/Jobs/Python/version_compare.yaml
  - project: shared-services/gitlab-ci-templates
    ref: master
    file: templates/Jobs/Jenkins/branch-auto-merge.yml
  - local: "configengine/.gitlab-ci.yml"
  - local: "tf/terraform.gitlab-ci.yml"

stages:
  - pre-checks
  - build-ci-agent
  - trigger
  - automerge
  - push-charts
  - push-charts-after-merge

push-charts-on-master:
  stage: push-charts-after-merge
  allow_failure: true
  image: europe-west4-docker.pkg.dev/xdr-shared-services-prod-eu-01/viso/platformx-go-ci:v1.22.1
  script:
    - env
    - |
      #!/bin/bash 
        echo "Pushing charts"
        REGISTRY=oci://us-docker.pkg.dev/xdr-registry-dev-01/cortex-helm-charts/helm-charts
        cd configengine/cmd/cli
        go build -o cecli main.go && chmod +x cecli
        ./cecli helm push --all --charts /tmp/charts --registry ${REGISTRY}
        ./cecli helm push --all --charts /tmp/charts --registry "${REGISTRY}/master"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^(dev|master-\d+\.\d+.*)$/'
      when: manual
    - if: '$CI_COMMIT_BRANCH =~ /^master-\d+\.\d+.*$/'
      when: always
      changes:
        - configengine/charts/**/*
    - if: '$PUSH_CHART_MASTER == "true" && $CI_COMMIT_BRANCH =~ /^master-\d+\.\d+.*$/'
      when: always

push-charts-on-dev:
  stage: push-charts-after-merge
  image: europe-west4-docker.pkg.dev/xdr-shared-services-prod-eu-01/viso/platformx-go-ci:v1.22.1
  script:
    - env
    - |
      #!/bin/bash 
        echo "Pushing charts"
        cd configengine/cmd/cli
        go build -o cecli main.go && chmod +x cecli
        ./cecli helm push --all --charts /tmp/charts
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: always
      changes:
        - configengine/charts/**/*

# dummy-job-exit-true:
#   stage: dummy-job
#   image: alpine/git
#   script:
#     - exit 0

blacklist-verification-checks:
  stage: pre-checks
  image: python:3.9-slim
  allow_failure: false
  variables:
    image_user: root
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
    artifacts_path: blacklist-results.xml
  script:
    - python3 -m pip install --upgrade pip
    - python -m pip install -r ci/tools/blacklist-checker/requirements.txt
    - python ci/tools/blacklist-checker/blacklist_checker.py --junit $artifacts_path
  artifacts:
    when: always
    reports:
      junit: $artifacts_path
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/


recording-rules-tenant-exclusion-check:
  stage: pre-checks
  image:  python:3.11-slim
  allow_failure: true
  script:
    - python3 -m pip install -r ci/tools/recording-rule-tenant-exclusions/requirements.txt
    - python3 ci/tools/recording-rule-tenant-exclusions/recording_rule_tenant_exclusion.py
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/
  artifacts:
    reports:
      junit: Recording_rule_tenant_exclusion.xml



promeheus-load-rules-check:
  stage: pre-checks
  image:  prom/prometheus
  allow_failure: true
  script:
    - python3 -m pip install -r ci/tools/recording-rule-tenant-exclusions/requirements.txt
    - python3 
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/


pint-checks:
  stage: pre-checks
  image: us-docker.pkg.dev/xdr-registry-prod-us-01/golden-images/pint:0.71.8
  allow_failure: false
  script:
    - pint --offline -c ci/tools/pint-confg/pint-config.hcl lint configengine/charts/**/ -w Fatal
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/


recroding-rules-duration-checks:
  stage: pre-checks
  image:  python:3.11-slim
  allow_failure: true
  script:
    - python3 -m pip install pyyaml
    - pwd
    - python3 ci/tools/prometheus_rule_validations.py
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/

prometheus-yaml-validations:
  stage: pre-checks
  image:  python:3.11-slim
  allow_failure: false
  script:
    - python3 -m pip install pyyaml
    - pwd
    - python3 ci/tools/yaml-verifier/yaml_validation.py
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/

version_compare:
  stage: pre-checks
  tags:
    - gcp-configuration-5
  variables:
    CI_API_READ_TOKEN: ${GITLAB_CI_TEMPLATES_READ_API}
  extends: .version_compare


checkmarx:
  stage: pre-checks
  variables:
    ProjectName: "CxServer\\SP\\Company\\CortexXDR\\$CI_PROJECT_NAME"
    ALLOW_FAILURE_CHECKMARX: "true"
  allow_failure: true
  tags:
    - gcp-configuration-5
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/
    - changes:
        - tf/**

gather-versions:
  stage: pre-checks
  image: alpine/git
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: "0"
  script:
    - echo COMMIT_OR_TARGET_BRANCH=$COMMIT_OR_TARGET_BRANCH
    - BASE_TAG="$(git describe --abbrev=0 $CI_COMMIT_SHA)"
    - BASE_BRANCH="$(echo $COMMIT_OR_TARGET_BRANCH | sed -re 's/^(master|dev)(-[0-9]+\.[0-9]+)?$/\1/')"
    - echo BASE_TAG="$BASE_TAG" | tee -a versions.env
  artifacts:
    reports:
      dotenv: versions.env
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^(dev|master-\d+\.\d+)$/
      variables:
        COMMIT_OR_TARGET_BRANCH: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/
      variables:
        COMMIT_OR_TARGET_BRANCH: $CI_COMMIT_BRANCH
    - changes:
        - tf/**

build-viso-main:
  stage: trigger
  needs:
    - gather-versions
  rules:
    # check explicitly if we're a branch pipeline running for a master-*, dev
    # branch. if so, we want to build viso for that branch
    - if: $CI_COMMIT_BRANCH =~ /^(dev|master-\d+\.\d+)$/
  variables:
    TARGET_JOBS: "build-viso"
    GIT_STRATEGY: none
    GCP_FORCE_BUILD: "true"
    OVERRIDE_TF_BRANCH: $CI_COMMIT_BRANCH
  trigger:
    project: xdr/devops/orchestration
    branch: $CI_COMMIT_BRANCH
    strategy: depend

build-viso-mr:
  stage: trigger
  needs:
    - gather-versions
  allow_failure: true
  variables:
    TARGET_JOBS: "build-viso"
    GIT_STRATEGY: none
    GCP_FORCE_BUILD: "true"
    OVERRIDE_TF_BRANCH: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
  trigger:
    project: xdr/devops/orchestration
    branch: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    strategy: depend
  rules:
    # otherwise, we're an MR. if master-*, make a manual step to build the
    # image with the MR's version of gcp-configuration
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^master-\d+\.\d+$/
      when: manual
    - changes:
        - tf/**

.terratest-cwp-outposts:
  stage: trigger
  needs:
    - gather-versions
  allow_failure: true
  variables:
    UPSTREAM_COMMIT_SHA: $CI_COMMIT_SHA
    UPSTREAM_BRACH_NAME: $CI_COMMIT_REF_NAME
  trigger:
    project: xdr/cwp-devops/outposts/iac-testing-management
    branch: master
    strategy: depend

terratest-aws-outpost-cwp-agentless:
  extends: .terratest-cwp-outposts
  variables:
    CSP_NAME: "aws"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - "tf/modules/aws_outpost_cwp_agentless/**/*"
    - when: never

terratest-azure-outpost-cwp-agentless:
  extends: .terratest-cwp-outposts
  variables:
    CSP_NAME: "azure"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - "tf/modules/azure_outpost_cwp_agentless/**/*"
    - when: never


automerge:
  stage: automerge
  variables:
    AUTO_MERGE_RULE: gcp-configuration
    AUTO_MERGE_RUN_ON_REGEX: /^master-/
  extends:
    - .gitlab-ci-templates/jenkins/branch-auto-merge
  rules:
    - changes:
        - tf/**/*
